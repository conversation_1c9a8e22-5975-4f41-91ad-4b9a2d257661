import 'dart:async';
import 'package:onekitty/configs/country_specifics.dart';
import 'package:onekitty/main.dart' show isLight;
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:glassmorphism/glassmorphism.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/controllers/beneficiary_controller.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/controllers/kitty_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/models/kitty_model.dart'; 
import 'package:onekitty/screens/onboarding/passwd_req_screen.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/size_config.dart';
import 'package:onekitty/utils/utils_exports.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/views/screens/contribution_kitties/create_beneficiary.dart';

showWithdrawBottomSheet(
    BuildContext context, int kittyId, Kitty kitty, bool isFromWithDrawPage,
    {int beneficiaryId = 0, String beneficiaryTitle = ''}) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    //backgroundColor: Colors.transparent,
    builder: (_) {
      return Padding(
        padding:
            EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
        child: DraggableScrollableSheet(
          maxChildSize: 0.9,
          initialChildSize: 0.9,
          expand: false,
          builder: (context, scrollController) {
            return SingleChildScrollView(
              child: WithdrawWidget(
                kittId: kittyId,
                isFromWithDrawPage: isFromWithDrawPage,
                kitty: kitty,
                beneficiaryId: beneficiaryId,
                beneficiaryTitle: beneficiaryTitle,
              ),
            );
          },
        ),
      );
    },
  );
}

Future<Map<String, dynamic>?> showWBeneficiariesBottomSheet(
    BuildContext context,
    {required int kittyId}) async {
  return await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      //backgroundColor: Colors.transparent,
      builder: (_) {
        return DraggableScrollableSheet(
            maxChildSize: 0.97,
            initialChildSize: 0.7,
            expand: false,
            builder: (context, scrollController) {
              return SelectBeneficiary(
                kittyId: kittyId,
              );
            });
      });
}

Future<bool> showAddBeneficiaryBottomSheet(BuildContext context,
    {required int kittyId}) async {
  final result = await Navigator.of(context).push(
    MaterialPageRoute(
      fullscreenDialog: true,
      builder: (_) => CreateBeneficiary(
        kittyId: kittyId,
      ),
    ),
  );

  // Return true if a beneficiary was added
  return result == true;
}

class SelectBeneficiary extends StatelessWidget {
  final int kittyId;
  const SelectBeneficiary({super.key, required this.kittyId});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<BeneficiaryController>(initState: (_) {
      final benController = Get.find<BeneficiaryController>();
      benController.getBeneficiaries(kittyId);
    }, builder: (benController) {
      return Column(
        children: [
          SizedBox(
            height: 10.h,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Spacer(),
              Text(
                "select_beneficiary".tr,
                style: TextStyle(fontSize: 20.sp, fontWeight: FontWeight.w600),
              ),
              const Spacer(),
              IconButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  icon: const Icon(Icons.close_outlined))
            ],
          ),
          SizedBox(
            height: 15.h,
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: benController.isFetchingBeneficiaries.value
                  ? Center(
                      child: SpinKitDualRing(
                        color: ColorUtil.blueColor,
                        lineWidth: 4.sp,
                        size: 40.0.sp,
                      ),
                    )
                  : ListView.builder(
                      itemCount: benController.beneficiaries.length,
                      shrinkWrap: true,
                      itemBuilder: (context, index) {
                        final beneficiary = benController.beneficiaries[index];
                        return Column(
                          children: [
                            ListTile(
                              onTap: () {
                                Navigator.of(context).pop({
                                  'id': beneficiary.id,
                                  'title': beneficiary.accountName
                                });
                              },
                              trailing: SizedBox(
                                width: 130.w,
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    beneficiary.splitConfig == "AMOUNT"
                                        ? SizedBox(
                                            height: 40.h,
                                            child: Center(
                                              child: Text(
                                                  FormattedCurrency.getFormattedCurrency(beneficiary.amount ?? 0),
                                                  style: const TextStyle(
                                                      fontSize: 14,
                                                      fontWeight:
                                                          FontWeight.w600,
                                                      letterSpacing: 1.6)),
                                            ),
                                          )
                                        : SizedBox(
                                            width: 60.w,
                                            height: 40.h,
                                            child: Stack(
                                              alignment: Alignment.center,
                                              children: [
                                                CircularProgressIndicator(
                                                  value: beneficiary.percentage
                                                      ?.toDouble(),
                                                  strokeWidth: 4,
                                                  backgroundColor:
                                                      Colors.grey[200],
                                                  valueColor:
                                                      const AlwaysStoppedAnimation<
                                                              Color>(
                                                          AppColors.primary),
                                                ),
                                                Text(
                                                  '${((beneficiary.percentage ?? 0) * 100.0).toStringAsFixed(1)}%',
                                                  style: TextStyle(
                                                      fontSize: 8.spMin),
                                                ),
                                              ],
                                            ),
                                          ),
                                    const Icon(Icons.navigate_next),
                                  ],
                                ),
                              ),
                              subtitle: Text(
                                  '${'account_no'.tr}: ${beneficiary.accountNumber} ${beneficiary.transferMode} \n ${beneficiary.accountNumberRef}'),
                              title: Row(children: [
                                Text(beneficiary.accountName ?? ''),
                                if (beneficiary.role == 'PRIMARY')
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8.0, vertical: 2.0),
                                    decoration: BoxDecoration(
                                      border:
                                          Border.all(color: AppColors.primary),
                                      borderRadius: BorderRadius.circular(25),
                                      color: Colors.transparent,
                                    ),
                                    child: Row(
                                      children: [
                                        Icon(Icons.verified_outlined,
                                            color: AppColors.primary,
                                            size: 10.sp),
                                        const SizedBox(width: 4),
                                        Text(
                                          'primary'.tr,
                                          style: TextStyle(
                                            color: AppColors.primary,
                                            fontWeight: FontWeight.bold,
                                            fontSize: 8.spMin,
                                          ),
                                        ),
                                      ],
                                    ),
                                  )
                              ]),
                            ),
                            const Padding(
                              padding: EdgeInsets.symmetric(horizontal: 20.0),
                              child:
                                  Divider(color: Colors.grey, thickness: 1.2),
                            ),
                          ],
                        );
                      }),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: OutlinedButton.icon(
              onPressed: () async {
                // Use MaterialRoute to show the add beneficiary screen
                final added = await showAddBeneficiaryBottomSheet(context,
                    kittyId: kittyId);
                if (added) {
                  // Refresh beneficiary list only if a new one was added
                  benController.getBeneficiaries(kittyId);
                }
              },
              icon: const Icon(Icons.add),
              label: Text("add_new_beneficiary".tr),
              style: OutlinedButton.styleFrom(
                // backgroundColor: ColorUtil.blueColor,
                // foregroundColor: Colors.white,
                minimumSize: Size(double.infinity, 40.h),
              ),
            ),
          ),
          SizedBox(
            height: 25.h,
          ),
        ],
      );
    });
  }
}

// ignore: must_be_immutable
class WithdrawWidget extends StatefulWidget {
  WithdrawWidget(
      {super.key,
      required this.kittId,
      required this.kitty,
      required this.isFromWithDrawPage,
      required this.beneficiaryId,
      required this.beneficiaryTitle});
  final int kittId;
  final Kitty kitty;
  int beneficiaryId;
  String beneficiaryTitle;
  bool isFromWithDrawPage;
  @override
  State<WithdrawWidget> createState() => _WithdrawWidgetState();
}

class _WithdrawWidgetState extends State<WithdrawWidget> {
  TextEditingController amountontroller = TextEditingController();
  TextEditingController remarksController = TextEditingController();

  KittyController controller = Get.put(KittyController());
  final box = GetStorage();

  bool showConfirm = false;
  bool pinConfirmed = false;

  Future<void> selectBeneficiary() async {
    Map<String, dynamic>? selectedBeneficiary =
        await showWBeneficiariesBottomSheet(
      context,
      kittyId: widget.kittId,
    );

    if (selectedBeneficiary != null) {
      setState(() {
        widget.beneficiaryId = selectedBeneficiary['id'];
        // Reset the withdrawal process when a new beneficiary is selected
        showConfirm = false;
        amountontroller.clear();
      });

      // Load beneficiary details through a withdrawal request with empty amount
      if (!mounted) return;

      await controller.withdrawRequest(
        benficiaryId: widget.beneficiaryId,
        amount: "1", // Use a minimal amount just to get beneficiary details
        isconfirm: false,
        kittyId: widget.kittId,
        remarks: remarksController.text,
      );
    }
  }

  Future<void> addNewBeneficiary() async {
    final added =
        await showAddBeneficiaryBottomSheet(context, kittyId: widget.kittId);
    if (added) {
      // If a new beneficiary was added, let the user select one
      await selectBeneficiary();
    }
  }

  @override
  void initState() {
    setState(() {
      widget.isFromWithDrawPage = true;
      remarksController.text = widget.beneficiaryTitle;
    });
    super.initState();
  }

  Timer? _debounce;

  @override
  void dispose() {
    _debounce?.cancel();
    controller.charges.value = 0;
    controller.thirdPartyCharges.value = 0;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GlassmorphicContainer(
      height: SizeConfig.screenHeight * .8,
      width: SizeConfig.screenWidth,
      borderRadius: 2,
      blur: 17,
      padding: EdgeInsets.only(left: 10.sp, right: 10.sp, top: 10.sp),
      alignment: Alignment.center,
      border: 2,
      linearGradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: isLight.value
              ? [
                  const Color(0x0ff0ffff).withOpacity(0.7),
                  const Color(0x0ff0ffff).withOpacity(0.4),
                ]
              : [
                  const Color(0xFF2C2C2C).withOpacity(0.7),
                  const Color(0xFF1F1F1F).withOpacity(0.4),
                ],
          stops: const [
            0.45,
            1,
          ]),
      borderGradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: isLight.value
            ? [
                const Color(0xFFffffff).withOpacity(0.5),
                const Color((0xFFFFFFFF)).withOpacity(0.5),
              ]
            : [const Color(0xFF2C2C2C), const Color(0xFF1F1F1F)],
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.sp, vertical: 10.sp),
        child: Column(
          children: [
            SizedBox(
              height: 10.sp,
            ),
            Center(
              child: Container(
                height: 13,
                width: 160.spMin,
                decoration: BoxDecoration(
                    color: Theme.of(context).dividerColor,
                    borderRadius: BorderRadius.circular(11)),
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 20.sp,
                    ),
                    Center(
                      child: Text(
                        "withdraw_request".tr,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 15.sp,
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 20.sp,
                    ),
                    Container(
                      width: 350.w,
                      padding: EdgeInsets.all(15.sp),
                      decoration: BoxDecoration(
                        color: Theme.of(context).cardColor.withOpacity(0.8),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Theme.of(context).dividerColor,
                          width: 1,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Obx(() => Text(
                                '${'charges'.tr} : ${(controller.charges.value - controller.thirdPartyCharges.value).toPrecision(2)}',
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w500,
                                  color: Theme.of(context)
                                      .textTheme
                                      .bodyLarge
                                      ?.color,
                                ),
                              )),
                          Obx(() => Text(
                                '${'third_party_charges'.tr} : ${controller.thirdPartyCharges.value.toPrecision(2)}',
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w500,
                                  color: Theme.of(context)
                                      .textTheme
                                      .bodyLarge
                                      ?.color,
                                ),
                              )),
                          const Divider(),
                          Obx(() => Text(
                                '${'total_charges'.tr} : ${controller.charges.value.toPrecision(2)}',
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w500,
                                  color: Theme.of(context)
                                      .textTheme
                                      .bodyLarge
                                      ?.color,
                                ),
                              )),
                        ],
                      ),
                    ),
                    const SizedBox(height: 20),
                     Text(
                      "enter_amount".tr,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(
                      height: 6.sp,
                    ),
                    Obx(
                      () => CustomTextFieldText(
                        controller: amountontroller,
                        paddingHorizontal: 20.spMin,
                        showNoKeyboard: false,
                        readOnly: showConfirm,
                        isRequired: true,
                        maxLength: 7,
                        hintText: "amount".tr,
                        labelText: "enter_amount".tr,
                        suffixIcon: controller.isFetchingBalance.value
                            ? const CupertinoActivityIndicator()
                            : Obx(
                                () => Text.rich(
                                  TextSpan(
                                    text: '${'balance'.tr}: ',
                                    style: TextStyle(
                                        color: controller.balance.value > 0
                                            ? null
                                            : Colors.red,
                                        fontSize: 12.sp),
                                    children: [
                                      TextSpan(
                                          text: amountontroller.text.isEmpty
                                              ? "${widget.kitty.balance?.toDouble().toPrecision(2)}"
                                              : "${controller.balance.value.toPrecision(2)}",
                                          style: TextStyle(
                                            color: controller.balance.value > 0
                                                ? null
                                                : Colors.red,
                                          )),
                                    ],
                                  ),
                                ),
                              ),
                        onChanged: (val) {
                          if (_debounce?.isActive ?? false) {
                            _debounce!.cancel();
                          }

                          _debounce = Timer(
                            const Duration(seconds: 1),
                            () {
                              controller.fetchBalance(
                                double.parse(val.isEmpty ? "0" : val),
                                data: {
                                  "amount":
                                      double.parse(val.isEmpty ? "0" : val),
                                  "channel": int.tryParse(
                                      widget.kitty.beneficiaryChannel ?? ''),
                                  "kitty_id": widget.kittId,
                                  "settlement_type":
                                      widget.kitty.settlementType,
                                },
                              );
                            },
                          );
                        },
                        validator: (value) {
                          if (value!.isEmpty) {
                            return null;
                          }
                          final amt = int.tryParse(value);
                          if (amt == null) {
                            return "enter_valid_amount".tr;
                          } else {
                            return null;
                          }
                        },
                      ),
                    ),
                    SizedBox(
                      height: 10.sp,
                    ),
                    Obx(
                      () => Visibility(
                        visible: showConfirm,
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 20.sp),
                          child: Container(
                            width: double.infinity,
                            padding: EdgeInsets.symmetric(
                                horizontal: 20.sp, vertical: 10.sp),
                            decoration: BoxDecoration(
                                color: Theme.of(context)
                                    .scaffoldBackgroundColor
                                    .withOpacity(0.8),
                                borderRadius: BorderRadius.circular(15.sp)),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                 Text(
                                  "receiver_details".tr,
                                  style: const TextStyle(fontWeight: FontWeight.bold),
                                ),
                                SizedBox(
                                  height: 8.sp,
                                ),
                                Text(
                                    "${"account".tr.toUpperCase()}: ${controller.beneficiaryNumber}"),
                                Text("${"name".tr.toUpperCase()}: ${controller.beneficiaryName}"),
                                Text("${"channel".tr.toUpperCase()}: ${controller.reciveChannel}"),
                                Text(
                                    "${"amount".tr.toUpperCase()}: ${CountryConfig.getCurrencyCode} ${controller.reciveAmount.string}"),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 6.sp,
                    ),
                     Text(
                      "transaction_description".tr,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(
                      height: 6.sp,
                    ),
                    CustomTextFieldText(
                      controller: remarksController,
                      paddingHorizontal: 10.spMin,
                      showNoKeyboard: false,
                      readOnly: showConfirm,
                      isRequired: false,
                      maxLines: 2,
                      hintText: "enter_remarks_optional".tr,
                      labelText: "remarks".tr,
                      suffixIcon:
                          !showConfirm && remarksController.text.isNotEmpty
                              ? IconButton(
                                  icon: const Icon(Icons.clear),
                                  onPressed: () {
                                    remarksController.clear();
                                  },
                                )
                              : null,
                    ),
                    SizedBox(
                      height: 20.sp,
                    ),
                  ],
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(bottom: 10.sp, top: 10.sp),
              child: Obx(
                () => Center(
                  child: showConfirm
                      ? CustomKtButton(
                          isLoading: controller.loadingWithdraw.isTrue,
                          onPress: () async {
                            box.write("amountbox", amountontroller.text);
                            box.write("remarks", remarksController.text);
                            var isAuthenticated = await Get.to(
                                () => AuthPasswdScreen(),
                                arguments: [false]);
                            if (isAuthenticated != null &&
                                isAuthenticated == true) {
                              await controller.withdrawRequest(
                                benficiaryId: widget.beneficiaryId,
                                amount: amountontroller.text.trim(),
                                isconfirm: true,
                                kittyId: widget.kittId,
                                remarks: remarksController.text,
                              );
                              
                              if (mounted) {
                                Get.back();
                                if (controller.status.isTrue) {
                                  Get.offAndToNamed(NavRoutes.withdrawSuccessPage);
                                } else {
                                  Get.offAndToNamed(NavRoutes.withdrawErrorPage);
                                  ToastUtils.showErrorToast(
                                      context,
                                      controller.apiMessage.string,
                                      "error".tr);
                                }
                              }
                            } else {
                              Logger().w("user_not_authenticated".tr);
                            }
                          },
                          width: 150.sp,
                          btnText: "confirm".tr,
                        )
                      : CustomKtButton(
                          isLoading: controller.loadingWithdraw.isTrue,
                          onPress: () async {
                            await controller.withdrawRequest(
                              benficiaryId: widget.beneficiaryId,
                              amount: amountontroller.text.trim(),
                              isconfirm: false,
                              kittyId: widget.kittId,
                              remarks: remarksController.text,
                            );
                            if (controller.status.isTrue) {
                              setState(() {
                                showConfirm = true;
                              });
                            } else {
                              if (!mounted) {
                                return;
                              }
                              ToastUtils.showErrorToast(context,
                                  controller.apiMessage.string, "error".tr);
                            }
                          },
                          width: 300.sp,
                          btnText: "submit".tr,
                        ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
