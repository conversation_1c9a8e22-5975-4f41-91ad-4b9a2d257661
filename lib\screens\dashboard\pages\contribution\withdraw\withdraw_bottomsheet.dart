import 'dart:async'; 
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:get_storage_pro/get_storage_pro.dart'; 
import 'package:logger/logger.dart';
import 'package:onekitty/controllers/beneficiary_controller.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/models/user_kitties_model.dart';
import 'package:onekitty/screens/onboarding/passwd_req_screen.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart'; 
import 'package:onekitty/utils/size_config.dart';
import 'package:onekitty/utils/utils_exports.dart';

showWithdrawBottomSheet(
    BuildContext context, int kittyId, Kitty kitty, bool isFromWithDrawPage,
    {required int beneficiaryId}) {
  FocusScope.of(context).unfocus();
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    //backgroundColor: Colors.transparent,
    builder: (_) {
      return DraggableScrollableSheet(
        maxChildSize: 0.97,
        initialChildSize: 0.7,
        expand: false,
        builder: (context, scrollController) {
          return WithdrawWidget(
            kittId: kittyId,
            isFromWithDrawPage: isFromWithDrawPage,
            kitty: kitty,
            beneficiaryId: beneficiaryId,
          );
        },
      );
    },
  );
}

Future<int?> showWBeneficiariesBottomSheet(BuildContext context,
    {required int kittyId}) async {
  FocusScope.of(context).unfocus();
  return await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      //backgroundColor: Colors.transparent,
      builder: (_) {
        return DraggableScrollableSheet(
            maxChildSize: 0.97,
            initialChildSize: 0.7,
            expand: false,
            builder: (context, scrollController) {
              return SelectBeneficiary(
                kittyId: kittyId,
              );
            });
      });
}

class SelectBeneficiary extends StatelessWidget {
  final int kittyId;
  const SelectBeneficiary({super.key, required this.kittyId});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<BeneficiaryController>(initState: (_) {
      final benController = Get.find<BeneficiaryController>();
      benController.getBeneficiaries(kittyId);
    }, builder: (benController) {
      return Column(
        children: [
          SizedBox(
            height: 10.h,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Spacer(),
              Text(
                "Select Beneficiary",
                style: TextStyle(fontSize: 20.sp, fontWeight: FontWeight.w600),
              ),
              const Spacer(),
              IconButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  icon: const Icon(Icons.close_outlined))
            ],
          ),
          SizedBox(
            height: 15.h,
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: benController.isFetchingBeneficiaries.value
                  ? Center(
                      child: SpinKitDualRing(
                        color: ColorUtil.blueColor,
                        lineWidth: 4.sp,
                        size: 40.0.sp,
                      ),
                    )
                  : SingleChildScrollView(
                      child: ListView.builder(
                          itemCount: benController.beneficiaries.length,
                          shrinkWrap: true,
                          itemBuilder: (context, index) {
                            final beneficiary =
                                benController.beneficiaries[index];
                            return Column(
                              children: [
                                ListTile(
                                  onTap: () {
                                    Navigator.of(context).pop(beneficiary.id);
                                  },
                                  trailing: SizedBox(
                                    width: 130.w,
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        beneficiary.splitConfig == "AMOUNT"
                                            ? SizedBox(
                                                height: 40.h,
                                                child: Center(
                                                  child: Text(
                                                      "${FormattedCurrency().getFormattedCurrency(beneficiary.amount ?? 0)}",
                                                      style: const TextStyle(
                                                          fontSize: 14,
                                                          fontWeight:
                                                              FontWeight.w600,
                                                          letterSpacing: 1.6)),
                                                ),
                                              )
                                            : SizedBox(
                                                width: 60.w,
                                                height: 40.h,
                                                child: Stack(
                                                  alignment: Alignment.center,
                                                  children: [
                                                    CircularProgressIndicator(
                                                      value: beneficiary
                                                          .percentage
                                                          ?.toDouble(),
                                                      strokeWidth: 4,
                                                      backgroundColor:
                                                          Colors.grey[200],
                                                      valueColor:
                                                          const AlwaysStoppedAnimation<
                                                                  Color>(
                                                              AppColors
                                                                  .primary),
                                                    ),
                                                    Text(
                                                      '${((beneficiary.percentage ?? 0) * 100.0).toStringAsFixed(1)}%',
                                                      style: TextStyle(
                                                          fontSize: 8.spMin),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                        const Icon(Icons.navigate_next),
                                      ],
                                    ),
                                  ),
                                  subtitle: Text(
                                      'AccNo: ${beneficiary.accountNumber} ${beneficiary.transferMode} \n ${beneficiary.accountNumberRef}'),
                                  title: Row(children: [
                                    Text(beneficiary.accountName ?? ''),
                                    if (beneficiary.role == 'PRIMARY')
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 8.0, vertical: 2.0),
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                              color: AppColors.primary),
                                          borderRadius:
                                              BorderRadius.circular(25),
                                          color: Colors.transparent,
                                        ),
                                        child: Row(
                                          children: [
                                            Icon(Icons.verified_outlined,
                                                color: AppColors.primary,
                                                size: 10.sp),
                                            const SizedBox(width: 4),
                                            Text(
                                              'Primary',
                                              style: TextStyle(
                                                color: AppColors.primary,
                                                fontWeight: FontWeight.bold,
                                                fontSize: 8.spMin,
                                              ),
                                            ),
                                          ],
                                        ),
                                      )
                                  ]),
                                ),
                                const Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 20.0),
                                  child: Divider(
                                      color: Colors.grey, thickness: 1.2),
                                ),
                              ],
                            );
                          }),
                    ),
            ),
          ),
        ],
      );
    });
  }
}

// ignore: must_be_immutable
class WithdrawWidget extends StatefulWidget {
  WithdrawWidget(
      {super.key,
      required this.kittId,
      required this.kitty,
      required this.isFromWithDrawPage,
      required this.beneficiaryId});
  final int kittId;
  final Kitty kitty;
  final int beneficiaryId;
  bool isFromWithDrawPage;
  @override
  State<WithdrawWidget> createState() => _WithdrawWidgetState();
}

class _WithdrawWidgetState extends State<WithdrawWidget> {
  TextEditingController amountontroller = TextEditingController();

  KittyController controller = Get.put(KittyController());
  final box = GetStorage();

  bool showConfirm = false;
  bool pinConfirmed = false;
  @override
  void initState() {
    setState(() {
      widget.isFromWithDrawPage = true;
    });
    super.initState();
  }

  Timer? _debounce;

  @override
  void dispose() {
    _debounce?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    controller.balance(0.0);
    return SizedBox(
      height: SizeConfig.screenHeight * .8,
      width: SizeConfig.screenWidth,
        
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20.sp, vertical: 10.sp),
        child: Column(
          children: [
            SizedBox(
              height: 10.sp,
            ),
            Center(
              child: Container(
                height: 13,
                width: 160.spMin,
                decoration: BoxDecoration(
                    color: Theme.of(context).dividerColor,
                    borderRadius: BorderRadius.circular(11)),
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 20.sp,
                    ),
                    Center(
                      child: Text(
                        "Withdraw Request.",
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 15.sp,
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 20.sp,
                    ),
                    const Text(
                      "Enter amount",
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(
                      height: 6.sp,
                    ),
                    Obx(
                      () => CustomTextField(
                        controller: amountontroller,
                        paddingHorizontal: 20.spMin,
                        showNoKeyboard: true,
                        readOnly: showConfirm,
                        isRequired: true,
                        maxLength: 7,
                        hintText: "Amount",
                        labelText: "Enter amount",
                        suffixIcon: controller.isFetchingBalance.value
                            ? const CupertinoActivityIndicator()
                            : Obx(
                                () => Text.rich(
                                  TextSpan(
                                    text: 'Balance: ',
                                    style: TextStyle(
                                        color: controller.balance.value > 0
                                            ? null
                                            : Colors.red,
                                        fontSize: 12.sp), // Default size
                                    children: [
                                      TextSpan(
                                          text: amountontroller.text.isEmpty
                                              ? "${widget.kitty.balance}"
                                              : "${controller.balance.value.toPrecision(2)}", // Smaller size
                                          style: TextStyle(
                                            color: controller.balance.value > 0
                                                ? null
                                                : Colors.red,
                                          )),
                                    ],
                                  ),
                                ),
                              ),
                        onChanged: (val) {
                          if (_debounce?.isActive ?? false) {
                            _debounce!.cancel();
                          }

                          _debounce = Timer(
                            const Duration(seconds: 1),
                            () {
                              controller.fetchBalance(
                                double.parse(val.isEmpty ? "0" : val),
                                data: {
                                  "amount":
                                      double.parse(val.isEmpty ? "0" : val),
                                  "channel": int.tryParse(
                                      widget.kitty.beneficiaryChannel ?? ''),
                                  "kitty_id": widget.kittId,
                                  "settlement_type":
                                      widget.kitty.settlementType,
                                },
                              );
                            },
                          );
                        },
                        validator: (value) {
                          if (value!.isEmpty) {
                            return null;
                          }
                          final amt = int.tryParse(value);
                          if (amt == null) {
                            return "Enter valid amount";
                          } else {
                            return null;
                          }
                        },
                      ),
                    ),
                    SizedBox(
                      height: 10.sp,
                    ),
                    Obx(
                      () => Visibility(
                        visible: showConfirm,
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 20.sp),
                          child: Container(
                            width: double.infinity,
                            padding: EdgeInsets.symmetric(
                                horizontal: 20.sp, vertical: 10.sp),
                            decoration: BoxDecoration(
                                color: Theme.of(context)
                                    .scaffoldBackgroundColor
                                    .withOpacity(0.8),
                                borderRadius: BorderRadius.circular(15.sp)),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  "Receiver Details",
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                                SizedBox(
                                  height: 8.sp,
                                ),
                                Text(
                                    "ACCOUNT: ${controller.beneficiaryNumber}"),
                                Text("NAME: ${controller.beneficiaryName}"),
                                Text("CHANNEL: ${controller.reciveChannel}"),
                                Text(
                                    "AMOUNT: KSH ${controller.reciveAmount.string}")
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                    Visibility(
                      visible: showConfirm,
                      child: SizedBox(
                        height: 20.sp,
                      ),
                    ),
                    Obx(
                      () => Center(
                        child: showConfirm
                            ? CustomKtButton(
                                isLoading: controller.loadingWithdraw.isTrue,
                                onPress: () async {
                                  box.write("amountbox", amountontroller.text);
                                  var isAuthenticated = await Get.to(
                                      () => const  AuthPasswdScreen(),
                                      arguments: [false]);
                                  if (isAuthenticated != null &&
                                      isAuthenticated == true) {
                                    await controller.withdrawRequest(
                                      benficiaryId: widget.beneficiaryId,
                                      amount: amountontroller.text.trim(),
                                      isconfirm: true,
                                      kittyId: widget.kittId,
                                    );
                                    Get.back();
                                    controller.status.isTrue
                                        ? Get.offAndToNamed(
                                            NavRoutes.withdrawSuccessPage)
                                        : Get.offAndToNamed(
                                                NavRoutes.withdrawErrorPage)
                                            ?.then(
                                            (value) =>
                                                ToastUtils.showErrorToast(
                                                    context,
                                                    controller
                                                        .apiMessage.string,
                                                    "Error"),
                                          );
                                  } else {
                                    Logger().w("user is not authenticated");
                                  }
                                },
                                width: 110.sp,
                                btnText: "Confirm",
                              )
                            : CustomKtButton(
                                isLoading: controller.loadingWithdraw.isTrue,
                                onPress: () async {
                                  await controller.withdrawRequest(
                                    benficiaryId: widget.beneficiaryId,
                                    amount: amountontroller.text.trim(),
                                    isconfirm: false,
                                    kittyId: widget.kittId,
                                    // benficiaryId: 0,
                                  );
                                  if (controller.status.isTrue) {
                                    setState(() {
                                      showConfirm = true;
                                    });
                                  } else {
                                    if (!mounted) {
                                      return;
                                    }
                                    ToastUtils.showErrorToast(context,
                                        controller.apiMessage.string, "Error");
                                  }
                                },
                                width: 110.sp,
                                btnText: "Submit",
                              ),
                      ),
                    ),
                    SizedBox(
                      height: 15.sp,
                    )
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
