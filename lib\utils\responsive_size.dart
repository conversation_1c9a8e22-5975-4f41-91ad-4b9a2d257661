import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';

class ResponsiveSize {
  static double getAdaptiveSize(BuildContext context, double size) {
    double baseWidth = 375;
    double screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = screenWidth / baseWidth;
    if (kIsWeb || Platform.isWindows || Platform.isMacOS || Platform.isLinux) {
      scaleFactor = scaleFactor.clamp(0.8, 1.2);
    }
    return size * scaleFactor;
  }

  static double width(BuildContext context, double size) => getAdaptiveSize(context, size);
  static double height(BuildContext context, double size) => getAdaptiveSize(context, size);
  static double fontSize(BuildContext context, double size) => getAdaptiveSize(context, size);
  static double radius(BuildContext context, double size) => getAdaptiveSize(context, size);
}
