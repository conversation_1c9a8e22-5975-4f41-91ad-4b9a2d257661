import 'dart:io';

import 'package:accordion/accordion.dart';
import 'package:accordion/controllers.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:get_storage_pro/get_storage_pro.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/models/chama/signatory_approval.dart';
import 'package:onekitty/screens/onboarding/passwd_req_screen.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/cache_keys.dart';
import 'package:onekitty/utils/size_config.dart';

import '../../../../../utils/utils_exports.dart';

class SignatoryTransactions extends StatefulWidget {
  static const headerStyle = TextStyle(
      color: Color(0xffffffff), fontSize: 18, fontWeight: FontWeight.bold);
  static const contentStyleHeader = TextStyle(
      color: Color(0xff999999), fontSize: 14, fontWeight: FontWeight.w700);
  static const contentStyle = TextStyle(
      color: Color(0xff999999), fontSize: 14, fontWeight: FontWeight.normal);
  const SignatoryTransactions({super.key});

  @override
  State<SignatoryTransactions> createState() => _SignatoryTransactionsState();
}

class _SignatoryTransactionsState extends State<SignatoryTransactions> {
  final ChamaController chamaController = Get.put(ChamaController());
  final ChamaDataController chamaDataController =
      Get.put(ChamaDataController());

  TextEditingController commentController = TextEditingController();
  final formKey = GlobalKey<FormState>();
  String deviceId = "";
  String deviceModel = "";
  final box = GetStorage();
  @override
  void dispose() {
    super.dispose();
    commentController.dispose();
  }

  @override
  void initState() {
    super.initState();
    commentController.clear();
    getDeviceInfo();
  }

  void getDeviceInfo() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      setState(() {
        deviceId = androidInfo.id;
        deviceModel = androidInfo.model;
      });
      print('Running on ${androidInfo.id} ${androidInfo.model}');
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      print(
          'Running on ${iosInfo.utsname.machine} ${iosInfo.identifierForVendor} ${iosInfo.model}');
      setState(() {
        deviceId = iosInfo.identifierForVendor!;
        deviceModel = iosInfo.model;
      });
    } else {
      WebBrowserInfo webBrowserInfo = await deviceInfo.webBrowserInfo;
      print('Running on ${webBrowserInfo.userAgent}');
      setState(() {
        deviceId = webBrowserInfo.userAgent!;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        //appBar: buildAppBar(context),
        body: SafeArea(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 12),
        child: Column(
          children: [
            const RowAppBar(),
            Text(
              "Signatory Approval Transactions",
              style: context.titleText,
            ),
            const SizedBox(
              height: 5,
            ),
            const Text(
              "These are the transactions awaiting your approval",
              textAlign: TextAlign.center,
            ),
            const SizedBox(
              height: 5,
            ),
            Expanded(
              child: GetX(
                  init: ChamaController(),
                  initState: (state) {
                    Future.delayed(Duration.zero, () async {
                      try {
                        await state.controller?.getSigTransactions(
                            chamaId:
                                chamaDataController.chama.value.chama?.id ?? 0);
                      } catch (e) {
                        rethrow;
                      }
                    });
                  },
                  builder: (ChamaController chamaController) {
                    if (chamaController.isGetSigTraLoading.isTrue) {
                      return SizedBox(
                        height: SizeConfig.screenHeight * .33,
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SpinKitDualRing(
                                color: ColorUtil.blueColor,
                                lineWidth: 4.sp,
                                size: 40.0.sp,
                              ),
                              const Text(
                                "loading..",
                                style: TextStyle(
                                  color: Colors.white,
                                ),
                              )
                            ],
                          ),
                        ),
                      );
                    } else if (chamaController.sigTransactions.isEmpty) {
                      return Center(
                        child: Column(
                          children: [
                            Image.asset(
                              AssetUrl.notFound,
                              height: 130.h,
                            ),
                            const Text("No transactions added to this chama"),
                            const SizedBox(
                              height: 10,
                            ),
                          ],
                        ),
                      );
                    } else if (chamaController.sigTransactions.isNotEmpty) {
                      return ListView.separated(
                          shrinkWrap: true,
                          physics: const BouncingScrollPhysics(),
                          separatorBuilder: (context, index) {
                            return const Divider();
                          },
                          itemCount: chamaController.sigTransactions.length,
                          itemBuilder: (context, index) {
                            final sigTransaction =
                                chamaController.sigTransactions[index];
                            return SingleChildScrollView(
                              child: Accordion(
                                  headerBorderColor: Colors.blueGrey,
                                  headerBorderColorOpened: Colors.transparent,
                                  // headerBorderWidth: 1,
                                  headerBackgroundColorOpened: Colors.green,
                                  contentBackgroundColor: Colors.white,
                                  contentBorderColor: Colors.green,
                                  contentBorderWidth: 3,
                                  contentHorizontalPadding: 20,
                                  scaleWhenAnimating: true,
                                  openAndCloseAnimation: true,
                                  headerPadding: const EdgeInsets.symmetric(
                                      vertical: 7, horizontal: 15),
                                  sectionOpeningHapticFeedback:
                                      SectionHapticFeedback.heavy,
                                  sectionClosingHapticFeedback:
                                      SectionHapticFeedback.light,
                                  children: [
                                    AccordionSection(
                                      contentVerticalPadding: 8,
                                      leftIcon: const Icon(
                                          Icons.text_fields_rounded,
                                          color: Colors.white),
                                      header: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            sigTransaction.reason ,
                                            style: SignatoryTransactions
                                                .headerStyle,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                          Text(
                                            FormattedCurrency()
                                                .getFormattedCurrency(
                                                    sigTransaction.amount),
                                            style: SignatoryTransactions
                                                .contentStyle
                                                .copyWith(color: Colors.white),
                                          ),
                                          Text(
                                            sigTransaction.status ,
                                            style: SignatoryTransactions
                                                .contentStyle
                                                .copyWith(color: Colors.white),
                                          )
                                        ],
                                      ),
                                      content: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            sigTransaction.reason ,
                                            style: context.titleText
                                                ?.copyWith(fontSize: 14),
                                          ),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                "ACCOUNT:",
                                                style: context.dividerTextLarge,
                                              ),
                                              Text(
                                                  "${sigTransaction.receiverAccount } ${sigTransaction.receiverAccountRef }"),
                                            ],
                                          ),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                "TRANSFER MODE:",
                                                style: context.dividerTextLarge,
                                              ),
                                              Text(
                                                  sigTransaction.transferMode 
                                                    )
                                            ],
                                          ),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                "DATE:",
                                                style: context.dividerTextLarge,
                                              ),
                                              Text(DateFormat(
                                                      "MM/dd/yyyy hh:mm a")
                                                  .format(DateTime.parse(
                                                          sigTransaction
                                                              .initiatedAt!
                                                              .toIso8601String())
                                                      .toLocal()))
                                            ],
                                          ),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                "AMOUNT:",
                                                style: context.dividerTextLarge,
                                              ),
                                              Text(
                                                FormattedCurrency()
                                                    .getFormattedCurrency(
                                                        sigTransaction.amount),
                                                style: context.dividerTextLarge
                                                    ?.copyWith(
                                                        color: Colors.green),
                                              ),
                                            ],
                                          ),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                "INITIATED BY:",
                                                style: context.dividerTextLarge,
                                              ),
                                              Text(
                                                  "${sigTransaction.initator?.firstName} ${sigTransaction.initator?.secondName}"),
                                            ],
                                          ),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                "STATUS:",
                                                style: context.dividerTextLarge,
                                              ),
                                              Text("${sigTransaction.status}"),
                                            ],
                                          ),
                                          SizedBox(
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                OutlinedButton(
                                                    style: OutlinedButton
                                                        .styleFrom(
                                                      side: BorderSide(
                                                          color: Colors
                                                              .red.shade900),
                                                    ),
                                                    onPressed: () {
                                                      showApproveDialog(
                                                          index, false);
                                                    },
                                                    child: Text(
                                                      "DECLINE",
                                                      style: TextStyle(
                                                          color: Colors
                                                              .red.shade900),
                                                    )),
                                                ElevatedButton(
                                                    onPressed: () {
                                                      showApproveDialog(
                                                          index, true);
                                                    },
                                                    child:
                                                        const Text("APPROVE"))
                                              ],
                                            ),
                                          )
                                        ],
                                      ),
                                    )
                                  ]),
                            );
                            // return ListTile(
                            //   onTap: () {
                            //     Get.to(SignatoryApproval(
                            //         transacton: sigTransaction));
                            //   },
                            //   title: Text(sigTransaction.reason ),
                            //   subtitle: Text("${sigTransaction.amount}"),
                            // );
                          });
                    }
                    return Center(
                      child: Column(
                        children: [
                          Image.asset(
                            AssetUrl.notFound,
                            height: 130.h,
                          ),
                          const Text("No transactions added to this chama"),
                          const SizedBox(
                            height: 10,
                          ),
                        ],
                      ),
                    );
                  }),
            ),
          ],
        ),
      ),
    ));
  }

  showApproveDialog(index, bool isApprove) {
    showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            title: Text(
              isApprove
                  ? "Kindly fill out the field to complete this transaction"
                  : "Confirm to decline this transaction",
              style: context.titleText,
            ),
            content: Visibility(
              visible: isApprove,
              child: Form(
                key: formKey,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CustomTextField(
                      controller: commentController,
                      labelText: "Comment",
                      isRequired: true,
                      validator: (p0) {
                        if (p0!.isEmpty) {
                          return "This field can't be empty";
                        }
                        return null;
                      },
                    )
                  ],
                ),
              ),
            ),
            actions: [
              isApprove
                  ? const SizedBox()
                  : OutlinedButton(
                      onPressed: () async {
                        await approveTransaction(index, false);
                        Navigator.pop(context);
                      },
                      child: const Text("OK")),
              isApprove
                  ? Obx(() => CustomKtButton(
                      width: 120.w,
                      isLoading:
                          chamaController.isSignatoryApproveLoading.isTrue,
                      onPress: () async {
                        await approveTransaction(index, true);
                      },
                      btnText: "Approve"))
                  : const SizedBox()
            ],
          );
        });
  }

  approveTransaction(index, bool isApprove) async {
    //if (formKey.currentState!.validate()) {
    final tra = chamaController.sigTransactions[index];
    var isAuthenticated =
        await Get.to(() => const AuthPasswdScreen(), arguments: [false]);

    SignatoryApprovalModel request = SignatoryApprovalModel(
        chamaId: chamaDataController.chama.value.chama?.id,
        memberId: chamaDataController.chama.value.member?.id,
        isApproved: isApprove ? true : false,
        comment: isApprove ? commentController.text.trim() : "Decline",
        transactionId: tra.id,
        latitude: box.read(CacheKeys.lat),
        longitude: box.read(CacheKeys.long),
        deviceId: deviceId,
        deviceModel: deviceModel);
    if (isAuthenticated != null && isAuthenticated == true) {
      bool res = await chamaController.signatoryApproval(request: request);
      if (res) {
        if (!mounted) return;
        Snack.show(res, chamaController.apiMessage.string);
        setState(() {
          chamaController.sigTransactions.removeAt(index);
          Navigator.pop(context);
        });
      } else {
        if (!mounted) return;
        Snack.show(res, chamaController.apiMessage.string);
      }
    } else {
      ToastUtils.showInfoToast(
          context, "You need to authenticate to make this operation", "Oops");
    }
    //}
  }
}
