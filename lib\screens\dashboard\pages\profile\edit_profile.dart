import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage_pro/get_storage_pro.dart';
import 'package:onekitty/controllers/user_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';
import 'package:onekitty/models/auth/profile_request.dart';
import 'package:onekitty/models/auth/user_model.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/widgets/date_picker.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/cache_keys.dart';
import 'package:onekitty/utils/counties.dart';
import 'package:onekitty/utils/utils_exports.dart';

class EditProfilePage extends StatefulWidget {
  const EditProfilePage({super.key});

  @override
  State<EditProfilePage> createState() => _EditProfilePageState();
}

class _EditProfilePageState extends State<EditProfilePage> {
  final UserKittyController userController = Get.put(UserKittyController());
  UserModelLatest? get user => userController.getLocalUser();
  TextEditingController nameCtr = TextEditingController();
  TextEditingController emailCtr = TextEditingController();
  TextEditingController dateCtrl = TextEditingController();
  TextEditingController timeCtrl = TextEditingController();
  final UserController userCtrl = Get.put(UserController());
  String? county;
  final box = GetStorage();
  setValues() {
    nameCtr.text = "${user?.firstName} ${user?.secondName}";
    emailCtr.text = user?.email ?? "";
    county = user?.county;
    dateCtrl.text = user?.birthDate ?? "";
  }

  late List<DropdownMenuItem<String>> countyList;

  @override
  void initState() {
    super.initState();
    setValues();
    countyList = counties.map((String county) {
      return DropdownMenuItem<String>(
        value: county,
        child: Text(county),
      );
    }).toList();
  }

  void dropdownCallback(String? selectedValue) {
    if (selectedValue is String) {
      setState(() {
        county = selectedValue;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      //appBar: buildAppBar(context),
      body: Container(
        margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const RowAppBar(),
              Align(
                alignment: Alignment.center,
                child: Text(
                  "Update Profile",
                  style:
                      context.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
              ),
              const SizedBox(
                height: 15,
              ),
              customText("Name", context),
              CustomTextField(
                controller: nameCtr,
                labelText: "Enter Name",
              ),
              customText("Email Address", context),
              CustomTextField(
                controller: emailCtr,
                labelText: "Enter Email address",
              ),
              customText("Date of birth", context),
              DateOnlyPicker(dateController: dateCtrl, isAllow: false),
              const SizedBox(
                height: 10,
              ),
              customText("County", context),
              Container(
                  decoration: BoxDecoration(
                      border: Border.all(color: AppColors.blueButtonColor),
                      borderRadius: BorderRadius.circular(12)),
                  child: ListTile(
                    title: Text(county ?? "Select county"),
                    trailing: DropdownButton(
                        underline: const SizedBox(),
                        items: countyList,
                        onChanged: dropdownCallback),
                  )),

              // CountyPicker(
              //   county: county ?? "",
              // ),
              const SizedBox(
                height: 12,
              ),
              Obx(() => CustomKtButton(
                  isLoading: userCtrl.isUpdateProfile.isTrue,
                  onPress: () async {
                    await updateProfile(
                        dateCtrl: dateCtrl,
                        emailCtr: emailCtr,
                        nameCtr: nameCtr,
                        county: county);
                  },
                  btnText: "Save Changes"))
            ],
          ),
        ),
      ),
    );
  }
}

Future<bool> updateProfile({
  String? photoUrl,
  TextEditingController? dateCtrl,
  TextEditingController? emailCtr,
  TextEditingController? nameCtr,
  String? county,
}) async {
  try {
    final UserKittyController userController = Get.put(UserKittyController());
    final UserController userCtrl = Get.put(UserController());
    final box = GetStorage();
    userController.getLocalUser();

    String fullName = nameCtr?.text.trim() ?? "";
    String? firstName;
    String? lastName;
    List<String> names = fullName.split(RegExp(r'\s+'));
    if (names.isNotEmpty) {
      firstName = names[0];
      if (names.length > 1) {
        lastName = names[names.length - 1];
      }
    }
    ProfileRequest request = ProfileRequest(
      id: userController.getLocalUser()!.id,
      firstName: firstName ?? userController.getLocalUser()!.firstName,
      secondName: lastName ?? userController.getLocalUser()!.secondName,
      email: emailCtr == null
          ? userController.user.value.email
          : emailCtr.text.trim(),
      birthDate: dateCtrl == null
          ? DateTime.tryParse(userController.getLocalUser()!.birthDate!)
          : DateTime.tryParse(dateCtrl.text.trim()),
      county: county ?? userController.getLocalUser()!.county,
      latitude: box.read(CacheKeys.lat),
      longitude: box.read(CacheKeys.long),
      profileUrl: (photoUrl == "" || photoUrl == null)
          ? userController.getLocalUser()!.profileUrl
          : photoUrl,
      phoneNumber: userController.getLocalUser()!.phoneNumber,
    );
    bool res = await userCtrl.updateProfile(request: request);
    if (res) {
      Snack.show(res, userCtrl.apiMessage.string);
      userController.getUser();
      Get.offAndToNamed(NavRoutes.profile);
      return true;
    } else {
      Snack.show(res, userCtrl.apiMessage.string);
      return false;
    }
  } catch (e) {
    return false;
  }
}

// class CountyPicker extends StatefulWidget {
//   final String county;
//   const CountyPicker({super.key, required this.county});

//   @override
//   _CountyPickerState createState() => _CountyPickerState();
// }

// class _CountyPickerState extends State<CountyPicker> {
//   String countryValue = "";
//   String stateValue = "";
//   String cityValue = "";
//   String address = "";

//   @override
//   void initState() {
//     super.initState();
//     stateValue = widget.county;
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Center(
//       child: Container(
//           //: EdgeInsets.symmetric(horizontal: 20),
//           child: Column(
//         children: [
//           Row(
//             children: [
//               ///Adding CSC Picker Widget in app
//               Expanded(
//                 child: CSCPicker(
//                   ///Enable disable state dropdown [OPTIONAL PARAMETER]
//                   showStates: true,

//                   /// Enable disable city drop down [OPTIONAL PARAMETER]
//                   showCities: false,

//                   ///Enable (get flag with country name) / Disable (Disable flag) / ShowInDropdownOnly (display flag in dropdown only) [OPTIONAL PARAMETER]
//                   flagState: CountryFlag.DISABLE,

//                   ///Dropdown box decoration to style your dropdown selector [OPTIONAL PARAMETER] (USE with disabledDropdownDecoration)
//                   dropdownDecoration: BoxDecoration(
//                       borderRadius: BorderRadius.all(Radius.circular(10)),
//                       color: Colors.blueAccent.withOpacity(0.1),
//                       border:
//                           Border.all(color: Colors.grey.shade300, width: 1)),

//                   ///Disabled Dropdown box decoration to style your dropdown selector [OPTIONAL PARAMETER]  (USE with disabled dropdownDecoration)
//                   disabledDropdownDecoration: BoxDecoration(
//                       borderRadius: BorderRadius.all(Radius.circular(10)),
//                       color: Colors.grey.shade300,
//                       border:
//                           Border.all(color: Colors.grey.shade300, width: 1)),

//                   ///placeholders for dropdown search field
//                   countrySearchPlaceholder: "Country",
//                   stateSearchPlaceholder: "County",
//                   citySearchPlaceholder: "City",

//                   ///labels for dropdown
//                   countryDropdownLabel: "Country",
//                   stateDropdownLabel: "County",
//                   cityDropdownLabel: "City",

//                   ///Default Country
//                   ///defaultCountry: CscCountry.India,

//                   ///Country Filter [OPTIONAL PARAMETER]
//                   //countryFilter: [CscCountry.India,CscCountry.United_States,CscCountry.Canada],

//                   ///Disable country dropdown (Note: use it with default country)
//                   //disableCountry: true,

//                   ///selected item style [OPTIONAL PARAMETER]
//                   selectedItemStyle: TextStyle(
//                     color: Colors.black,
//                     fontSize: 14,
//                   ),

//                   ///DropdownDialog Heading style [OPTIONAL PARAMETER]
//                   dropdownHeadingStyle: TextStyle(
//                       color: Colors.black,
//                       fontSize: 17,
//                       fontWeight: FontWeight.bold),

//                   ///DropdownDialog Item style [OPTIONAL PARAMETER]
//                   dropdownItemStyle: TextStyle(
//                     color: Colors.black,
//                     fontSize: 14,
//                   ),

//                   ///Dialog box radius [OPTIONAL PARAMETER]
//                   dropdownDialogRadius: 10.0,

//                   ///Search bar radius [OPTIONAL PARAMETER]
//                   searchBarRadius: 10.0,

//                   ///triggers once country selected in dropdown
//                   onCountryChanged: (value) {
//                     setState(() {
//                       ///store value in country variable
//                       countryValue = value;
//                     });
//                   },

//                   ///triggers once state selected in dropdown
//                   onStateChanged: (value) {
//                     setState(() {
//                       ///store value in state variable
//                       if (value != null) {
//                         stateValue = value;
//                         address = "$stateValue, $countryValue";

//                         print(address);
//                       }
//                       stateValue = widget.county;
//                       print("THE COUNTY IS: $stateValue");
//                     });
//                   },

//                   ///triggers once city selected in dropdown
//                   onCityChanged: (value) {
//                     setState(() {
//                       ///store value in city variable
//                       if (value != null) {
//                         cityValue = value;
//                       }
//                     });
//                   },

//                   ///Show only specific countries using country filter
//                   // countryFilter: ["United States", "Canada", "Mexico"],
//                 ),
//               ),

//               ///print newly selected country state and city in Text Widget
//               // TextButton(
//               //     onPressed: () {
//               //       setState(() {
//               //         address = "$cityValue, $stateValue, $countryValue";
//               //       });
//               //     },
//               //     child: Text("Print Data")),
//             ],
//           ),
//           Text(address)
//         ],
//       )),
//     );
//   }
// }
