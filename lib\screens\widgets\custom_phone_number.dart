// // ignore_for_file: must_be_immutable

// import 'package:country_pickers/country.dart';
// import 'package:country_pickers/country_picker_dialog.dart';
// import 'package:country_pickers/utils/utils.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:onekitty/helpers/colors.dart';
// import 'package:onekitty/helpers/extensions/text_styles.dart';
// import 'package:onekitty/screens/widgets/text_form_field.dart';

// class CustomPhoneNumber extends StatelessWidget {
//   CustomPhoneNumber({
//     super.key,
//     required this.country,
//     required this.onTap,
//     required this.controller,
//   });

//   Country country;

//   Function(Country) onTap;

//   TextEditingController controller;

//   @override
//   Widget build(BuildContext context) {
//     return Row(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       children: [
//         InkWell(
//           onTap: () {
//             _openCountryPicker(context);
//           },
//           child: Container(
//             height: 65,
//             decoration: BoxDecoration(

//               //color: AppColors.greyTextColor,
//               //color: appTheme.whiteA700,
//               borderRadius: BorderRadius.circular(
//                 8.h,
//               ),
//               border: Border.all(
//                 color: AppColors.slate,
//                 //color: appTheme.indigo500,
//                 width: 1.w,
//               ),
//             ),
//             child: Row(
//               children: [
//                 Container(
//                   height: 20.h,
//                   width: 25.w,
//                   margin: EdgeInsets.only(
//                     left: 8.w,
//                     top: 10.h,
//                     bottom: 10.h,
//                   ),
//                   child: ClipRRect(
//                     borderRadius: BorderRadius.circular(
//                       4.h,
//                     ),
//                     child: CountryPickerUtils.getDefaultFlagImage(
//                       country,
//                     ),
//                   ),
//                 ),
//                 Padding(
//                   padding: EdgeInsets.only(
//                     left: 8.w,
//                     top: 15.h,
//                     bottom: 16.h,
//                   ),
//                   child: Text(
//                     "+${country.phoneCode}",
//                     style: context.dividerTextSmall,
//                     //style: CustomTextStyles.bodyLargeInterBluegray200,
//                   ),
//                 ),
//                 IconButton(onPressed: (){}, icon: Icon(Icons.keyboard_arrow_down_rounded))
//                 // CustomImageView(
//                 //   color: Colors.black,
//                 //   imagePath: AssetUrl.arrowDown,
//                 //   height: 16.h,
//                 //   width: 16.w,
//                 //   margin: EdgeInsets.fromLTRB(11.w, 18.h, 16.w, 18.h),
//                 // ),
//               ],
//             ),
//           ),
//         ),
//         Expanded(
//           child: CustomTextField(
            
//             controller: controller,
//             // paddingHorizontal: 4.spMin,
//             maxLength: 13,
//             showNoKeyboard: true,
//             hintText: "0701114555",
//             labelText: "Phone Number",
//             isRequired: true,
//             validator: (value) {
//               RegExp regex = RegExp(r'[a-zA-Z]');
          
//               if (value!.isEmpty) {
//                 return "Phone Number is required";
//               } else if (value.length < 9) {
//                 return "Invalid phone Number";
//               } else if (regex.hasMatch(value)) {
//                 return "Phone number can not contain Alphabets";
//               } else {
//                 return null;
//               }
//             },
//           ),
//         ),
//       ],
//     );
//   }

//   Widget _buildDialogItem(Country country) => Row(
//         children: <Widget>[
//           CountryPickerUtils.getDefaultFlagImage(country),
//           Container(
//             margin: EdgeInsets.only(
//               left: 10.h,
//             ),
//             width: 60.h,
//             child: Text(
//               "+${country.phoneCode}",
//               style: const TextStyle(fontSize: 14),
//             ),
//           ),
//           const SizedBox(width: 8.0),
//           Flexible(
//             child: Text(
//               country.name,
//               style: const TextStyle(fontSize: 14),
//             ),
//           ),
//         ],
//       );

//   void _openCountryPicker(BuildContext context) => showDialog(
//         context: context,
//         builder: (context) => CountryPickerDialog(
//           searchInputDecoration: const InputDecoration(
//             hintText: 'Search...',
//             hintStyle: TextStyle(fontSize: 14),
//           ),
//           isSearchable: true,
//           title: const Text('Select your phone code',
//               style: TextStyle(fontSize: 14)),
//           onValuePicked: (Country country) => onTap(country),
//           itemBuilder: _buildDialogItem,
//         ),
//       );
// }