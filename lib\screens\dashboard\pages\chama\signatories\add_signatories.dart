import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';
import 'package:onekitty/models/chama/add_signatory_request.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/size_config.dart';

import '../../../../../utils/utils_exports.dart';

class AddSignatories extends StatefulWidget {
  const AddSignatories({super.key});

  @override
  State<AddSignatories> createState() => _AddSignatoriesState();
}

class _AddSignatoriesState extends State<AddSignatories> {
  TextEditingController whatsappNumberController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController searchController = TextEditingController();
  String? _dropdownValue;
  final chamaDataController = Get.put(ChamaDataController());
  final chamaController = Get.put(ChamaController());
  PhoneNumber num = PhoneNumber(isoCode: 'KE');
  String myPhone = "";
  String? phoneNumber;
  String? name;
  int? memberID;
  final formKey = GlobalKey<FormState>();
  List filteredMembers = [];

  @override
  void initState() {
    filteredMembers = chamaController.chamaMembers;
    searchController.clear();
    super.initState();
  }

  @override
  void dispose() {
    whatsappNumberController.dispose();
    emailController.dispose();
    searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 12),
          child: Form(
            key: formKey,
            child: SingleChildScrollView(
              child: Column(
                children: [
                  Text("Select Signatories",
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold, fontSize: 22)),
                  SizedBox(
                    height: 5.h,
                  ),
                  const Text(
                    "Add signatories to the chama group",
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(
                    height: 15.h,
                  ),
                  Container(
                    decoration: BoxDecoration(
                        border: Border.all(color: AppColors.blueButtonColor),
                        borderRadius: BorderRadius.circular(12)),
                    child: ListTile(
                      onTap: () {
                        showBottomAddSheet();
                      },
                      title: Text(
                          "${name ?? "Select contact"}\n${phoneNumber ?? ""}"),
                      trailing: IconButton(
                          onPressed: () {
                            showBottomAddSheet();
                          },
                          icon: const Icon(Icons.arrow_drop_down_rounded)),
                    ),
                  ),
                  const SizedBox(
                    height: 15,
                  ),
                  Container(
                    decoration: BoxDecoration(
                        border: Border.all(color: AppColors.blueButtonColor),
                        borderRadius: BorderRadius.circular(12)),
                    child: ListTile(
                      title:
                          Text(_dropdownValue ?? "Select a notification type"),
                      trailing: DropdownButton(
                          underline: const SizedBox(),
                          items: [
                            const DropdownMenuItem(
                              value: "SMS",
                              child: Text("SMS"),
                            ),
                            const DropdownMenuItem(
                              value: "WHATSAPP",
                              child: Text("WHATSAPP"),
                            ),
                            const DropdownMenuItem(
                              value: "EMAIL",
                              child: Text("EMAIL"),
                            ),
                            const DropdownMenuItem(
                              value: "ALL",
                              child: Text("ALL"),
                            ),
                          ],
                          //value: _dropdownValue,
                          onChanged: dropdownCallback),
                    ),
                  ),
                  const SizedBox(
                    height: 12,
                  ),
                  if (_dropdownValue == "EMAIL" || _dropdownValue == "ALL")
                    CustomTextField(
                      controller: emailController,
                      hintText: "Enter email",
                      labelText: "Email",
                    ),
                  if (_dropdownValue == "WHATSAPP" || _dropdownValue == "ALL")
                    InternationalPhoneNumberInput(
                      hintText: "Whatsapp Number",
                      onInputChanged: (num) {
                        setState(() {
                          myPhone = num.phoneNumber!;
                        });
                      },
                      onInputValidated: (bool value) {
                        print(value);
                      },
                      selectorConfig: const SelectorConfig(
                        selectorType: PhoneInputSelectorType.BOTTOM_SHEET,
                        useBottomSheetSafeArea: true,
                      ),
                      ignoreBlank: false,
                      autoValidateMode: AutovalidateMode.disabled,
                      selectorTextStyle: const TextStyle(color: Colors.black),
                      initialValue: num,
                      textFieldController: whatsappNumberController,
                      formatInput: true,
                      keyboardType: const TextInputType.numberWithOptions(
                          signed: true, decimal: true),
                      inputBorder: const OutlineInputBorder(),
                      onSaved: (PhoneNumber number) {
                        print('On Saved: $number');
                      },
                    ),
                  SizedBox(
                    height: 12.h,
                  ),
                  Obx(
                    () => CustomKtButton(
                        isLoading: chamaController.isSignatoryLoading.isTrue,
                        onPress: () async {
                          await addSignatory().whenComplete(() {
                            Get.back(result: true);
                          });
                        },
                        btnText: "Add Signatory"),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  void dropdownCallback(String? selectedValue) {
    if (selectedValue is String) {
      setState(() {
        _dropdownValue = selectedValue;
      });
    }
  }

  void selectedContact(String? selectedName, String? selectedPhone) {
    if (selectedName is String) {
      setState(() {
        name = selectedName;
        phoneNumber = selectedPhone;
      });
    }
  }

  showBottomAddSheet() {
    showModalBottomSheet(
        isScrollControlled: true,
        context: context,
        builder: (context) {
          return DraggableScrollableSheet(
              maxChildSize: 0.97,
              initialChildSize: 0.7,
              expand: false,
              builder: (context, scrollController) {
                return Container(
                  decoration:
                      BoxDecoration(borderRadius: BorderRadius.circular(20)),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                  child: Column(
                    children: [
                      TextFormField(
                        controller: searchController,
                        decoration: InputDecoration(
                            hintText: "Search members",
                            suffixIcon: IconButton(
                                onPressed: () {},
                                icon: const Icon(Icons.search))),
                        onChanged: (value) {
                          filteredMembers = chamaController.chamaMembers
                              .where((memeber) => memeber.firstName!
                                  .toLowerCase()
                                  .contains(value.toLowerCase()))
                              .toList();
                          setState(() {});
                        },
                      ),
                      Expanded(
                        child: GetX(
                            init: ChamaController(),
                            initState: (state) {
                              Future.delayed(Duration.zero, () async {
                                try {
                                  await state.controller?.getChamaMembers(
                                    chamaId: chamaDataController
                                        .chama.value.chama?.id,
                                  );
                                  chamaController.reset();
                                } catch (e) {
                                  throw e;
                                }
                              });
                            },
                            builder: (ChamaController chamaController) {
                              if (chamaController.isloadingChama.isTrue) {
                                return SizedBox(
                                  height: SizeConfig.screenHeight * .33,
                                  child: Center(
                                    child: Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        SpinKitDualRing(
                                          color: ColorUtil.blueColor,
                                          lineWidth: 4.sp,
                                          size: 40.0.sp,
                                        ),
                                        const Text(
                                          "loading..",
                                          style: TextStyle(
                                            color: Colors.white,
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                                );
                              } else if (chamaController.chamaMembers.isEmpty) {
                                return const Text("No members added yet.");
                              } else if (filteredMembers.isEmpty) {
                                return Padding(
                                  padding: EdgeInsets.only(top: 15.h),
                                  child: const Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Text("Member not found"),
                                    ],
                                  ),
                                );
                              } else if (filteredMembers.isNotEmpty) {
                                return ListView.separated(
                                    controller: chamaController.controller,
                                    itemBuilder: (context, index) {
                                      final member = filteredMembers[index];
                                      final fullName =
                                          "${member.firstName} ${member.secondName}";
                                      final phone = "${member.phoneNumber}";
                                      final memberId = member.id;
                                      return ListTile(
                                        onTap: () {
                                          selectedContact(fullName, phone);
                                          memberID = memberId;
                                          Navigator.pop(context);
                                        },
                                        title: Text(fullName),
                                        subtitle: Text(phone),
                                        trailing: ElevatedButton.icon(
                                            onPressed: () {
                                              selectedContact(fullName, phone);
                                              memberID = memberId;
                                              Navigator.pop(context);
                                            },
                                            icon: const Icon(Icons.add),
                                            label: const Text("Add")),
                                      );
                                    },
                                    separatorBuilder: (context, index) {
                                      return const Divider();
                                    },
                                    itemCount: filteredMembers.length);
                              }
                              return const Text("No members added yet.");
                            }),
                      ),
                    ],
                  ),
                );
              });
        });
  }

  addSignatory() async {
    if (formKey.currentState!.validate()) {
      SignatoryRequest request = SignatoryRequest(
        chamaId: chamaDataController.chama.value.chama?.id,
        memberId: memberID,
        phoneNumber: phoneNumber,
        notificationType: _dropdownValue,
        email: emailController.text.trim(),
        whatsAppNumber: myPhone != "" ? myPhone.substring(1) : null,
      );
      bool res = await chamaController.addSignatory(request: request);
      if (res) {
        if (!mounted) return;
        Snack.show(res, chamaController.apiMessage.string);
        Get.offAndToNamed(NavRoutes.signatories);
      } else {
        if (!mounted) return;
        Snack.show(res, chamaController.apiMessage.string);
      }
    }
  }
}
