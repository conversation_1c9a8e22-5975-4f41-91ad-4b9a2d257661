import 'package:date_time_format/date_time_format.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/models/user_kitties_model.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/main.dart' as main;
import '../../../../../../utils/utils_exports.dart';

// ignore: must_be_immutable
class ContributionKittyWidget extends StatelessWidget {
  final UserKitty kitty;

  const ContributionKittyWidget({
    super.key,
    required this.kitty,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        viewKittyDetails(context);
      },
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 7.w,
          vertical: 2.h,
        ),
        decoration: AppDecoration.shadow1Themed.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder6,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: EdgeInsets.only(right: 4.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 7.h),
                      child: Text(
                        kitty.kitty?.title ?? "",
                        overflow: TextOverflow.ellipsis,
                        style: CustomTextStyles.labelMediumff545963,
                      ),
                    ),
                  ),
                  Text(
                    FormattedCurrency()
                        .getFormattedCurrency(kitty.kitty?.balance),
                    //FormattedCurrency.,
                    style: TextStyle(
                      fontSize: 20.h,
                      fontWeight: FontWeight.w900,
                      color: main.isLight.value ? appTheme.gray900 : appTheme.whiteA700,
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.only(right: 4.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: AppDecoration.outlineIndigo.copyWith(
                      borderRadius: BorderRadiusStyle.circleBorder16,
                    ),
                    child: Text(
                      kitty.kittyType ?? "",
                      style: CustomTextStyles.titleSmallyellow,
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(left: 10.w),
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: AppDecoration.outlineIndigo.copyWith(
                      borderRadius: BorderRadiusStyle.circleBorder16,
                    ),
                    child: Text(
                      kitty.kittyStatus ?? "",
                      style: TextStyle(
                        color: getkittyStatusColor(kitty.kittyStatus ?? ""),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const Spacer(),
                  Padding(
                    padding: EdgeInsets.only(bottom: 3.h),
                    child: Text(
                      "Balance",
                      style: CustomTextStyles.bodySmallGray900,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 10.h),
            Padding(
              padding: EdgeInsets.only(right: 4.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CustomImageView(
                            imagePath: AssetUrl.imgCalendar,
                            height: 24.h,
                            width: 24.w,
                          ),
                          Padding(
                            padding: EdgeInsets.only(
                              left: 4.w,
                              top: 2.h,
                              bottom: 3.h,
                            ),
                            child: RichText(
                              text: TextSpan(
                                children: [
                                  TextSpan(
                                    text: "Created: ",
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontStyle: FontStyle.italic,
                                      color: main.isLight.value ? appTheme.gray90001 : appTheme.whiteA70001,
                                    ),
                                  ),
                                  TextSpan(
                                    text: DateFormat('MMM dd, yyyy').format(
                                        kitty.kitty?.createdAt?.toLocal() ??
                                            DateTime.now()),
                                    style: CustomTextStyles.bodySmallGray900,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 4.h),
                      Row(
                        children: [
                          CustomImageView(
                            imagePath: AssetUrl.imgClock,
                            height: 24.h,
                            width: 24.w,
                          ),
                          Padding(
                              padding: EdgeInsets.only(
                                left: 4.w,
                                top: 4.h,
                              ),
                              child: RichText(
                                text: TextSpan(
                                  children: [
                                    TextSpan(
                                      text: DateTimeFormat.relative(
                                          kitty.kitty?.endDate ??
                                              DateTime.now(),
                                          levelOfPrecision: 1,
                                          prependIfBefore: 'Ends In',
                                          ifNow: "Now",
                                          appendIfAfter: 'ago'),
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: main.isLight.value ? appTheme.gray90001 : appTheme.whiteA70001,
                                      ),
                                    ),
                                  ],
                                ),
                              )),
                        ],
                      ),
                    ],
                  ),
                  _buildViewButton1(context),
                ],
              ),
            ),
            SizedBox(height: 3.h),
          ],
        ),
      ),
    );
  }

  viewKittyDetails(context) async {
    var dataController = Get.put(DataController());
    dataController.kitty.value = kitty;

    Navigator.pushNamed(
      context,
      NavRoutes.viewingsinglekittyScreen,
    );
  }

  /// Section Widget
  Widget _buildViewButton1(BuildContext context) {
    return CustomOutlinedButton(
      onPressed: () {
        viewKittyDetails(context);
      },
      width: 68.w,
      height: 20.h,
      text: "View",
      margin: EdgeInsets.fromLTRB(1.w, 1.h, 1.w, 1.h),
    );
  }
}

Color getkittyStatusColor(String status) {
  switch (status.toLowerCase()) {
    case "active":
      return const Color(0xFF56AF57);
    case "completed":
      return const Color(0xFF56AF57);
    case "settlement initiated":
      return const Color.fromARGB(255, 206, 104, 192);

    default:
      return const Color(0xFFEE5B60);
  }
}
