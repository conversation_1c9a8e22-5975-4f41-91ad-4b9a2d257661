import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/events/events_controller.dart';
import 'package:onekitty/helpers/connectivity_wrapper.dart';
import 'package:onekitty/screens/dashboard/pages/events/create_event/create_event_page.dart';
import 'package:onekitty/utils/app_bar/custom_app_bar.dart';
import 'package:onekitty/utils/event_cards.dart';
import 'package:onekitty/utils/my_button.dart';
import 'package:onekitty/utils/my_event_card.dart';
import 'package:onekitty/utils/search_widget.dart';
import 'package:shimmer/shimmer.dart';

class EventsPage extends StatefulWidget {
  const EventsPage({super.key});

  @override
  State<EventsPage> createState() => _EventsPageState();
}

class _EventsPageState extends State<EventsPage> {
  final ValueNotifier<int> _eventTabNumber = ValueNotifier(1);
  final _controller = Get.put(Eventcontroller());
  final ScrollController scrollController = ScrollController();
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _controller.fetchEvents();
    _controller.fetchUserEvents();
    scrollController.addListener(_scrollListener);
  }

  Future<void> _scrollListener() async {
    if (_isLoadingMore) return;

    if (scrollController.position.pixels >=
        scrollController.position.maxScrollExtent * 0.8) {
      _isLoadingMore = true;
      try {
        if (_eventTabNumber.value == 0) {
          _controller.fetchEvents();
        } else {
          _controller.fetchUserEvents();
        }
      } finally {
        _isLoadingMore = false;
      }
    }
  }

  @override
  void dispose() {
    scrollController.removeListener(_scrollListener);
    scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ConnectivityCheck(
      child: Scaffold(
        body: RefreshIndicator(
          onRefresh: () async {
            if (_eventTabNumber.value == 0) {
              _controller.resetEvents();
              _controller.fetchEvents();
            } else {
              _controller.resetUserEvents();
              _controller.fetchUserEvents();
            }
          },
          child: CustomScrollView(
            controller: scrollController,
            slivers: [
              SliverAppBar(
                  backgroundColor: Theme.of(context).scaffoldBackgroundColor,
                  pinned: true,
                  expandedHeight: 200.h,
                  flexibleSpace: FlexibleSpaceBar(
                    background: Padding(
                      padding: EdgeInsets.all(8.0.spMin),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text('Events',
                              style: TextStyle(
                                  fontWeight: FontWeight.w700,
                                  fontSize: 20.spMin)),
                          MyButton(
                            width: 205.w,
                            onClick: () {
                              Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          const CreateEventPage()));
                            },
                            label: 'Create Event',
                            icon: Icons.add,
                          )
                        ],
                      ),
                    ),
                  ),
                  bottom: PreferredSize(
                    preferredSize: const Size(double.infinity, 60),
                    child: ValueListenableBuilder(
                        valueListenable: _eventTabNumber,
                        builder: (context, eventTabNumber, _) {
                          return SearchBarWidget(page: eventTabNumber);
                        }),
                  ),
                  title: buildAppBarWithImage(context)),
              SliverToBoxAdapter(
                child: SizedBox(
                  child: Center(
                    child: ValueListenableBuilder(
                        valueListenable: _eventTabNumber,
                        builder: (context, eventTabNumber, _) {
                          return Row(
                            children: [
                              TabButtonWidget(
                                  onTap: () => _eventTabNumber.value = 1,
                                  label: 'My Events',
                                  active: eventTabNumber == 1),
                              TabButtonWidget(
                                label: 'Other Events',
                                active: eventTabNumber == 0,
                                onTap: () => _eventTabNumber.value = 0,
                              ),
                            ],
                          );
                        }),
                  ),
                ),
              ),
              ValueListenableBuilder(
                valueListenable: _eventTabNumber,
                builder: (context, eventTabNumber, _) {
                  return SliverToBoxAdapter(
                      child: eventTabNumber == 0
                          ? Obx(() {
                              return ListView.builder(
                                physics: const NeverScrollableScrollPhysics(),
                                shrinkWrap: true,
                                itemCount: _controller.events.length + 1,
                                itemBuilder: (context, index) {
                                  if (index == _controller.events.length) {
                                    return _buildLoadingIndicator();
                                  }
                                  return EventCards(
                                      event: _controller.events[index]);
                                },
                              );
                            })
                          : Obx(() {
                              return ListView.builder(
                                physics: const NeverScrollableScrollPhysics(),
                                shrinkWrap: true,
                                itemCount: _controller.userEvents.length + 1,
                                itemBuilder: (context, index) {
                                  if (index == _controller.userEvents.length) {
                                    return _buildLoadingIndicator();
                                  }
                                  return MyEventCards(
                                    eventModel: _controller.userEvents[index],
                                    organizer: true,
                                  );
                                },
                              );
                            }));
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return Obx(() {
      if (_controller.isLoading.value &&
          (_controller.page.value <= _controller.maxPage.value ||
              _controller.userpage.value <= _controller.maxUserPage.value)) {
        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Center(
            child: EventShimmer(itemHeight: 250.h),
          ),
        );
      }
      return const SizedBox.shrink();
    });
  }
}

class EventShimmer extends StatelessWidget {
  final double? itemHeight;
  const EventShimmer({super.key, this.itemHeight});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: ListView.builder(
          shrinkWrap: true,
          itemCount: 3,
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (_, __) {
            if (__ == 2) {
              return const CupertinoActivityIndicator();
            }
            return Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: Container(
                height: itemHeight ?? 350.h,
                color: Colors.white,
              ),
            );
          }),
    );
  }
}
