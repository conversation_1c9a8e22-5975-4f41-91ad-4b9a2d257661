import 'dart:async';

import 'package:date_time_format/date_time_format.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/cardPayment.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/confirm_payment_screen.dart';
import 'package:onekitty/screens/widgets/payment_radio.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/common_strings.dart';
import 'package:onekitty/utils/size_config.dart';

import '../../../../utils/utils_exports.dart';

// ignore_for_file: must_be_immutable
class ContributingToAKitty extends StatefulWidget {
  const ContributingToAKitty({super.key});

  @override
  State<ContributingToAKitty> createState() => _ContributingToAKittyState();
}

class _ContributingToAKittyState extends State<ContributingToAKitty> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  TextEditingController nameEditTextController = TextEditingController();
  TextEditingController amountEditTextController = TextEditingController();
  TextEditingController phoneNumberController = TextEditingController();
  TextEditingController emailTextController = TextEditingController();
  ContributeController contributeController = Get.find();
  late Timer _timer;
  DateTime tagetDate = DateTime.now().add(const Duration(days: 1));

  @override
  void initState() {
    super.initState();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {});
    });
  }

  bool isNameChecked = false;
  bool isNumberChecked = false;

  String? selectedChannel = "M-Pesa";

  final GlobalKey _tooltipKey1 = GlobalKey();
  final GlobalKey _tooltipKey2 = GlobalKey();

  @override
  void dispose() {
    _timer.cancel(); // Cancel the timer to prevent memory leaks
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      //appBar: _buildAppBar(context),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: EdgeInsets.only(top: 10.0.h),
          child: Container(
            margin: EdgeInsets.only(bottom: 5.h),
            padding: EdgeInsets.symmetric(horizontal: 30.w),
            child: Stack(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const RowAppBar(),
                    Align(
                      alignment: Alignment.center,
                      child: Text(
                        contributeController.kittGoten.value.title ?? "",
                        style: theme.textTheme.titleLarge,
                        textAlign: TextAlign.center,
                      ),
                    ),
                    SizedBox(height: 16.h),
                    Container(
                      width: 343.w,
                      margin: EdgeInsets.only(left: 1.w, right: 25.w),
                      child: RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: extractText(contributeController
                                      .kittGoten.value.description ??
                                  ""),
                              style: CustomTextStyles.bodySmallff545963,
                            ),
                          ],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    SizedBox(height: 16.h),
                    _buildKittyDesignFrame(context),
                    SizedBox(height: 17.h),
                    Padding(
                        padding: EdgeInsets.only(left: 1.w),
                        child: Text("Name (optional)",
                            style: CustomTextStyles.titleSmallGray90001)),
                    SizedBox(height: 3.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        _buildNameEditText(context),
                        Padding(
                          padding: EdgeInsets.only(left: 1.w, top: 5.h),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Padding(
                                    padding: EdgeInsets.only(left: 6.w),
                                    child: Text(
                                      "Hide name",
                                      style:
                                          CustomTextStyles.bodySmallGray900_1,
                                    ),
                                  ),
                                  GestureDetector(
                                    onTap: () {
                                      final dynamic tooltip =
                                          _tooltipKey1.currentState;
                                      tooltip.ensureTooltipVisible();
                                    },
                                    child: Tooltip(
                                      key: _tooltipKey1,
                                      message: KtStrings.hideNames,
                                      child: CustomImageView(
                                        imagePath: AssetUrl.imgInbox,
                                        height: 12.h,
                                        width: 12.w,
                                        margin: EdgeInsets.only(
                                            left: 3.w, top: 2.h, bottom: 3.h),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              Checkbox(
                                value: isNameChecked,
                                onChanged: (value) {
                                  setState(() {
                                    isNameChecked = value!;
                                  });
                                },
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 0.h),
                    SizedBox(height: 8.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        _buildPhoneFrame(context),
                        Padding(
                          padding: EdgeInsets.only(left: 1.w, top: 20.h),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Padding(
                                    padding: EdgeInsets.only(left: 6.w),
                                    child: Text(
                                      "Hide number",
                                      style:
                                          CustomTextStyles.bodySmallGray900_1,
                                    ),
                                  ),
                                  GestureDetector(
                                    onTap: () {
                                      final dynamic tooltip =
                                          _tooltipKey2.currentState;
                                      tooltip.ensureTooltipVisible();
                                    },
                                    child: Tooltip(
                                      key: _tooltipKey2,
                                      message: KtStrings.hideNumber,
                                      child: CustomImageView(
                                        imagePath: AssetUrl.imgInbox,
                                        height: 12.h,
                                        width: 12.w,
                                        margin: EdgeInsets.only(
                                            left: 3.w, top: 2.h, bottom: 3.h),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              Checkbox(
                                value: isNumberChecked,
                                onChanged: (value) {
                                  setState(() {
                                    isNumberChecked = value!;
                                  });
                                },
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 16.h),
                    Padding(
                        padding: EdgeInsets.only(left: 1.w),
                        child: Text("Amount to contribute",
                            style: CustomTextStyles.titleSmallGray90001)),
                    SizedBox(height: 3.h),
                    _buildAmountEditText(context),
                    SizedBox(height: 18.h),
                    Padding(
                        padding: EdgeInsets.only(left: 1.w),
                        child: Text("Choose Payment Channel",
                            style: CustomTextStyles.titleSmallGray90001)),
                    SizedBox(height: 10.h),
                    ContributeChannelsBuilder(
                        selectedChannel: selectedChannel ?? "",
                        onChange: (String? newlySelectedChannel) {
                          setState(() {
                            selectedChannel = newlySelectedChannel;
                          });
                        }),
                    SizedBox(height: 18.h),
                    if (selectedChannel == 'Visa') // show email field
                      _buildAEmailField(context),
                    SizedBox(height: 14.h),
                    _buildMakeContributionButton(context),
                    SizedBox(
                      height: 30.h,
                    )
                  ],
                ),
                Visibility(
                  visible: contributeController.confirmpayLoading.value,
                  child: Positioned.fill(
                    child: Container(
                      color: Colors.black
                          .withOpacity(0.5), // Semi-transparent background
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SpinKitDualRing(
                              color: Theme.of(context).primaryColor,
                              size: 70.0,
                            ),
                            const SizedBox(height: 20),
                            const Text(
                              'Processing Payment.... Please wait',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Section Widget
  Widget _buildKittyDesignFrame(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(left: 1.w, right: 3.w),
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 14.h),
      decoration: AppDecoration.shadow1
          .copyWith(borderRadius: BorderRadiusStyle.roundedBorder6),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(top: 4.h, bottom: 1.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomImageView(
                        imagePath: AssetUrl.imgCalendar,
                        height: 24.h,
                        width: 24.w),
                    Padding(
                      padding:
                          EdgeInsets.only(left: 4.w, top: 2.h, bottom: 3.h),
                      child: RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: "Created on: ",
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: appTheme.gray90001,
                              ),
                            ),
                            TextSpan(
                              text: DateFormat('MMM dd, yyyy').format(
                                  contributeController
                                          .kittGoten.value.createdAt ??
                                      DateTime.now()),
                              style: CustomTextStyles.bodySmallGray900,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Container(
                  padding: const EdgeInsets.only(
                    left: 50,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.white),
                    borderRadius: BorderRadius.circular(5),
                  ),
                  child: Text(
                    contributeController.kittStatus.value,
                    style: TextStyle(
                      color: Colors.green,
                      fontSize: 12.h,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 4.h),
          Row(children: [
            CustomImageView(
                imagePath: AssetUrl.total, height: 24.h, width: 24.w),
            Padding(
              padding: EdgeInsets.only(left: 4.w, top: 4.h),
              child: RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: "Volumes: ${contributeController.volumes}",
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: appTheme.gray90001,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ]),
          SizedBox(height: 4.h),
          Row(children: [
            CustomImageView(
                imagePath: AssetUrl.imgClock, height: 24.h, width: 24.w),
            Padding(
              padding: EdgeInsets.only(left: 4.w, top: 4.h),
              child: RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                      text: DateTimeFormat.relative(
                        contributeController.kittGoten.value.endDate ??
                            DateTime.now(),
                        levelOfPrecision: 2,
                        prependIfBefore: 'Ends In',
                        ifNow: "Now",
                        appendIfAfter: 'ago',
                      ),
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: appTheme.gray90001,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ]),
          SizedBox(height: 4.h)
        ],
      ),
    );
  }

  /// Section Widget
  Widget _buildNameEditText(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 1.w),
      child: CustomTextField(
        width: SizeConfig.screenWidth * .55,
        controller: nameEditTextController,
        paddingHorizontal: 2.spMin,
        hintText: "Full names",
        labelText: "full Names",
        validator: (value) {
          return null;
        },
      ),
    );
  }

  /// Section Widget
  Widget _buildPhoneFrame(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 1.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
              padding: EdgeInsets.only(left: 4.w),
              child: Text("Phone number",
                  style: CustomTextStyles.titleSmallGray900)),
          SizedBox(height: 2.h),
          SizedBox(
            width: SizeConfig.screenWidth * .55,
            child: CustomTextField(
              controller: phoneNumberController,
              paddingHorizontal: 2.spMin,
              maxLength: 13,
              showNoKeyboard: true,
              inputFormatters: <TextInputFormatter>[
                FilteringTextInputFormatter.allow(RegExp("[0-9]")),
              ],
              hintText: "phone number",
              labelText: "Mobile number * ",
              validator: (value) {
                if (value!.isEmpty) {
                  return "Enter your Mobile Number";
                } else if (value.length < 9) {
                  return "Invalid Mobile Number";
                } else {
                  return null;
                }
              },
            ),
          )
        ],
      ),
    );
  }

  /// Section Widget
  Widget _buildAmountEditText(BuildContext context) {
    return Padding(
        padding: EdgeInsets.only(left: 1.w),
        child: CustomTextField(
          controller: amountEditTextController,
          maxLength: 6,
          paddingHorizontal: 2.spMin,
          showNoKeyboard: true,
          inputFormatters: <TextInputFormatter>[
            FilteringTextInputFormatter.allow(RegExp("[0-9]")),
          ],
          hintText: "Amount",
          labelText: "Enter amount(KSH) * ",
          validator: (value) {
            if (value!.isEmpty) {
              return "Enter amount";
            }
            final amt = int.tryParse(value);
            if (amt == null) {
              return "Enter valid amount";
            } else {
              return null;
            }
          },
        ));
  }

  Widget _buildAEmailField(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 1.w),
      child: CustomTextField(
        controller: emailTextController,
        paddingHorizontal: 2.spMin,
        hintText: "Email",
        labelText: "Email",
        validator: (value) {
          return null;
        },
      ),
    );
  }

  /// Section Widget
  Widget _buildMakeContributionButton(BuildContext context) {
    return Obx(
      () => Column(
        children: [
          CustomKtButton(
              isLoading: contributeController.isContributeloading.isTrue,
              onPress: () async {
                if (_formKey.currentState!.validate()) {
                  String fullName = nameEditTextController.text.trim();
                  String? firstName;
                  String? lastName;
                  List<String> names = fullName.split(' ');
                  if (names.length == 1) {
                    firstName = names[0];
                  } else if (names.length >= 2) {
                    firstName = names[0];
                    // last name should be from zero index onwards
                    lastName = names.sublist(1).join(' ');
                  }
                  //-----------GET CHANNEL--------
                  final chan = KittyController().getNetworkCode(
                    networkTitle: selectedChannel ?? "",
                  );

                  //----------TRANSACT-----------

                  final kitty = contributeController.kittGoten.value;
                  bool res = await contributeController.contribute(
                    phoneNumber: phoneNumberController.text,
                    firstName: firstName,
                    secondName: lastName,
                    amount: int.parse(amountEditTextController.text.trim()),
                    channel: chan!,
                    kittyId: kitty.id!,
                    shownames: !isNameChecked,
                    shownumber: !isNumberChecked,
                    email: emailTextController.text,
                  );

                  if (!mounted) return;
                  //----------NAVIGATE USER----------
                  if (res) {
                    if (chan == 0) {
                      ToastUtils.showSuccessToast(
                        context,
                        contributeController.apiMessageContri.string,
                        "success",
                      );
                      Get.off(() =>
                          const ProcessPaymentOtp(isChamaContribute: false));
                    }
                    if (chan == 63902) {
                      ToastUtils.showSuccessToast(
                        context,
                        contributeController.apiMessageContri.string,
                        "success",
                      );

                      try {
                        contributeController.confirmpayLoading.value = true;
                        int attempts = 0;
                        bool result;
                        bool success = false; // Flag to track success

                        while (attempts < 5) {
                          // Maximum 5 attempts
                          contributeController.confirmpayLoading.value = true;

                          result =
                              await contributeController.confirmContribution(
                                  checkoutId: contributeController
                                      .contributeData["checkout_request_id"]);

                          await Future.delayed(const Duration(
                              seconds:
                                  5)); // Wait for 5 seconds before next attempt
                          attempts++;

                          if (result) {
                            var status = contributeController.status.value;
                            if (status == "SUCCESS") {
                              success = true;
                              break;
                            }
                            if (status == "FAILED") {
                              success = false;
                              break;
                            }
                          }
                        }
                        contributeController.confirmpayLoading.value = false;

                        if (success) {
                          Get.toNamed(NavRoutes
                              .contrSuccessScreen); // Navigate to success screen
                        } else {
                          Get.toNamed(NavRoutes
                              .contrErrScreen); // Navigate to error screen
                        }
                      } catch (e) {}
                    }
                    if (chan == 55) {
                      Get.off(
                          () => const CardPayment(isChamaContribute: false));
                      //Get.toNamed(NavRoutes.cardpayment);
                    }
                  } else {
                    ToastUtils.showErrorToast(
                      context,
                      contributeController.apiMessageContri.string,
                      "Error",
                    );
                  }
                } else {
                  ToastUtils.showErrorToast(
                    context,
                    "Check Your Values",
                    "Error",
                  );
                }
                _formKey.currentState?.reset();
              },
              btnText: "Make Contribution"),
          SizedBox(
            height: 30.h,
          )
        ],
      ),
    );
  }

  /// Navigates back to the previous screen.
  onTapArrowLeft(BuildContext context) {
    Navigator.pop(context);
  }
}
