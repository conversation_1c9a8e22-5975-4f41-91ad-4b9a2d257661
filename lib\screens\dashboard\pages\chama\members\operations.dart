import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/helpers/colors.dart';

import 'package:onekitty/helpers/show_snack_bar.dart';

import 'package:onekitty/models/chama/chama_memebers_model.dart';
import 'package:onekitty/models/chama/member_penalty_request.dart';
import 'package:onekitty/models/chama/penalty_model.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/dashboard/pages/chama/viewing_single_chama/transactions/chama_transactions.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/size_config.dart';
import 'package:date_time_format/date_time_format.dart';
import 'package:onekitty/utils/utils_exports.dart';
import 'package:flutter_expandable_fab/flutter_expandable_fab.dart';

class Operations extends StatefulWidget {
  const Operations({super.key});

  @override
  State<Operations> createState() => _OperationsState();
}

class _OperationsState extends State<Operations> {
  final chamaDataController = Get.find<ChamaDataController>();
  final chamaController = Get.find<ChamaController>();
  List<String> roles = [];

  @override
  void initState() {
    super.initState();
    // Initialize roles from the controller.
    roles = chamaController.roles.map((role) => role.role).toList();
    // Fetch members as soon as the widget loads.
    chamaController.getChamaMembers(
      chamaId: chamaDataController.chama.value.chama?.id,
      sort: "LEADERS",
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: SingleChildScrollView(
          child: Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 10.w),
            child: Column(
              children: [
                const RowAppBar(),
                // Display Chama title.
                Obx(() => Text(
                      chamaDataController.chama.value.chama?.title ?? "",
                      style: CustomTextStyles.titleMediumBlack900,
                    )),
                // Display total members count.
                Obx(() => chamaController.isLoadingChama.value
                    ? const Text("Checking...")
                    : Text(
                        "${chamaController.OData.value.total} Members",
                        style: Theme.of(context).textTheme.titleLarge,
                      )),
                SizedBox(height: 5.h),
                // Main content: either a loading spinner, empty state, or list of members.
                Obx(() {
                  if (chamaController.isLoadingChama.value) {
                    return SizedBox(
                      height: SizeConfig.screenHeight * .33,
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SpinKitDualRing(
                              color: AppColors.blueButtonColor,
                              lineWidth: 4.sp,
                              size: 40.0.sp,
                            ),
                            const Text("Loading...", style: TextStyle(color: Colors.white)),
                          ],
                        ),
                      ),
                    );
                  } 
                  return Obx(
                    ()=> chamaController.chamaMembers.isEmpty ? SizedBox(
                    height: SizeConfig.screenHeight * .33,
                    child: Center(
                      child: Text(
                        "No members available",
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                    ),
                  ) : ListView.builder(
                    shrinkWrap: true,
                      controller: chamaController.scrollController,
                      padding: EdgeInsets.zero,
                      itemCount: chamaController.chamaMembers.length,
                      itemBuilder: (context, index) {
                        final member = chamaController.chamaMembers[index];
                    return InkWell(
                    onTap: () {
                      _showMemberOptionsDialog(member);
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadiusStyle.roundedBorder8,
                      ),
                      child: Column(
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomImageView(
                                imagePath: AssetUrl.dotSix,
                                height: 25.h,
                                width: 25.w,
                                margin: EdgeInsets.only(right: 3.h),
                              ),
                              Opacity(
                                opacity: 0.5,
                                child: Padding(
                                  padding: EdgeInsets.only(top: 6.h, bottom: 8.h),
                                  child: Text(
                                    "${index + 1}",
                                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      color: AppColors.blueButtonColor.withOpacity(0.53),
                    ),
                                  ),
                                ),
                              ),
                              CustomImageView(
                                imagePath: AssetUrl.imgPerson,
                                height: 25.h,
                                width: 25.w,
                                margin: EdgeInsets.only(left: 3.h),
                              ),
                              // Wrap the member details Column in a Flexible to constrain its width.
                              Flexible(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Padding(
                  padding: EdgeInsets.only(left: 6.h, top: 1.h, bottom: 1.h),
                  child: Text(
                    "${member.firstName} ${member.secondName}",
                    style: CustomTextStyles.titleSmallGray90001.copyWith(
                      color: Colors.black87,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                                    ),
                                    Padding(
                  padding: EdgeInsets.only(left: 6.h, top: 1.h, bottom: 1.h),
                  child: Text(
                    member.phoneNumber ?? "",
                    style: CustomTextStyles.titleSmallGray90001.copyWith(
                      color: Colors.black54,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                                    ),
                                    Padding(
                  padding: EdgeInsets.only(left: 6.h, top: 1.h, bottom: 1.h),
                  child: Text(
                    "Joined On: ${DateTimeFormat.format(member.updatedAt?.toLocal() ?? DateTime.now(), format: 'M j')}",
                    overflow: TextOverflow.ellipsis,
                    style: CustomTextStyles.titleSmallGray90001.copyWith(
                      color: Colors.black54,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                                    ),
                                  ],
                                ),
                              ),
                              const Spacer(),
                              Padding(
                                padding: const EdgeInsets.only(top: 12.0),
                                child: CustomImageView(
                                  imagePath: AssetUrl.crownsv,
                                ),
                              ),
                              Expanded(
                                child: Padding(
                                  padding: EdgeInsets.symmetric(vertical: 10.h).copyWith(left: 5.h),
                                  child: Text(
                                    member.role ?? "",
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                  color: getRoleColors(member.role ?? ""),
                  fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                              IconButton(
                                icon: const Icon(
                                  Icons.edit,
                                  color: AppColors.blueButtonColor,
                                ),
                                padding: EdgeInsets.symmetric(vertical: 10.h),
                                onPressed: () {
                                  _showMemberOptionsDialog(member);
                                },
                              ),
                            ],
                          ),
                          SizedBox(height: 2.h),
                          const Divider(),
                        ],
                      ),
                    ),
                  );
                    },
                    ),
                  );
                }),
                // Loading more indicator or scroll end message.
                Obx(() {
                  if (chamaController.loadingMore.value) {
                    return const Text("Loading more members");
                  } else if (chamaController.scrollEnd.value) {
                    return const Text("You have reached the end of the list.");
                  }
                  return const SizedBox.shrink();
                }),
              ],
            ),
          ),
        ),
        // Floating action button with sample options.
   floatingActionButtonLocation: ExpandableFab.location,
        floatingActionButton: ExpandableFab(
          type: ExpandableFabType.up,
          openButtonBuilder: RotateFloatingActionButtonBuilder(
            child: const Icon(Icons.account_box),
            fabSize: ExpandableFabSize.regular,
            foregroundColor: Colors.white,
            backgroundColor: AppColors.blueButtonColor,
            shape: const CircleBorder(),
          ),
          closeButtonBuilder: DefaultFloatingActionButtonBuilder(
            child: const Icon(Icons.close),
            fabSize: ExpandableFabSize.regular,
            foregroundColor: Colors.white,
            backgroundColor: AppColors.blueButtonColor,
            shape: const CircleBorder(),
          ),
          children: [
            InkWell(
              onTap: () {
                Get.toNamed(NavRoutes.invite);
              },
              child: Container(
                decoration: BoxDecoration(
                  color: AppColors.blueButtonColor,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    children: [
                      CustomImageView(imagePath: AssetUrl.contact),
                      const Text(
                        "Add Chama Member",
                        style: TextStyle(fontSize: 15, color: Colors.white, fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            InkWell(
              onTap: () {
                Get.toNamed(NavRoutes.operation);
              },
              child: Container(
                decoration: BoxDecoration(
                  color: AppColors.blueButtonColor,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    children: [
                      CustomImageView(imagePath: AssetUrl.scroll, color: Colors.white),
                      const Text(
                        "Set Receiving Order",
                        style: TextStyle(fontSize: 15, color: Colors.white, fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            InkWell(
              onTap: () {
                Get.toNamed(NavRoutes.penalizeMultiple);
              },
              child: Container(
                decoration: BoxDecoration(
                  color: AppColors.blueButtonColor,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    children: [
                      CustomImageView(imagePath: AssetUrl.scroll, color: Colors.white),
                      const Text(
                        "Issue penalty(ies)",
                        style: TextStyle(fontSize: 15, color: Colors.white, fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  void _showMemberOptionsDialog(ChamaMembers member) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _rowWidget(
                imagePath: AssetUrl.contact,
                text: "View all ${member.firstName} penalties",
                onTap: () {
                  _showMemberPenalties(member.id ?? 0, "${member.firstName} ${member.secondName}");
                },
              ),
              _rowWidget(
                imagePath: AssetUrl.chamaTransactions,
                text: "View all ${member.firstName} transactions",
                onTap: () {
                  Get.to(() => ChamaTransactionsPage(
                        isFullPage: false,
                        isFromMemberTransactions: true,
                        accountNo: member.phoneNumber,
                        member: member,
                      ));
                },
              ),
              if (chamaDataController.chama.value.member?.role == "CHAIRPERSON")
                _rowWidget(
                  imagePath: AssetUrl.penalty,
                  text: "Issue penalty to ${member.firstName}",
                  onTap: () {
                    Get.back();
                    showModalBottomSheet(
                      context: context,
                      isScrollControlled: true,
                      builder: (context) {
                        return PenaltiesBottomSheet(member: member);
                      },
                    );
                  },
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _rowWidget({required String imagePath, required String text, required Function() onTap}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5.0),
      child: InkWell(
        onTap: onTap,
        child: Row(
          children: [
            SizedBox(
              width: 25.w,
              child: CustomImageView(imagePath: imagePath),
            ),
            const SizedBox(width: 12),
            Expanded(child: Text(text)),
          ],
        ),
      ),
    );
  }

  void _showMemberPenalties(int memberId, String memberName) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return DraggableScrollableSheet(
          maxChildSize: 0.97,
          initialChildSize: 0.7,
          expand: false,
          builder: (context, scrollController) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 5),
                  child: Text(
                    "${memberName.toUpperCase()} PENALTIES",
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          decoration: TextDecoration.underline,
                        ),
                  ),
                ),
                const SizedBox(height: 7),
                Expanded(
                  child: Obx(() {
                    if (chamaController.isGetMemberPenaltiesLoading.value) {
                      return SizedBox(
                        height: SizeConfig.screenHeight * .33,
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SpinKitDualRing(
                                color: ColorUtil.blueColor,
                                lineWidth: 4.sp,
                                size: 40.0.sp,
                              ),
                              const Text("Loading...", style: TextStyle(color: Colors.white))
                            ],
                          ),
                        ),
                      );
                    } else if (chamaController.memberPenalties.isEmpty) {
                      return const Padding(
                        padding: EdgeInsets.all(13.0),
                        child: Text("No penalties available"),
                      );
                    } else {
                      return ListView.separated(
                        separatorBuilder: (context, index) => const Divider(),
                        itemCount: chamaController.memberPenalties.length,
                        itemBuilder: (context, index) {
                          final memberPenalty = chamaController.memberPenalties[index];
                          return Container(
                            margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                            child: ListTile(
                              leading: Text("${index + 1}"),
                              title: Text(
                                "REASON: ${memberPenalty.reason}",
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text("STATUS: ${memberPenalty.status}"),
                                  Text("AMOUNT PAID: ${memberPenalty.paidAmount ?? "0"}"),
                                  Text("ISSUED ON: ${DateFormat("yyyy-MM-dd").format(memberPenalty.createdAt ?? DateTime.now())}"),
                                ],
                              ),
                              trailing: Text(
                                "${memberPenalty.amount}",
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Colors.red),
                              ),
                            ),
                          );
                        },
                      );
                    }
                  }),
                ),
              ],
            );
          },
        );
      },
    );
  }
}

class PenaltiesBottomSheet extends StatelessWidget {
  final ChamaMembers? member;
  const PenaltiesBottomSheet({super.key, this.member});

  @override
  Widget build(BuildContext context) {
    final chamaDataController = Get.find<ChamaDataController>();
    final chamaController = Get.find<ChamaController>();

    // Fetch penalties when the bottom sheet is opened.
    chamaController.getChamaPenalties(
      chamaId: chamaDataController.chama.value.chama?.id ?? 0,
    );

    return Padding(
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
        child: Column(
          children: [
            Text(
              "Pick a penalty to assign from the ones below",
              style: Theme.of(context)
                  .textTheme
                  .bodyMedium
                  ?.copyWith(fontSize: 15, decoration: TextDecoration.underline),
            ),
            const SizedBox(height: 12),
            Expanded(
              child: Obx(() {
                if (chamaController.isGetChamaPenaltyLoading.value) {
                  return SizedBox(
                    height: SizeConfig.screenHeight * .33,
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SpinKitDualRing(
                            color: ColorUtil.blueColor,
                            lineWidth: 4.sp,
                            size: 40.0.sp,
                          ),
                          const Text("Loading...", style: TextStyle(color: Colors.white)),
                        ],
                      ),
                    ),
                  );
                } else if (chamaController.penalties.isEmpty) {
                  return const Text("No penalties added");
                } else {
                  return SizedBox(
                    height: 300,
                    child: ListView.separated(
                      itemCount: chamaController.penalties.length,
                      separatorBuilder: (context, index) => const Divider(),
                      itemBuilder: (context, index) {
                        final penalty = chamaController.penalties[index];
                        return PenaltyCard(
                          member: member!,
                          index: index,
                          penalty: penalty,
                          amtInitialValue: penalty.amount.toString(),
                          reasonInitialValue: penalty.title ?? "",
                        );
                      },
                    ),
                  );
                }
              }),
            ),
          ],
        ),
      ),
    );
  }
}

class PenaltyCard extends StatefulWidget {
  final int index;
  final PenaltyModel penalty;
  final String amtInitialValue;
  final String reasonInitialValue;
  final ChamaMembers member;

  const PenaltyCard({
    super.key,
    required this.index,
    required this.member,
    required this.penalty,
    required this.amtInitialValue,
    required this.reasonInitialValue,
  });

  @override
  State<PenaltyCard> createState() => _PenaltyCardState();
}

class _PenaltyCardState extends State<PenaltyCard> {
  final chamaController = Get.find<ChamaController>();
  final chamaDataController = Get.find<ChamaDataController>();
  final TextEditingController amountController = TextEditingController();
  final TextEditingController reasonController = TextEditingController();

  @override
  void initState() {
    super.initState();
    amountController.text = widget.amtInitialValue;
    reasonController.text = widget.reasonInitialValue;
  }

  void onSubmitPenalty() async {
    MemberBeingPenalized memberPenalty = MemberBeingPenalized(
      memberId: widget.member.id,
      amount: amountController.text.trim().isEmpty
          ? widget.penalty.amount
          : int.parse(amountController.text.trim()),
      reason: reasonController.text.trim().isEmpty
          ? widget.penalty.title
          : reasonController.text.trim(),
    );
    MultiplePenaltyRequest request2 = MultiplePenaltyRequest(
      chamaId: chamaDataController.chama.value.chama?.id,
      penaltyId: widget.penalty.id,
      members: [memberPenalty],
    );
    bool res = await chamaController.penalizeMultiple(request: request2);
    if (res) {
      if (!mounted) return;
      Snack.show(res, chamaController.apiMessage.string);
      Navigator.pop(context);
    } else {
      if (!mounted) return;
      Snack.show(res, chamaController.apiMessage.string);
    }
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        setState(() {
          amountController.text = widget.amtInitialValue;
          reasonController.text = widget.reasonInitialValue;
        });
      },
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(9),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: AppColors.blueButtonColor.withOpacity(0.5),
            ),
            child: Text("${widget.index + 1}"),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.penalty.title ?? "",
                  style: Theme.of(context)
                      .textTheme
                      .bodyMedium
                      ?.copyWith(fontWeight: FontWeight.w800),
                ),
                Text(widget.penalty.description ?? ""),
                Text(
                  FormattedCurrency().getFormattedCurrency(widget.penalty.amount),
                  style: Theme.of(context)
                      .textTheme
                      .bodyMedium
                      ?.copyWith(color: Colors.red),
                ),
                Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16),
                      child: CustomTextField(
                        isRequired: true,
                        showNoKeyboard: true,
                        labelText: "Amount",
                        controller: amountController,
                        validator: (value) {
                          if (value!.isEmpty) {
                            return "This field cannot be empty";
                          }
                          return null;
                        },
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16),
                      child: CustomTextField(
                        isRequired: true,
                        labelText: "Reason",
                        controller: reasonController,
                        validator: (value) {
                          if (value!.isEmpty) {
                            return "Kindly give a reason";
                          }
                          return null;
                        },
                      ),
                    ),
                    SizedBox(
                      height: 45,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          OutlinedButton(
                            onPressed: () {
                              setState(() {
                                amountController.text = widget.amtInitialValue;
                                reasonController.text = widget.reasonInitialValue;
                              });
                            },
                            child: const Text("CANCEL"),
                          ),
                          Obx(() => CustomKtButton(
                                width: 70,
                                isLoading: chamaController.isPenalizeMultiple.value,
                                onPress: onSubmitPenalty,
                                btnText: "OK",
                              )),
                        ],
                      ),
                    ),
                  ],
                )
              ],
            ),
          ),
        ],
      ),
    );
  }
}
