import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage_pro/get_storage_pro.dart';
import 'package:onekitty/controllers/events/controllers.dart';
import 'package:onekitty/controllers/events/events_controller.dart';
import 'package:onekitty/controllers/events/payments_page_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/topUp_otp.dart';
import 'package:onekitty/utils/cache_keys.dart';
import 'package:onekitty/utils/my_text_field.dart';
import 'package:onekitty/utils/my_button.dart';
import '../contribution/cardPayment.dart';

class PaymentsPage extends StatefulWidget {
  final List<Map<String, dynamic>>? tickets;
  final int eventId;
  final int price;
  const PaymentsPage(
      {super.key, this.tickets, required this.eventId, required this.price});

  @override
  State<PaymentsPage> createState() => _PaymentsPageState();
}

class _PaymentsPageState extends State<PaymentsPage> {
  final ValueNotifier<Map<String, dynamic>?> _selectedValue =
      ValueNotifier(null);
  final List paymentMethods = [
    {
      'title': 'Mpesa',
      'icon': 'mpesa.png',
      'channel': 63902,
    },
    {
      'title': 'Sasapay',
      'icon': 'sasapay.png',
      'channel': 0,
    },
    {
      'title': 'Visa',
      'icon': 'visa.png',
      'channel': 55,
    },
    {
      'title': 'Airtel Money',
      'icon': 'airtelmoney.png',
      'channel': 63903,
    }
  ];
  final GetStorage box = Get.find();
  final TextEditingController _fullNames = TextEditingController();
  final TextEditingController _emailAddress = TextEditingController();
  final TextEditingController _phone = TextEditingController();
  final _controller = Get.find<PaymentsController>();
  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
        valueListenable: _selectedValue,
        builder: (context, selectedValue, child) {
          return Container(
              padding: EdgeInsets.only(
                top: 16.0.h,
                left: 8.0.w,
                right: 8.0.w,
                bottom: MediaQuery.of(context).viewInsets.bottom + 16.0.h,
              ),
              margin: EdgeInsets.only(
                left: 8.0.w,
                right: 8.0.w,
              ),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            'Select Payment Method',
                            style: TextStyle(
                                fontSize: 18.spMin,
                                fontWeight: FontWeight.bold),
                          ),
                        ),
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(false),
                          child: const Text('Cancel',
                              style: TextStyle(color: primaryColor)),
                        ),
                      ],
                    ),
                    const Divider(),
                    SizedBox(
                      height: 136.h,
                      child: ListView.builder(
                          itemCount: paymentMethods.length,
                          shrinkWrap: true,
                          scrollDirection: Axis.horizontal,
                          itemBuilder: (context, index) {
                            return GestureDetector(
                              onTap: () =>
                                  _selectedValue.value = paymentMethods[index],
                              child: Container(
                                  height: 120.h,
                                  width: 120.w,
                                  alignment: Alignment.center,
                                  margin: EdgeInsets.all(8.spMin),
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 8.w, vertical: 0),
                                  decoration: BoxDecoration(
                                      color: selectedValue ==
                                              paymentMethods[index]
                                          ? AppColors.primary.withOpacity(0.05)
                                          : Colors.white,
                                      border: Border.all(
                                          width: selectedValue ==
                                                  paymentMethods[index]
                                              ? 2
                                              : 1,
                                          color: selectedValue ==
                                                  paymentMethods[index]
                                              ? AppColors.primary
                                              : Colors.grey),
                                      borderRadius:
                                          BorderRadius.circular(15.r)),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        paymentMethods[index]['title'],
                                        style: const TextStyle(
                                            fontSize: 20,
                                            fontWeight: FontWeight.w600),
                                      ),
                                      Image.asset(
                                          height: paymentMethods[index]
                                                      ['title'] ==
                                                  "Airtel Money"
                                              ? 66.h
                                              : 80.h,
                                          width: paymentMethods[index]
                                                      ['title'] ==
                                                  "Airtel Money"
                                              ? 66.w
                                              : 80.w,
                                          fit: BoxFit.contain,
                                          'assets/images/${paymentMethods[index]['icon']}')
                                    ],
                                  )),
                            );
                          }),
                    ),
                    selectedValue == null
                        ? const SizedBox()
                        : Column(
                            children: [
                              SizedBox(height: 20.h),
                              if (selectedValue['title'] == 'Mpesa' ||
                                  selectedValue['title'] == 'Airtel Money' ||
                                  selectedValue['title'] == 'Sasapay')
                                Column(
                                  children: [
                                    MyTextField(
                                        controller: _fullNames,
                                        keyboardType: TextInputType.name,
                                        title: 'Full Names(Optional)'),
                                    MyTextField(
                                        controller: _emailAddress,
                                        keyboardType:
                                            TextInputType.emailAddress,
                                        title: 'Email Address(Optional)'),
                                    MyTextField(
                                        controller: _phone,
                                        keyboardType: TextInputType.phone,
                                        title:
                                            'Enter ${selectedValue['title'] == 'Mpesa' ? "Mpesa" : "Phone"} Number'),
                                  ],
                                ),
                              if (selectedValue['title'] == 'Visa')
                                Column(
                                  children: [
                                    MyTextField(
                                        controller: _fullNames,
                                        title: 'Full Names'),
                                    MyTextField(
                                        controller: _emailAddress,
                                        title: 'Email Address'),
                                  ],
                                ),
                              SizedBox(height: 20.h),
                              Obx(() {
                                Map<String, dynamic> data = {
                                  'channel_code': selectedValue['channel'],
                                  'phone_number': _phone.text,
                                  // 'affiliate_code': "344",
                                  'longitude': box.read(CacheKeys.long),
                                  'latitude': box.read(CacheKeys.lat),
                                  'first_name': _fullNames.text,
                                  'second_name': "",
                                  'email': _emailAddress.text,
                                  'tickets': widget.tickets,
                                  'event_id': widget.eventId.toString(),
                                };
                                return MyButton(
                                    showLoading: _controller.isLoading.value,
                                    label: 'Pay',
                                    onClick: () async {
                                      if (!_controller.isLoading.value) {
                                        if (selectedValue['title'] == "Visa") {
                                          if (await _controller
                                              .cardPaymentCheckout(
                                                  phoneNumber:
                                                      Get.put(Eventcontroller())
                                                              .getLocalUser()
                                                              ?.phoneNumber ??
                                                          "",
                                                  email: _emailAddress.text,
                                                  amount: widget.price,
                                                  channel: 55,
                                                  userId:
                                                      Get.put(Eventcontroller())
                                                              .getLocalUser()
                                                              ?.id ??
                                                          0)) {
                                            Get.off(() => const CardPayment(
                                                isPurchasingTicket: true,
                                                isChamaContribute: false));
                                          }
                                        } else if (selectedValue['title'] ==
                                            'Sasapay') {
                                          if (await _controller.completePayment(
                                              context: context, data: data)) {
                                            Get.off(() => TopUpOtp(
                                                transId: _controller
                                                    .transId.string));
                                          }
                                        } else {
                                          if (await _controller.completePayment(
                                              context: context, data: data)) {
                                            Snack.show(true,
                                                '${selectedValue['title']} STK sent. Enter your PIN to complete transaction');

                                            Get.defaultDialog(
                                                title:
                                                    '${selectedValue['title']} Transaction Processing',
                                                content:
                                                    const MPESATransactionWidget());
                                          }
                                        }
                                      }
                                    });
                              })
                            ],
                          )
                  ],
                ),
              ));
        });
  }
}

class MPESATransactionWidget extends StatelessWidget {
  const MPESATransactionWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.grey[200],
      child: Center(
        child: Card(
          margin: const EdgeInsets.all(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'Success. STK PUSH SENT',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Didn\'t receive this STK push?',
                  style: TextStyle(fontSize: 16),
                ),
                TextButton(
                  child: const Text(
                    'Manual Method',
                    style: TextStyle(
                      decoration: TextDecoration.underline,
                      color: Colors.blue,
                    ),
                  ),
                  onPressed: () {
                    // Handle manual method action
                  },
                ),
                const SizedBox(height: 16),
                const Text(
                  'Kindly Wait for the STK PUSH',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
