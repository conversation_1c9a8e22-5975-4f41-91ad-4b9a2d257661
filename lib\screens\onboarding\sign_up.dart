// ignore_for_file: must_be_immutable
import 'package:fl_country_code_picker/fl_country_code_picker.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animated_dialog_updated/flutter_animated_dialog.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage_pro/get_storage_pro.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty/controllers/auth_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/models/auth/register_request.dart';
import 'package:onekitty/screens/onboarding/login_screen.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';
import 'package:onekitty/screens/onboarding/pinput.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/cache_keys.dart';
import 'package:onekitty/utils/privacy_policy.dart';
import 'package:onekitty/utils/size_config.dart';

import '../../utils/utils_exports.dart';

class SignUpPage extends StatefulWidget {
  SignUpPage({super.key, required this.isForgotPasswd});
  bool isForgotPasswd;
  @override
  State<SignUpPage> createState() => _SignUpPageState();
}

class _SignUpPageState extends State<SignUpPage> {
  final _formKey = GlobalKey<FormState>();
  TextEditingController phoneController = TextEditingController();
  TextEditingController nameController = TextEditingController();
  final AuthenticationController authenticationController =
      Get.put(AuthenticationController());
  final countryPicker = const FlCountryCodePicker();
  PhoneNumber num = PhoneNumber(isoCode: 'KE');
  final box = GetStorage();

  String countryCode = "";
  bool acceptTerms = false;
  String myPhone = "";

  @override
  void dispose() {
    super.dispose();
    phoneController.dispose();
    nameController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    return Scaffold(
      body: Container(
        height: screenSize.height - 10,
        margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
        child: SingleChildScrollView(
          child: Column(
            children: [
              CustomImageView(
                imagePath: AssetUrl.logoVariation1,
                width: screenSize.width * 0.45,
              ),
              const Text(
                "Karibu",
                style: TextStyle(
                    color: AppColors.greyTextColor,
                    fontSize: 15,
                    fontWeight: FontWeight.w600),
              ),
              Text(
                widget.isForgotPasswd ? "Reset Password" : "Create an Account",
                style:
                    const TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
              ),
              Form(
                  key: _formKey,
                  autovalidateMode: AutovalidateMode.onUserInteraction,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 20.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Visibility(
                          visible: !widget.isForgotPasswd,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text("Full Name",
                                  style: TextStyle(
                                      color: AppColors.dark,
                                      fontWeight: FontWeight.w600)),
                              CustomTextField(
                                controller: nameController,
                                labelText: "e.g John Juma",
                                validator: (p0) {
                                  if (p0!.isEmpty || p0.length < 6) {
                                    return "Name is required";
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(
                                height: 10,
                              ),
                            ],
                          ),
                        ),
                        InternationalPhoneNumberInput(
                          onInputChanged: (num) {
                            setState(() {
                              myPhone = num.phoneNumber!;
                              countryCode = num.isoCode!;
                            });
                          },
                          onInputValidated: (bool value) {},
                          selectorConfig: const SelectorConfig(
                            selectorType: PhoneInputSelectorType.BOTTOM_SHEET,
                            useBottomSheetSafeArea: true,
                          ),
                          ignoreBlank: false,
                          autoValidateMode: AutovalidateMode.disabled,
                          selectorTextStyle:
                              const TextStyle(color: Colors.black),
                          initialValue: num,
                          textFieldController: phoneController,
                          formatInput: true,
                          keyboardType: const TextInputType.numberWithOptions(
                              signed: true, decimal: true),
                          inputBorder: const OutlineInputBorder(),
                          onSaved: (PhoneNumber number) {},
                        ),
                        widget.isForgotPasswd
                            ? const SizedBox()
                            : Row(
                                children: [
                                  Checkbox(
                                      value: acceptTerms,
                                      checkColor: AppColors.primary,
                                      focusColor: AppColors.greyTextColor,
                                      activeColor: Colors.blue[300],
                                      side: const BorderSide(
                                          color: Colors.black12),
                                      onChanged: (value) {
                                        buildAcceptTermsDialog(
                                            context: context);
                                      }),
                                  Expanded(
                                    child: RichText(
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                        text: TextSpan(children: [
                                          TextSpan(
                                              text:
                                                  "By signing up you agree to our ",
                                              style: context.bodyExtraSmall!
                                                  .copyWith(
                                                      color:
                                                          AppColors.neutralDark,
                                                      fontSize: 12)),
                                          TextSpan(
                                              text: "Terms & Conditions",
                                              recognizer: TapGestureRecognizer()
                                                ..onTap = () {
                                                  buildAcceptTermsDialog(
                                                      context: context);
                                                },
                                              style: const TextStyle(
                                                  color: AppColors.primary,
                                                  decoration:
                                                      TextDecoration.underline))
                                        ])),
                                  )
                                ],
                              ),
                        const SizedBox(
                          height: 20,
                        ),
                        Obx(
                          () => CustomKtButton(
                            isLoading:
                                authenticationController.isloading.isTrue,
                            onPress: () async {
                              if (_formKey.currentState!.validate()) {
                                if (widget.isForgotPasswd) {
                                  acceptTerms = true;
                                }
                                if (acceptTerms) {
                                  String fullName = nameController.text.trim();
                                  String? firstName;
                                  String? lastName;
                                  List<String> names =
                                      fullName.split(RegExp(r'\s+'));
                                  if (names.isNotEmpty) {
                                    firstName = names[0];
                                    if (names.length > 1) {
                                      lastName = names[names.length - 1];
                                    }
                                  }
                                  bool res = false;
                                  if (widget.isForgotPasswd) {
                                    res = await authenticationController
                                        .forgotPassRequest(
                                      myPhone.substring(1),
                                    );
                                  } else {
                                    RegisterRequest request = RegisterRequest(
                                      phoneNumber: myPhone.substring(1),
                                      countryCode: countryCode,
                                      latitude: box.read(CacheKeys.lat),
                                      longitude: box.read(CacheKeys.long),
                                      firstName: firstName,
                                      secondName: lastName,
                                    );

                                    print(request);
                                    res = await authenticationController
                                        .register(request: request);
                                  }
                                  if (!mounted) {
                                    return;
                                  }
                                  Snack.show(
                                      res,
                                      authenticationController
                                          .apiMessage.string);
                                  if (res) {
                                    Get.to(
                                        () => PinPutPage(phoneNumber: myPhone));
                                  }
                                } else {
                                  Snack.show(false,
                                      "Accept our terms and conditions to continue");
                                }
                              }
                            },
                            text: "register",
                            onPressed: () {},
                            btnText: 'Verify number',
                          ),
                        ),
                      ],
                    ),
                  )),
              const SizedBox(
                height: 15,
              ),
              GestureDetector(
                onTap: () {
                  Get.to(() => const LoginScreen());
                },
                child: RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: "Already have an account?",
                        style: CustomTextStyles.bodyLargeff6e6f79,
                      ),
                      TextSpan(
                        text: " Sign in",
                        style: TextStyle(
                            color: appTheme.indigo500, fontSize: 15.sp),
                      ),
                    ],
                  ),
                  //textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  buildAcceptTermsDialog({required BuildContext context}) {
    showAnimatedDialog(
        context: context,
        barrierDismissible: true,
        animationType: DialogTransitionType.sizeFade,
        curve: Curves.fastOutSlowIn,
        duration: const Duration(milliseconds: 900),
        builder: (context) {
          return Dialog(
            child: Scaffold(
                floatingActionButtonLocation:
                    FloatingActionButtonLocation.centerFloat,
                floatingActionButton: CustomKtButton(
                  width: SizeConfig.screenWidth * .7,
                  height: 40,
                  btnText: "Accept",
                  onPress: () {
                    setState(() {
                      acceptTerms = true;
                      Navigator.pop(context);
                    });
                  },
                ),
                body: const PrivacyPolicy()),
          );
        });
  }
}
