import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/controllers/events/controllers.dart';
import 'package:onekitty/controllers/events/edit_event_controller.dart';
import 'package:onekitty/controllers/events/view_single_event.dart';
import 'package:onekitty/controllers/events/vieweventcontroller.dart';
import 'package:onekitty/models/events/events_model.dart';
import 'package:onekitty/models/events/media_models.dart';
import 'package:onekitty/models/events/tickets_model.dart';
import 'package:onekitty/screens/dashboard/pages/events/payments_page.dart';
import 'package:onekitty/utils/date_formatter.dart';
import 'package:onekitty/utils/formatted_currency.dart';
import 'package:onekitty/utils/image_popup.dart';
import 'package:onekitty/utils/my_button.dart';
import 'package:onekitty/utils/my_text_field.dart';
import 'package:onekitty/utils/search_widget.dart';
import 'package:onekitty/utils/show_cached_network_image.dart';
import 'package:onekitty/utils/timeSince.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:share_plus/share_plus.dart';
import '/utils/glassmorphic.dart';
import 'package:carousel_slider/carousel_slider.dart' as q;
import 'package:onekitty/utils/my_quill_editor.dart';

import 'map_page.dart';

class ViewSingleEventViewer extends StatefulWidget {
  final Event event;
  const ViewSingleEventViewer({super.key, required this.event});

  @override
  State<ViewSingleEventViewer> createState() => _ViewSingleEventViewerState();
}

class _ViewSingleEventViewerState extends State<ViewSingleEventViewer> {
  final _refreshController = RefreshController(initialRefresh: true);
  final controller = Get.put(ViewSingleEventController());
  final getEventController = Get.put(EditEventController());
  @override
  void initState() {
    _onRefresh();
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _onRefresh() async {
    try {
      getEventController.fetchEventDetail(widget.event.id);
      _refreshController.refreshCompleted();
    } catch (e) {
      _refreshController.refreshCompleted();
      Logger().e(e);
    }
  }

  final activeIndex = 0.obs;
  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(392.72727272727275, 803.6363636363636),
      child: Scaffold(body: Obx(() {
        final event = controller.event.value;
        return SmartRefresher(
          onRefresh: _onRefresh,
          controller: _refreshController,
          child: CustomScrollView(slivers: [
            SliverAppBar(
                // floating: true,
                leadingWidth: 88,
                pinned: true,
                backgroundColor: scaffoldBackgroundColor,
                bottom: PreferredSize(
                    preferredSize: const Size(0, 0),
                    child: Container(
                        height: 55,
                        alignment: Alignment.center,
                        width: 280,
                        padding: const EdgeInsets.symmetric(
                            vertical: 1, horizontal: 2),
                        decoration: BoxDecoration(
                            color: Colors.white,
                            boxShadow: [
                              BoxShadow(
                                  color: Colors.grey.shade500,
                                  offset: const Offset(0, 2),
                                  blurRadius: 12,
                                  spreadRadius: -4)
                            ],
                            borderRadius: BorderRadius.circular(30)),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            AttendeesWidget(
                              count: event.count ?? 0,
                              size: 18,
                              textSize: 15,
                            ),
                            Material(
                              borderRadius: BorderRadius.circular(25),
                              color: primaryColor,
                              child: SizedBox(
                                height: 30,
                                width: 70,
                                child: MaterialButton(
                                    onPressed: () {
                                      //  Share event link
                                      final url =
                                          'https://www.onekitty.co.ke/events/${event.username}'; // Replace with actual URL

                                      Share.share(
                                        'Check out this event: $url',
                                        subject: event.title,
                                      );
                                    },
                                    child: const Text(
                                      'Invite',
                                      style: TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.w400,
                                          fontSize: 12),
                                    )),
                              ),
                            )
                          ],
                        ))),
                leading: GlassmorphicContainer(
                  onTap: () => Navigator.pop(context),
                  color: Colors.black,
                  blurRadius: 20,
                  cornerRadius: 24,
                  child: const Row(
                    children: [
                      Icon(
                        Icons.arrow_back,
                        color: Colors.white,
                        size: 18,
                      ),
                      SizedBox(width: 8),
                      Text('Back', style: TextStyle(color: Colors.white))
                    ],
                  ),
                ),
                expandedHeight: 250,
                flexibleSpace: FlexibleSpaceBar(
                    background: Hero(
                  tag: 'image:${event.id}',
                  child: Stack(alignment: Alignment.topCenter, children: [
                    SizedBox(
                      height: 250.h,
                      child: Stack(alignment: Alignment.topCenter, children: [
                        q.CarouselSlider.builder(
                          itemCount: event.eventMedia?.length ?? 0,
                          itemBuilder: (context, index, realIndex) {
                            return InkWell(
                              onTap: () {
                                Get.to(() => ImagePopup(
                                    pos: realIndex,
                                    title: event.title,
                                    imageUrl: event.eventMedia
                                            ?.map((e) => e.url)
                                            .toList() ??
                                        <String>[]));
                              },
                              child: ShowCachedNetworkImage(
                                fit: BoxFit.cover,
                                width: MediaQuery.sizeOf(context).width,
                                imageurl: event.eventMedia?[index].url ?? '',
                              ),
                            );
                          },
                          options: q.CarouselOptions(
                            height: 260.h,
                            viewportFraction: 1.0,
                            enableInfiniteScroll: false,
                            onPageChanged: (index, reason) {
                              activeIndex(index);
                              // carouselController.jumpToPage(index);
                            },
                          ),
                        ),
                        (event.eventMedia ?? <EventMedia>[]).isEmpty
                            ? const SizedBox()
                            : Positioned(
                                bottom: 38.h,
                                left: 0,
                                right: 0,
                                child: Obx(
                                  () => Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: List.generate(
                                      event.eventMedia?.length ?? 0,
                                      (index) => Container(
                                        margin: const EdgeInsets.symmetric(
                                            horizontal: 4),
                                        width: 8,
                                        height: 8,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          color: activeIndex.value == index
                                              ? Colors.white70
                                              : Colors.white24,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                      ]),
                    ),
                  ]),
                ))),
            SliverToBoxAdapter(
                child: Hero(
              tag: 'text:${event.id}',
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Material(
                  color: Colors.transparent,
                  child: Text(event.title,
                      style: const TextStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.w700,
                          fontSize: 22)),
                ),
              ),
            )),
            SliverToBoxAdapter(
              child: Column(
                children: [
                  ListTile(
                    onTap: () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => MapScreen(
                          viewOnly: true,
                          longitude: event.longitude,
                          latitude: event.latitude,
                        ),
                      ),
                    ),
                    leading: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          color: primaryColor.withOpacity(0.15),
                        ),
                        child: Hero(
                            tag: "ilocation${event.id}",
                            child: Image.asset(
                                'assets/images/icons/location.png',
                                height: 30,
                                width: 30,
                                color: primaryColor))),
                    title: Hero(
                      tag: "tlocation${event.id}",
                      child: Material(
                        color: Colors.transparent,
                        child: Text(
                          event.venue,
                          style: const TextStyle(fontWeight: FontWeight.w600),
                        ),
                      ),
                    ),
                    subtitle: Text(event.locationTip,
                        style:
                            const TextStyle(fontSize: 12, color: Colors.grey)),
                  ),
                  ListTile(
                    onTap: () {
                      showDialog(
                        context: context,
                        builder: (context) => Dialog(
                          child: Padding(
                            padding: const EdgeInsets.all(16.0),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                CalendarDatePicker(
                                  initialDate: event.startDate!,
                                  firstDate: DateTime(2000),
                                  lastDate: DateTime(2100),
                                  onDateChanged: (_) {},
                                  selectableDayPredicate: (DateTime date) {
                                    return date.year == event.startDate!.year &&
                                        date.month == event.startDate!.month &&
                                        date.day == event.startDate!.day;
                                  },
                                ),
                                Align(
                                  alignment: Alignment.centerRight,
                                  child: TextButton(
                                    onPressed: () => Navigator.pop(context),
                                    child: const Text('Close'),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                    leading: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          color: primaryColor.withOpacity(0.15),
                        ),
                        child: Hero(
                          tag: "idate${event.id}",
                          child: Image.asset('assets/images/icons/calendar.png',
                              height: 30, width: 30, color: primaryColor),
                          //  const Icon(Icons.calendar_month_rounded,
                          //     color: primaryColor)
                        )),
                    title: Hero(
                      tag: "tdate${event.id}",
                      child: Material(
                        color: Colors.transparent,
                        child: Text(
                          formatDate("${event.startDate?.toLocal()}"),
                          style: const TextStyle(fontWeight: FontWeight.w600),
                        ),
                      ),
                    ),
                    subtitle: Text(
                        event.startDate == null || event.endDate == null
                            ? ''
                            : '${DateFormat('EEEE').format(event.startDate?.toLocal() ?? DateTime.now())}, ${formatTime("${event.startDate?.toLocal()}")} - ${formatDate("${event.startDate?.toLocal()}") == formatDate("${event.endDate?.toLocal()}") ? formatTime("${event.endDate?.toLocal()}") : "${DateFormat('EEEE').format(event.endDate?.toLocal() ?? DateTime.now())}, ${formatTime("${event.endDate?.toLocal()}")}"}\n'
                                ' - ${highPrecisiontimeSince(event.endDate?.toLocal() ?? DateTime.now())}',
                        style:
                            const TextStyle(fontSize: 12, color: Colors.grey)),
                  ),
                ],
              ),
            ),
            SliverToBoxAdapter(
              child: Padding(
                padding:
                    EdgeInsets.symmetric(horizontal: 12.0.w, vertical: 2.h),
                child: Text(
                  'About Event',
                  style: TextStyle(
                      fontSize: 16.spMin,
                      fontWeight: FontWeight.w500,
                      color: Colors.black),
                ),
              ),
            ),
            SliverToBoxAdapter(
                child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 12.0.w, vertical: 4.h),
              child: Hero(
                  tag: 'desc:${event.id}',
                  child: QuillEditorWidget(text: event.description)),
            )),
            SliverToBoxAdapter(
              child: Padding(
                padding:
                    EdgeInsets.symmetric(horizontal: 12.0.w, vertical: 2.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 8),
                    ColoredBox(
                      color: Theme.of(context).scaffoldBackgroundColor,
                      child: ListTile(
                        title: Text(event.username,
                            style: const TextStyle(
                                fontSize: 14, fontWeight: FontWeight.w600)),
                        subtitle: Text('Organizer',
                            style: TextStyle(
                                fontSize: 12, color: Colors.grey.shade700)),
                        leading: CircleAvatar(
                          backgroundColor: primaryColor,
                          child: Text(
                            event.title.isNotEmpty
                                ? event.title
                                    .split(' ')
                                    .map((word) => word[0])
                                    .take(2)
                                    .join()
                                    .toUpperCase()
                                : '',
                            style: const TextStyle(color: Colors.white),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                  ],
                ),
              ),
            ),
            SliverToBoxAdapter(
                child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: Column(
                children: [
                  Material(
                    borderRadius: BorderRadius.circular(25),
                    color: primaryColor,
                    child: SizedBox(
                      // height: 30,
                      // width: 70,
                      child: Row(
                        children: [
                          Expanded(
                            child: Builder(builder: (context) {
                              return MaterialButton(
                                  onPressed: () async {
                                    showDialog(
                                        context: context,
                                        builder: (context) => BuyTicket(
                                            eventId: event.id,
                                            ticket:
                                                event.tickets ?? <Ticket>[]));
                                  },
                                  child: const Text(
                                    'Buy Ticket',
                                    style: TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.w400,
                                        fontSize: 16),
                                  ));
                            }),
                          ),
                        ],
                      ),
                    ),
                  )
                ],
              ),
            )),
            const SliverToBoxAdapter(child: SizedBox(height: 100))
          ]),
        );
      })),
    );
  }
}

class BuyTicket extends StatelessWidget {
  final List<Ticket> ticket;
  final int eventId;
  const BuyTicket({super.key, required this.ticket, required this.eventId});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(ViewEventController());

    return Dialog(
      child: Builder(
        builder: (context) {
          final ValueNotifier<List<Map<String, dynamic>>> purchasedTickets =
              ValueNotifier([]);
          return SizedBox(
            height: 400.h,
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                Text('Available Tickets:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 20.spMin,
                    )),
                if (ticket.isEmpty)
                  SizedBox(
                    height: 290.h,
                    child: const Center(
                      child: Text('No Tickets Available at the moment'),
                    ),
                  ),
                if (ticket.isNotEmpty)
                  Expanded(
                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: ticket.length,
                      itemBuilder: (context, index) {
                        Map<String, dynamic> selected = {};
                        final Ticket tickets = ticket[index];

                        return ValueListenableBuilder(
                            valueListenable: purchasedTickets,
                            builder: (context, purchasedTicket, _) {
                              for (var item in purchasedTicket) {
                                if (item["ticket_id"] == tickets.id) {
                                  selected = item;
                                  break;
                                }
                              }
                              return Container(
                                padding: const EdgeInsets.all(8),
                                margin: EdgeInsets.all(8.spMin),
                                decoration: BoxDecoration(
                                  border:
                                      Border.all(color: Colors.grey, width: 1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text(tickets.title ?? '',
                                              style: const TextStyle(
                                                  fontWeight: FontWeight.bold)),
                                          Text(
                                              "${tickets.ticketType ?? ''} ${(tickets.ticketType ?? "") == "GROUP" ? (tickets.groupSize ?? 0) == 1 ? " - ${tickets.groupSize} person" : " - ${tickets.groupSize} people" : ''}"),
                                        ],
                                      ),
                                    ),
                                    tickets.ticketType == "FREE" ||
                                            tickets.ticketType == "RESERVE"
                                        ? Obx(
                                            () => MyButton(
                                              onClick: () {
                                                Get.back();
                                                Get.dialog(ReserveDetails(
                                                    eventId: eventId,
                                                    ticketId: tickets.id!));
                                              },
                                              showLoading:
                                                  controller.isReserving.value,
                                              label: 'Reserve',
                                            ),
                                          )
                                        : Column(
                                            children: [
                                              Row(
                                                children: [
                                                  CircleAvatar(
                                                    child: IconButton(
                                                        icon: const Icon(
                                                            Icons.remove),
                                                        onPressed: () {
                                                          try {
                                                            if (selected[
                                                                    'quantity'] >
                                                                0) {
                                                              selected[
                                                                  'quantity'] -= 1;
                                                              selected[
                                                                      'amount'] =
                                                                  selected[
                                                                          'quantity'] *
                                                                      tickets
                                                                          .price;
                                                              purchasedTickets
                                                                      .value =
                                                                  purchasedTickets
                                                                      .value
                                                                      .map((e) => e['ticket_id'] ==
                                                                              selected["ticket_id"]
                                                                          ? selected
                                                                          : e)
                                                                      .toList();
                                                            } else {
                                                              purchasedTickets
                                                                      .value =
                                                                  purchasedTickets
                                                                      .value
                                                                      .where((e) =>
                                                                          e['ticket_id'] !=
                                                                          selected[
                                                                              'ticket_id'])
                                                                      .toList();
                                                            }
                                                          } catch (e) {
                                                            Get.snackbar(
                                                                'Error', "");
                                                          }
                                                        }),
                                                  ),
                                                  SizedBox(
                                                    width: 8.w,
                                                  ),
                                                  Text(
                                                      '${selected["quantity"] ?? 0}'),
                                                  SizedBox(
                                                    width: 8.w,
                                                  ),
                                                  CircleAvatar(
                                                    child: IconButton(
                                                        icon: const Icon(
                                                            Icons.add),
                                                        onPressed: () {
                                                          try {
                                                            if (selected[
                                                                    'quantity'] >
                                                                0) {
                                                              selected[
                                                                  'quantity'] += 1;
                                                              selected[
                                                                      'amount'] =
                                                                  selected[
                                                                          'quantity'] *
                                                                      tickets
                                                                          .price;
                                                              purchasedTickets
                                                                      .value =
                                                                  purchasedTickets
                                                                      .value
                                                                      .map((e) => e['ticket_id'] ==
                                                                              selected["ticket_id"]
                                                                          ? selected
                                                                          : e)
                                                                      .toList();
                                                            } else {
                                                              purchasedTickets
                                                                  .value = [
                                                                ...purchasedTickets
                                                                    .value,
                                                                {
                                                                  "ticket_id":
                                                                      tickets
                                                                          .id,
                                                                  "quantity": 1,
                                                                  "amount":
                                                                      tickets
                                                                          .price
                                                                }
                                                              ];
                                                            }
                                                          } catch (e) {
                                                            purchasedTickets
                                                                .value = [
                                                              ...purchasedTickets
                                                                  .value,
                                                              {
                                                                "ticket_id":
                                                                    tickets.id,
                                                                "quantity": 1,
                                                                "amount":
                                                                    tickets
                                                                        .price
                                                              }
                                                            ];
                                                          }
                                                        }),
                                                  ),
                                                ],
                                              ),
                                              Text('${selected['amount'] ?? 0}')
                                            ],
                                          ),
                                  ],
                                ),
                              );
                            });
                      },
                    ),
                  ),
                const SizedBox(height: 10),
                ValueListenableBuilder(
                    valueListenable: purchasedTickets,
                    builder: (context, purchasedTickers, _) {
                      num total = 0;
                      for (var item in purchasedTickers) {
                        total += item['amount'];
                      }
                      return Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: MyButton(
                            isGreyedOut: total == 0,
                            label:
                                'Checkout ${FormattedCurrency().getFormattedCurrency(total)}',
                            onClick: () async {
                              if (total > 0) {
                                bool? results = await showModalBottomSheet(
                                    context: context,
                                    isScrollControlled: true,
                                    // transitionAnimationController: animationController,
                                    shape: const RoundedRectangleBorder(
                                      borderRadius: BorderRadius.vertical(
                                          top: Radius.circular(16.0)),
                                    ),
                                    builder: (context) => PaymentsPage(
                                          tickets: purchasedTickers.map((e) {
                                            return {
                                              'ticket_id': e['ticket_id'],
                                              'amount': e['amount']
                                            };
                                          }).toList(),
                                          eventId: eventId,
                                          price: total.toInt(),
                                        ));
                                if (results != null && results) {
                                  Navigator.pop(context);
                                  const snackBar = SnackBar(
                                    content: Text('Successfully Payed!'),
                                    backgroundColor: primaryColor,
                                    behavior: SnackBarBehavior.floating,
                                    duration: Duration(seconds: 2),
                                  );
                                  ScaffoldMessenger.of(context)
                                      .showSnackBar(snackBar);
                                } else if (results == null) {
                                } else {
                                  const snackBar = SnackBar(
                                    content:
                                        Text('Error occurred while paying'),
                                    backgroundColor: Colors.red,
                                    behavior: SnackBarBehavior.floating,
                                    duration: Duration(seconds: 2),
                                  );
                                  ScaffoldMessenger.of(context)
                                      .showSnackBar(snackBar);
                                }
                              }
                            }),
                      );
                    })
              ],
            ),
          );
        },
      ),
    );
  }
}

class ReserveDetails extends StatelessWidget {
  final int eventId, ticketId;
  const ReserveDetails(
      {super.key, required this.eventId, required this.ticketId});

  @override
  Widget build(BuildContext context) {
    TextEditingController name = TextEditingController(),
        email = TextEditingController(),
        _phoneNumber = TextEditingController();

    final controller = Get.put(ViewEventController());
    return AlertDialog(
      title: const Text('Reserve Ticket'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              children: [
                MyTextFieldwValidator(
                  controller: name,
                  title: 'full names',
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: InternationalPhoneNumberInput(
                    onInputChanged: (PhoneNumber number) {
                      print("${number.phoneNumber}");
                      _phoneNumber.text =
                          number.phoneNumber.toString().replaceAll("+", '');
                    },
                    onInputValidated: (bool value) {},
                    selectorConfig: const SelectorConfig(
                      selectorType: PhoneInputSelectorType.BOTTOM_SHEET,
                      useBottomSheetSafeArea: true,
                    ),
                    ignoreBlank: false,
                    selectorTextStyle: const TextStyle(color: Colors.black),
                    initialValue: PhoneNumber(isoCode: 'KE', dialCode: '+254'),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Phone Number is required';
                      }
                      return null;
                    },
                    formatInput: true,
                    keyboardType: const TextInputType.numberWithOptions(
                        signed: true, decimal: true),
                    inputBorder: const OutlineInputBorder(),
                    inputDecoration: InputDecoration(
                      hintText: "eg. 0712345678",
                      //filled: true,
                      // fillColor: Colors.white70,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(
                          width: 0.5,
                          color: Colors.grey,
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(
                          width: 0.5,
                          color: Colors.grey,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          width: 1,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    ),
                  ),
                ),
                MyTextFieldwValidator(
                  controller: email,
                  title: 'email',
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () {
            Get.back();
          },
          child: const Text('Cancel'),
        ),
        Obx(
          () => MyButton(
            width: 155.w,
            showLoading: controller.isReserving.value,
            label: 'reserve',
            onClick: () async {
              controller.reserve(
                  eventId: "$eventId",
                  phoneNumber: _phoneNumber.text,
                  firstname: name.text,
                  email: email.text,
                  tickets: [
                    {
                      "ticket_id": ticketId,
                    }
                  ]);
            },
          ),
        ),
      ],
    );
  }
}
