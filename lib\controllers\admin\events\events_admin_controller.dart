
import 'dart:io' as io; 
// import 'dart:html' show AnchorElement, document;
import 'package:excel/excel.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/models/transac_kitt_model.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/custom_logger.dart';
import 'package:onekitty/services/http_service.dart';
import 'package:path_provider/path_provider.dart';
import '../../../models/events/events_model.dart';

class EventsAdminController extends GetxController implements GetxService {
  final events = <Event>[].obs;
  final logger = Logger(filter: CustomLogFilter());
  final HttpService apiProvider = Get.find();
  RxBool isLoading = false.obs;
  final size = 15.obs;
  final maxPage = 0.obs;
  final totalPages = 0.obs;
  final isLast = false.obs;
  final isFirst = true.obs;
  final currentPage = 0.obs;
  final startDate = ''.obs;
  final endDate = ''.obs;
  final phoneNumber = ''.obs;
  final search = ''.obs;
  final frequency = ''.obs;
  final eventId = ''.obs;
  final transactions = <TransactionModel>[].obs;

  final transmaxPage = 0.obs;
  final transTotalPages = 0.obs;
  final transIsLast = false.obs;
  final transIsFirst = true.obs;
  final transCurrentPage = 0.obs;
  final transize = 30.obs;
  final kittyId = ''.obs;
  clear() {
    maxPage(0);
    totalPages(0);
    isLast(false);
    isFirst(true);
    currentPage(0);
  }

  clearTrans() {
    transactions([]);
    transmaxPage(0);
    transTotalPages(0);
    transIsLast(false);
    transIsFirst(true);
    transCurrentPage(0);
  }
  final allEvents = <Event>[].obs;
  final steps = 0.obs;
  final totalSteps = 0.obs;
  final isFetchingAllChamas = false.obs;
  final error = ''.obs;
  final isGeneratingExcel = false.obs;
   Future fetchAllEventsWithoutPagination() async {
    steps.value = 0;
    totalSteps.value = 0;    
    error('');
    allEvents.clear();
    startDate.value = '';
    endDate.value = '';
    phoneNumber.value = '';
    search.value = '';
    frequency.value = '';
    kittyId.value = '';
    try {
      isLoading(true);
      int page = 0;
      bool lastPage = false;    
      while (!lastPage) {
        
        var response = await apiProvider.request(
          method: Method.GET,
          url:
              "${ApiUrls.GETALLEVENTS}?page=$page&size=${size.value}&search=${search.value}&frequency=${frequency.value}&kitty_id=${kittyId.value}&start_date=${startDate.value}&end_date=${endDate.value}",
        );
        steps.value++;
        if (response.data['status'] ?? false) {
          var fetchedChamas = (response.data['data']['items'] as List)
              .map((e) => Event.fromJson(e))
              .toList();
          allEvents.addAll(fetchedChamas);
          lastPage = response.data['data']['last'];
          totalSteps.value = response.data['data']['total_pages'];
          page++;
        } else {
          
          throw Exception('Failed to load chamas');
        }
      }
      events(allEvents);
    } catch (e) {
      
      logger.e('Error fetching all chamas without pagination: $e');
      error('Error fetching all chamas without pagination: $e');
    } finally {
      isLoading(false);
    }
  }
 
  Future generateExcel() async {
    isGeneratingExcel(true);
    try {
      final excel = Excel.createExcel();
      final sheet = excel['Events'];
      // Add headers
      final headers = [
        'ID',
        'Name',
        'Description',
        'Start Date',
        'End Date',
        'Location',
        'Status'
      ];
      sheet.appendRow(headers.map((header) => TextCellValue(header)).toList());
      // Add data
      for (var event in allEvents) {
        final row = [
          TextCellValue(event.id.toString()),
          TextCellValue(event.title),
          TextCellValue(event.description),
          TextCellValue(event.startDate.toString()),
          TextCellValue(event.endDate.toString()),
          TextCellValue(event.longitude.toString()),
          TextCellValue(event.status.toString())
        ];    
        sheet.appendRow(row);
      }
      final fileBytes = excel.encode();
        if (fileBytes != null) {
          // TODO IF WEB
          // if(GetPlatform.isWeb){
          //   // Create a blob URL and trigger download
          //   final blob = excel.save();
          //   if (blob != null) {
          //     final anchor = AnchorElement(href: 'data:application/octet-stream;base64,${base64Encode(blob)}')
          //       ..setAttribute('download', 'events.xlsx')
          //       ..style.display = 'none';
          //     document.body?.append(anchor);
          //     anchor.click();
          //     anchor.remove();
          //   }
          //   Get.snackbar('Success', 'Excel file downloaded successfully');
          //   return;
          // }
          try {
            // Create a temporary file
            final tempDir = await getTemporaryDirectory();
            final tempFile = io.File('${tempDir.path}/events.xlsx');
            await tempFile.writeAsBytes(fileBytes);
            // Use system file picker to choose save location (requires file_picker package)
            final FilePicker filePicker = FilePicker.platform;
            String? outputFile = await filePicker.saveFile(
              dialogTitle: 'Save Excel File',
              fileName: 'events.xlsx',
              allowedExtensions: ['xlsx'],
              type: FileType.custom,
            );
            if (outputFile != null) {
              // Copy the temp file to selected location
              await tempFile.copy(outputFile);
              await tempFile.delete(); // Clean up temp file
              Get.snackbar('Success', 'Excel file saved successfully');
            }
          } catch (e) {
            logger.e('Error saving file: $e');
            Get.snackbar('Error', 'Failed to save file: $e');
          }
        }
    } catch (e) {
      logger.e('Error generating Excel file: $e');
      Get.snackbar('Error', 'Error generating Excel file: $e');
    }finally{
      isGeneratingExcel(false);
    }
  }


  Future editEvent(Map<String, dynamic> params) async {
    try {
      isLoading(true);
      var response = await apiProvider.request(
        method: Method.PUT,
        url: ApiUrls.editEventAdmin,
        params: params,
      );
      if (response.data['status'] ?? false) {
        await getEvents(currentPage.value);
        Get.snackbar('Success', 'Event updated successfully');
      } else {
        throw Exception(response.data['message'] ?? 'Failed to update event');
      }
    } catch (e) {
      logger.e('Error updating event: $e');
      Get.snackbar('Error', 'Error updating event: $e');
    } finally {
      isLoading(false);
    }
  }


 

  Future fetchTransactions({
    required int eventId,
    required int page,
  }) async {
    try {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        isLoading(true);
      });
      final response = await apiProvider.request(
        method: Method.GET,
        url:
            "${ApiUrls.EVENTTRANSACTIONS}?event_id=$eventId&page=$page&size=${size.value}",
      );
      if (response.data != null && response.data['data'] != null) {
        final returnedData = response.data['data']['items'] as List;
        transactions.value = returnedData
            .map((item) => TransactionModel.fromJson(item))
            .toList();
        transmaxPage.value = response.data['data']['total_pages'];
        transTotalPages.value = response.data['data']['total_pages'];
        transIsLast.value = response.data['data']['last'];
        transIsFirst.value = response.data['data']['first'];
        transCurrentPage.value = response.data['data']['page'];
      } else {
        throw Exception('Failed to load transactions');
      }
    } catch (e) {
      Get.snackbar('Error', "$e");
    } finally {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        isLoading(false);
      });
    }
  }

  Future getEvents(int page) async {
    try {
      isLoading(true);
      var response = await apiProvider.request(
        method: Method.GET,
        url:
            "${ApiUrls.GETALLEVENTS}?page=$page&size=${size.value}&search=${search.value}&phone_number=${phoneNumber.value}&start_date=${startDate.value}&end_date=${endDate.value}&kitty_id=${kittyId.value}&frequency=${frequency.value}",
      );
      if (response.data['status'] ?? false) {
        events((response.data['data']['items'] as List)
            .map((e) => Event.fromJson(e))
            .toList());
        currentPage.value = response.data['data']['page'];
        maxPage.value = response.data['data']['total_pages'];
        totalPages.value = response.data['data']['total_pages'];
        isLast.value = response.data['data']['last'];
        isFirst.value = response.data['data']['first'];
      } else {
        throw Exception('Failed to load chamas');
      }
      print(response.data);
    } catch (e) {
      logger.e('Error fetching chamas: $e');
    } finally {
      isLoading(false);
    }
  }
}
