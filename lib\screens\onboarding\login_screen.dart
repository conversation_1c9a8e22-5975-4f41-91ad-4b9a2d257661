import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart'; 
import 'package:get/get.dart';
import 'package:onekitty/controllers/auth_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart' show NavRoutes;
import 'package:onekitty/screens/onboarding/sign_up.dart';
import 'package:onekitty/utils/asset_urls.dart';
import 'package:onekitty/utils/int_phone_no.dart' show CountryPickerWidget;
import 'package:onekitty/utils/responsiveness.dart';


class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _authController = Get.find<AuthenticationController>();
  bool _obscurePassword = true;
  String _phoneNumber = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ResponsiveLayout(
        mobile: _buildMobileLayout(context),
        desktop: _buildDesktopLayout(context),
      ),
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(24.w),
      child: _buildLoginContent(context),
    );
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return Center(
      child: Container(
        constraints: const BoxConstraints(maxWidth: 1200),
        padding: EdgeInsets.all(48.w),
        child: Row(
          children: [
            Expanded(child: _buildLoginContent(context)),
            VerticalDivider(indent: 20, endIndent: 20, color: Colors.grey[300]),
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(48.w),
                child: Image.asset(
                  AssetUrl.logo7,
                  fit: BoxFit.contain,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoginContent(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildHeader(),
          SizedBox(height: 32.h),
          _buildPhoneField(),
          SizedBox(height: 24.h),
          _buildPasswordField(),
          SizedBox(height: 32.h),
          _buildLoginButton(),
          SizedBox(height: 24.h),
          _buildFooterLinks(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Text(
          'Welcome Back!',
          style: TextStyle(
            fontSize: 32.spMin,
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
        ),
        SizedBox(height: 8.h),
        Text(
          'Sign in to continue your journey',
          style: TextStyle(
            fontSize: 16.sp,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildPhoneField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Phone Number',
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 8.h),
        CountryPickerWidget(
          textEditingController: _phoneController,
          onInputChanged: (number) {
            _phoneNumber = number;
          },
          // selectorConfig: const SelectorConfig(
          //   selectorType: PhoneInputSelectorType.DROPDOWN,
          // ),
          // ignoreBlank: false,
          // autoValidateMode: AutovalidateMode.onUserInteraction,
          // initialValue: _phoneNumber,
          // textFieldController: _phoneController,
          // formatInput: true,
          // keyboardType: const TextInputType.numberWithOptions(
          //   signed: true,
          //   decimal: true,
          // ),
          // inputDecoration: InputDecoration(
          //   //filled: true,
          //   // fillColor: Colors.grey[50],
          //   border: OutlineInputBorder(
          //     borderRadius: BorderRadius.circular(12),
          //     borderSide: BorderSide.none,
          //   ),
          //   hintText: 'Enter phone number',
          //   prefixIcon: const Icon(Icons.phone_outlined),
          //  ),
        ),
      ],
    );
  }

  Widget _buildPasswordField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Password',
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 8.h),
        TextFormField(
          controller: _passwordController,
          onFieldSubmitted: (_) => _handleLogin(),
          obscureText: _obscurePassword,
          decoration: InputDecoration(
            //filled: true,
            // fillColor: Colors.grey[50],
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            hintText: 'Enter password',
            prefixIcon: const Icon(Icons.lock_outline),
            suffixIcon: IconButton(
              icon: Icon(
                _obscurePassword ? Icons.visibility_off : Icons.visibility,
              ),
              onPressed: () =>
                  setState(() => _obscurePassword = !_obscurePassword),
            ),
          ),
          validator: (value) =>
              (value?.isEmpty ?? true) ? 'Required field' : null,
        ),
      ],
    );
  }

  Widget _buildLoginButton() {
    return Obx(
      () => ElevatedButton(
        onPressed:
            _authController.isLoginloading.value ? null : () => _handleLogin(),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          padding: EdgeInsets.symmetric(vertical: 16.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0,
        ),
        child: _authController.isLoginloading.value
            ? const CircularProgressIndicator(color: Colors.white)
            : Text(
                'Sign In',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }

  Widget _buildFooterLinks() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        TextButton(
          onPressed: () {
            Get.to(() => SignUpPage(isForgotPasswd: true));
          },
          child: const Text('Forgot Password?'),
        ),
        SizedBox(width: 16.w),
        TextButton(
          onPressed: () {
            Get.to(() => SignUpPage(isForgotPasswd: false));
          },
          child: const Text('Create Account'),
        ),
      ],
    );
  }

  Future<void> _handleLogin() async {
    if (_formKey.currentState!.validate()) {
      final success = await _authController.login(
        _phoneNumber,
        _passwordController.text,
      );

      if (success) {
        if (success) {
          
          Get.offAllNamed(NavRoutes.bottomNavSection);
        }
      } else {
        Get.snackbar(
          'Login Failed',
          _authController.apiMessage.string,
          backgroundColor: Colors.redAccent,
          colorText: Colors.white,
        );
      }
    }
  }
}
