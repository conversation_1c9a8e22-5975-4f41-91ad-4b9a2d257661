// To parse this JSON data, do
//
//     final signatoryTransactionsModel = signatoryTransactionsModelFromJson(jsonString);

import 'dart:convert';

import 'package:onekitty/models/events/signatory_transaction_model.dart';

SignatoryTransactionsModel signatoryTransactionsModelFromJson(String str) =>
    SignatoryTransactionsModel.fromJson(json.decode(str));

String signatoryTransactionsModelToJson(SignatoryTransactionsModel data) =>
    json.encode(data.toJson());

class SignatoryTransactionsModel {
  bool? status;
  String? message;
  Data? data;

  SignatoryTransactionsModel({
    this.status,
    this.message,
    this.data,
  });

  factory SignatoryTransactionsModel.fromJson(Map<String, dynamic> json) =>
      SignatoryTransactionsModel(
        status: json["status"],
        message: json["message"],
        data: Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data!.toJson(),
      };
}

class Data {
  List<SignatoryTransaction>? transactions;
  int? page;
  int? size;
  int? maxPage;
  int? totalPages;
  int? total;
  bool? last;
  bool? first;
  int? visible;

  Data({
    this.transactions,
    this.page,
    this.size,
    this.maxPage,
    this.totalPages,
    this.total,
    this.last,
    this.first,
    this.visible,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        transactions: List<SignatoryTransaction>.from(
            json["items"].map((x) => SignatoryTransaction.fromJson(x))),
        page: json["page"],
        size: json["size"],
        maxPage: json["max_page"],
        totalPages: json["total_pages"],
        total: json["total"],
        last: json["last"],
        first: json["first"],
        visible: json["visible"],
      );

  Map<String, dynamic> toJson() => {
        "items": List<dynamic>.from(transactions!.map((x) => x.toJson())),
        "page": page,
        "size": size,
        "max_page": maxPage,
        "total_pages": totalPages,
        "total": total,
        "last": last,
        "first": first,
        "visible": visible,
      };
}

class ApprovedBy {
  int? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;
  int? userId;
  String? profileUrl;
  String? phoneNumber;
  String? countryCode;
  String? firstName;
  String? secondName;
  String? role;
  dynamic beneficiary;
  int? chamaId;
  int? receivingOrder;
  String? status;

  ApprovedBy({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.userId,
    this.profileUrl,
    this.phoneNumber,
    this.countryCode,
    this.firstName,
    this.secondName,
    this.role,
    this.beneficiary,
    this.chamaId,
    this.receivingOrder,
    this.status,
  });

  factory ApprovedBy.fromJson(Map<String, dynamic> json) => ApprovedBy(
        id: json["ID"],
        createdAt: DateTime.parse(json["CreatedAt"]),
        updatedAt: DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        userId: json["user_id"],
        profileUrl: json["profile_url"],
        phoneNumber: json["phone_number"],
        countryCode: json["country_code"],
        firstName: json["first_name"],
        secondName: json["second_name"],
        role: json["role"],
        beneficiary: json["beneficiary"],
        chamaId: json["chama_id"],
        receivingOrder: json["receiving_order"],
        status: json["status"],
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt!.toIso8601String(),
        "UpdatedAt": updatedAt!.toIso8601String(),
        "DeletedAt": deletedAt,
        "user_id": userId,
        "profile_url": profileUrl,
        "phone_number": phoneNumber,
        "country_code": countryCode,
        "first_name": firstName,
        "second_name": secondName,
        "role": role,
        "beneficiary": beneficiary,
        "chama_id": chamaId,
        "receiving_order": receivingOrder,
        "status": status,
      };
}

class MetaData {
  int? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;
  String? reason;

  MetaData({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.reason,
  });

  factory MetaData.fromJson(Map<String, dynamic> json) => MetaData(
        id: json["ID"],
        createdAt: DateTime.parse(json["CreatedAt"]),
        updatedAt: DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        reason: json["reason"],
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt!.toIso8601String(),
        "UpdatedAt": updatedAt!.toIso8601String(),
        "DeletedAt": deletedAt,
        "reason": reason,
      };
}
