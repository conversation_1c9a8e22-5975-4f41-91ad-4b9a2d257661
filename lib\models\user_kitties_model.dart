// To parse this JSON data, do
//
//     final userKitties = userKittiesFromJson(jsonString);

import 'dart:convert';

import 'package:onekitty/controllers/kitty_controller.dart';

UserKitties userKittiesFromJson(String str) =>
    UserKitties.fromJson(json.decode(str));

String userKittiesToJson(UserKitties data) => json.encode(data.toJson());

class UserKitties {
  bool? status;
  String? message;
  Data? data;

  UserKitties({
    this.status,
    this.message,
    this.data,
  });

  factory UserKitties.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return UserKitties(); // Return a default object if json is null
    }

    return UserKitties(
      status: json["status"],
      message: json["message"],
      data: json["data"] != null ? Data.fromJson(json["data"]) : null,
    );
  }

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data?.toJson(),
      };
}

class Data {
  List<UserKitty> userKitties;

  Data({
    required this.userKitties,
  });

  factory Data.fromJson(Map<String, dynamic>? json) {
    if (json == null) return Data(userKitties: []);

    return Data(
      userKitties: (json["user_kitties"] as List<dynamic>? ?? [])
          .map((e) => UserKitty.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() => {
        "user_kitties": List<dynamic>.from(userKitties.map((x) => x.toJson())),
      };
}

class UserKitty {
  Kitty? kitty;
  String? kittyStatus;
  String? kittBeneficiaryChannel;
  String? kittyType;

  UserKitty({
    this.kitty,
    this.kittyStatus,
    this.kittBeneficiaryChannel,
    this.kittyType,
  });

  factory UserKitty.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return UserKitty(
        kitty: Kitty(),
        kittyStatus: '',
        kittBeneficiaryChannel: '',
        kittyType: '',
      );
    }

    return UserKitty(
      kitty: json["kitty"] != null ? Kitty.fromJson(json["kitty"]) : Kitty(),
      kittyStatus: json["kitty_status"] ?? '',
      kittBeneficiaryChannel: json["kitt_beneficiary_channel"] ?? '',
      kittyType: json["kitty_type"] ?? '',
    );
  }

  Map<String, dynamic> toJson() => {
        "kitty": kitty?.toJson(),
        "kitty_status": kittyStatus,
        "kitt_beneficiary_channel": kittBeneficiaryChannel,
        "kitty_type": kittyType,
      };
}

class Kitty {
  int? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;
  String? title;
  String? description;
  String? beneficiaryAccount;
  String? beneficiaryChannel;
  String? beneficiaryPhoneNumber;
  DateTime? endDate;
  dynamic balance;
  dynamic limit;
  int? settlementType;
  String? bennefAccRef;
  int? refererMerchantCode;
  int? kittyType;
  String? phoneNumber;
  List<KittyMediaModel>? media;
  bool? hasSignatories;

  Kitty(
      {this.id,
      this.createdAt,
      this.updatedAt,
      this.deletedAt,
      this.title,
      this.description,
      this.beneficiaryAccount,
      this.beneficiaryChannel,
      this.beneficiaryPhoneNumber,
      this.endDate,
      this.limit,
      this.refererMerchantCode,
      this.settlementType,
      this.balance,
      this.bennefAccRef,
      this.kittyType,
      this.phoneNumber,
      this.media,
      this.hasSignatories});

  factory Kitty.fromJson(Map<String, dynamic>? json) {
    if (json == null) return Kitty();

    return Kitty(
      hasSignatories: json['has_signatory_transaction'] ?? false,
      id: json["ID"],
      createdAt:
          json["CreatedAt"] == null ? null : DateTime.parse(json["CreatedAt"]),
      updatedAt:
          json["UpdatedAt"] == null ? null : DateTime.parse(json["UpdatedAt"]),
      deletedAt: json["DeletedAt"],
      title: json["title"],
      description: json["description"],
      beneficiaryAccount: json["beneficiary_account"],
      beneficiaryChannel: json["beneficiary_channel"],
      beneficiaryPhoneNumber: json["beneficiary_phone_number"],
      endDate:
          json["end_date"] == null ? null : DateTime.parse(json["end_date"]),
      limit: json["limit"],
      refererMerchantCode: json["referer_merchant_code"],
      settlementType: json["settlement_type"],
      balance: json["balance"],
      bennefAccRef: json["beneficiary_account_ref"],
      kittyType: json["kitty_type"],
      phoneNumber: json["phone_number"],
      media: (json["media"] as List<dynamic>?)
              ?.map((item) => KittyMediaModel.fromJson(item))
              .toList() ??
          <KittyMediaModel>[],
    );
  }

  Map<String, dynamic> toJson() => {
        "ID": id,
        "has_signatory_transaction": hasSignatories,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "title": title,
        "description": description,
        "beneficiary_account": beneficiaryAccount,
        "beneficiary_channel": beneficiaryChannel,
        "beneficiary_phone_number": beneficiaryPhoneNumber,
        "end_date": endDate?.toIso8601String(),
        "limit": limit,
        "referer_merchant_code": refererMerchantCode,
        "balance": balance,
        "phone_number": phoneNumber,
        "beneficiary_account_ref": bennefAccRef,
        "kitty_type": kittyType,
        "settlement_type": settlementType,
        "media": media,
      };
}
