// import 'package:data_table_2/data_table_2.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:intl/intl.dart';
// import 'package:onekitty/controllers/admin/chama/dashboard_controller.dart';
// import 'package:onekitty/models/chama/chama_model.dart';

// class BeneficiaryTable extends StatelessWidget {
//   const BeneficiaryTable({super.key});

//   void _showBeneficiaryDetails(BuildContext context, NextBeneficiary beneficiary) {
//     showDialog(
//       context: context,
//       builder: (context) {
//         return AlertDialog(
//           title: const Text('Beneficiary Details'),
//           content: SingleChildScrollView(
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 Text('ID: ${beneficiary.beneficiaries ?? "N/A"}'),
//                 Text(
//                     'Created At: ${beneficiary?.createdAt != null ? DateFormat('yyyy-MM-dd').format(beneficiary.createdAt!) : "N/A"}'),
//                 Text(
//                     'Updated At: ${beneficiary?.updatedAt != null ? DateFormat('yyyy-MM-dd').format(beneficiary.updatedAt!) : "N/A"}'),
//                 Text('Title: ${beneficiary?.title ?? "N/A"}'),
//                 Text('User ID: ${beneficiary?.userId ?? "N/A"}'),
//                 Text('Chama ID: ${beneficiary?.chamaId ?? "N/A"}'),
//                 Text('Member ID: ${beneficiary?.memberId ?? "N/A"}'),
//                 Text('Transfer Mode: ${beneficiary?.transferMode ?? "N/A"}'),
//                 Text('Percentage: ${beneficiary?.percentage ?? "N/A"}'),
//                 Text(
//                     'Beneficiary Type: ${beneficiary?.beneficiaryType ?? "N/A"}'),
//                 Text('Channel Name: ${beneficiary?.channelName ?? "N/A"}'),
//                 Text('Channel: ${beneficiary?.channel ?? "N/A"}'),
//                 Text('Status: ${beneficiary?.status ?? "N/A"}'),
//                 Text('Account Number: ${beneficiary?.accountNumber ?? "N/A"}'),
//                 Text(
//                     'Account Number Ref: ${beneficiary?.accountNumberRef ?? "N/A"}'),
//               ],
//             ),
//           ),
//           actions: [
//             TextButton(
//               onPressed: () => Navigator.of(context).pop(),
//               child: const Text('Close'),
//             ),
//           ],
//         );
//       },
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     final controller = Get.find<ChamaAdminController>();
//     return DataTable2(
//       columnSpacing: 12,
//       horizontalMargin: 12,
//       minWidth: 600,
//       columns: [
//         const DataColumn2(label: Text('ID'), size: ColumnSize.S),
//         const DataColumn2(label: Text('Name'), size: ColumnSize.M),
//         const DataColumn2(label: Text('Created At'), size: ColumnSize.M),
//         const DataColumn2(label: Text('Transfer Mode'), size: ColumnSize.M),
//         const DataColumn2(label: Text('Status'), size: ColumnSize.S),
//         const DataColumn2(label: Text('View'), size: ColumnSize.S),
//       ],
//       rows: controller.beneficiaries.map((ben) {
//         final beneficiary = ben.member;

//         return DataRow2(cells: [
//           DataCell(Text(beneficiary?.id.toString() ?? 'N/A')),
//           DataCell(Text(beneficiary?.firstName ?? 'N/A')),
//           DataCell(Text(beneficiary?.createdAt != null
//               ? DateFormat('yyyy-MM-dd')
//                   .format(beneficiary?.createdAt ?? DateTime.now())
//               : 'N/A')),
//           DataCell(Text(beneficiary?.role ?? 'N/A')),
//           DataCell(Text(beneficiary?.status ?? 'N/A')),
//           DataCell(
//             IconButton(
//               icon: const Icon(Icons.visibility),
//               onPressed: () => _showBeneficiaryDetails(context, ben),
//             ),
//           ),
//         ]);
//       }).toList(),
//     );
//   }
// }
