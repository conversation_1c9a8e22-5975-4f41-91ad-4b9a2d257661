import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/admin/events/block_event_controller.dart';
import 'package:onekitty/utils/responsive_size.dart';

class BlockEventPage extends StatelessWidget {
  final int eventId;
  const BlockEventPage({super.key, required this.eventId});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(BlockEventController());
    return Scaffold(
        appBar: AppBar(
          actions: [
            IconButton(
              icon: const Icon(Icons.more_vert),
              onPressed: () {
                showMenu(
                  context: context,
                  position: const RelativeRect.fromLTRB(100, 0, 0, 0),
                  items: [
                    PopupMenuItem(
                      child: const Text('Unblock Event'),
                      onTap: () => controller.unblockEvent(eventId),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Why do you want to block this event?',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
              const SizedBox(height: 16),
              SizedBox(
                width: ResponsiveSize.width(context, 520),
                child: TextField(
                  controller: controller.reasonController,
                  maxLines: 3,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    hintText: 'Enter your reason here...',
                  ),
                ),
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Obx(
                    () => ElevatedButton.icon(
                      onPressed: () async => !controller.isUploadingImage.value
                          ? await controller.pickImages()
                          : null,
                      icon: controller.isUploadingImage.value
                          ? const SizedBox() : const Icon(Icons.add_photo_alternate),
                      label: controller.isUploadingImage.value
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                valueColor:
                                    AlwaysStoppedAnimation(Colors.white),
                                strokeWidth: 2,
                              ),
                            )
                          : const Text('Add Photos'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Obx(() =>
                      Text('${controller.images.length} photos selected')),
                ],
              ),
              const SizedBox(height: 16),
              Obx(
                () => controller.images.isEmpty
                    ? const SizedBox()
                    : LayoutBuilder(
                        builder: (context, constraints) {
                          final crossAxisCount =
                              (constraints.maxWidth / 120).floor();
                          return Obx(
                            () => GridView.builder(
                              shrinkWrap: true,
                              gridDelegate:
                                  SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount:
                                    crossAxisCount > 0 ? crossAxisCount : 1,
                                crossAxisSpacing: 8,
                                mainAxisSpacing: 8,
                                childAspectRatio: 1,
                              ),
                              itemCount: controller.images.length,
                              itemBuilder: (context,
                                      index) => // Text('${controller.images}')
                                  controller.isUploadingImage.value &&
                                          controller.images.length == index - 1
                                      ? Container(
                                          alignment: Alignment.center,
                                          height: 100,
                                          width: 100,
                                          child:
                                              const CircularProgressIndicator(),
                                        )
                                      : Stack(
                                          alignment: Alignment.topRight,
                                          children: [
                                            Image.memory(
                                              controller.images[index]['path'],
                                              fit: BoxFit.cover,
                                              height: 100,
                                              width: 100,
                                            ),
                                            Positioned(
                                              top: 0,
                                              right: 0,
                                              child: Container(
                                                padding:
                                                    const EdgeInsets.all(2),
                                                height: 35,
                                                width: 35,
                                                decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(12),
                                                  color: Colors.grey
                                                      .withOpacity(0.6),
                                                ),
                                                child: IconButton(
                                                    icon: const Icon(
                                                      size: 20,
                                                      Icons.delete_outline,
                                                      color: Colors.white,
                                                    ),
                                                    onPressed: () => controller
                                                        .images
                                                        .removeAt(index)),
                                              ),
                                            ),
                                          ],
                                        ),
                            ),
                          );
                        },
                      ),
              ),
              const Spacer(),
              Align( 
                alignment: Alignment.center,
                child: SizedBox( 
                  width: ResponsiveSize.width(context, 300),
                  child: Obx(
                    () => ElevatedButton(
                      onPressed: controller.isLoading.value
                          ? null
                          : () => controller.submitBlock(eventId),
                      child: controller.isLoading.value
                          ? const CircularProgressIndicator()
                          : const Text('Submit Report'),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ));
  }

  // Add this widget to display uploaded images
  Widget buildImagePreview() {
    return Obx(() => Wrap(
          spacing: 8,
          runSpacing: 8,
          children: Get.find<BlockEventController>()
              .images
              .map((image) => Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Image.network(
                      image["url"],
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) =>
                          const Icon(Icons.error),
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return const Center(
                          child: CircularProgressIndicator(),
                        );
                      },
                    ),
                  ))
              .toList(),
        ));
  }
}
