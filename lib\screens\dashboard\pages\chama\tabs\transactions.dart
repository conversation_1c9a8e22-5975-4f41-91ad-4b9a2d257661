import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onekitty/utils/custom_text_style.dart';

class ChamaTransactions extends StatelessWidget {
  const ChamaTransactions({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 12.h, vertical: 12.h),
      child: Column(
        children: [
      
            
           Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text("Transactions", style: CustomTextStyles.titleSmallGray900),
                GestureDetector(
                  onTap: () {
                    
                  },
                  child: Text("See all",
                      style: CustomTextStyles.titleSmallIndigo500),
                )
              ],
            ),
          
        ],
      ),
    );
  }
}
