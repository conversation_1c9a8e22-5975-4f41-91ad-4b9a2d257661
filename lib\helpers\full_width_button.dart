import 'package:flutter/material.dart';

class FullWidthButton extends StatelessWidget {
  const FullWidthButton(
      {super.key,
      this.color,
      this.textColor,
      this.width,
      this.borderRadius,
      this.fontSize,
      this.buttonHeight,
      required this.buttonText,
      this.fontWeight,
      required this.press});
  final double? buttonHeight;
  final String buttonText;
  final Color? color, textColor;
  final VoidCallback press;
  final double? width;
  final BorderRadius? borderRadius;
  final double? fontSize;
  final FontWeight? fontWeight;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: buttonHeight ?? 50.0,
      //width: width ?? context.width,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: color ?? Theme.of(context).primaryColor,
          shape: RoundedRectangleBorder(
              borderRadius: borderRadius ?? BorderRadius.circular(12)),
          padding: const EdgeInsets.all(12),
        ),
        onPressed: press,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12.0),
          child: Text(
            buttonText,
            style: TextStyle(
                fontSize: fontSize ?? 18,
                color: textColor ??Colors.white,
                fontWeight: fontWeight ?? FontWeight.bold),
          ),
        ),
      ),
    );
  }
}