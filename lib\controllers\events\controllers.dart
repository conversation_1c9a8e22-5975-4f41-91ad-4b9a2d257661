import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:contacts_service/contacts_service.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/models/auth/payments_channels.dart';
import 'package:onekitty/models/events/enums.dart'; 
import 'package:onekitty/services/custom_logger.dart';
import 'package:onekitty/services/http_service.dart';
import 'dart:async';
import 'package:permission_handler/permission_handler.dart';

const primaryColor = Color(0xff4355b6);
Color scaffoldBackgroundColor = const Color(0xfff8fafc);

class GlobalControllers extends GetxController implements GetxService {
  final contacts = <Contact>[].obs;
  final paymentChannels = <PaymentChannels>[].obs;
  Rx<Enums> enums = Enums(
      eventPaymentModes: [],
      eventStatus: [],
      eventTypes: [],
      ticketStatus: [],
      ticketType: []).obs;
  var logger = Logger(filter: CustomLogFilter());
  final HttpService apiProvider = Get.find();

  @override
  void onInit() {
    super.onInit();
    getEnums();
    getPaymentChannels();
    if (Theme.of(Get.context!).platform == TargetPlatform.iOS ||
        Theme.of(Get.context!).platform == TargetPlatform.android) {
      fetchContacts();
    }
  }

  Future<void> getEnums() async {
    // try {
    //   final httpService = HttpService();
    //   final dio = httpService.initializeDio();
    //   final response = await dio.get(ApiUrls.GETENUMS);

    //   if (response.data != null) {
    //     final _returneddata = response.data['data'] as Map<String, dynamic>;
    //     enums = Enums.fromJson(_returneddata).obs;
    //   } else {
    //     logger.v('No data or unexpected response structure');
    //   }
    // } catch (e) {
    //   logger.e('Error fetching enums: $e');
    // }
  }

  Future<void> getPaymentChannels() async {
    // try {
    //   final httpService = HttpService();
    //   final dio = httpService.initializeDio();
    //   final response = await dio.get(ApiUrls.getPaymentChannels);

    //   if (response.data['status'] ?? false) {
    //     final _returneddata = response.data['data'] as List;
    //     paymentChannels(
    //         _returneddata.map((e) => PaymentChannels.fromJson(e)).toList());
    //   } else {
    //     logger.v('No data or unexpected response structure');
    //   }
    // } catch (e) {
    //   logger.e('Error fetching paymentChannels: $e');
    // }
  }

  Future fetchContacts() async {
    if (await Permission.contacts.request().isGranted) {
      Iterable<Contact> contactz = await ContactsService.getContacts();
      contacts.value = contactz.toList();
    } else {
      contacts.value = [];
    }
  }
}
