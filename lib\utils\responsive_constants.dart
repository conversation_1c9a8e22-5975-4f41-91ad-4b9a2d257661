import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ResponsiveBreakpoints {
  static const double mobile = 600;
  static const double tablet = 1024;
  static const double desktop = 1440;
}

class ResponsiveSpacing {
  static double get xs => 4.w;
  static double get sm => 8.w;
  static double get md => 16.w;
  static double get lg => 24.w;
  static double get xl => 32.w;

  static EdgeInsets get paddingAll => EdgeInsets.all(md);
  static EdgeInsets get paddingHorizontal => EdgeInsets.symmetric(horizontal: md);
  static EdgeInsets get paddingVertical => EdgeInsets.symmetric(vertical: md);
}

class ResponsiveTextStyles {
  static TextStyle get heading1 => TextStyle(
    fontSize: 32.sp,
    fontWeight: FontWeight.bold,
    letterSpacing: -0.5,
  );

  static TextStyle get heading2 => TextStyle(
    fontSize: 24.sp,
    fontWeight: FontWeight.bold,
  );

  static TextStyle get body => TextStyle(
    fontSize: 16.sp,
  );

  static TextStyle get caption => TextStyle(
    fontSize: 14.sp,
    color: Colors.grey,
  );
}

class ResponsiveSize {
  static double get buttonHeight => 48.h;
  static double get iconSize => 24.w;
  static double get cardRadius => 12.r;
  static double get appBarHeight => 56.h;
}
