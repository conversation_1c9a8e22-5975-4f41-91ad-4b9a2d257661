<!DOCTYPE html>
<html>

<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project.">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="onekitty_mobile">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png">

  <title>onekitty_mobile</title>
  <link rel="manifest" href="manifest.json">
  <!-- Firebase Configuration -->
<script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js"></script>
<script src="https://www.gstatic.com/firebasejs/8.10.0/firebase-messaging.js"></script>

  <script>
    // The value below is injected by flutter build, do not touch.
    var serviceWorkerVersion = {flutter_service_worker_version};

  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.register('/firebase-messaging-sw.js')
      .then(function (registration) {
        console.log('Service Worker registered with scope:', registration.scope);
      }).catch(function (err) {
        console.error('Service Worker registration failed:', err);
      });
  }

  </script>
  <!-- This script adds the flutter initialization JS code -->
  <script src="flutter.js" defer=""></script>
  


  <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport">
  
  <style id="splash-screen-style">
    html {
      height: 100%
    }

    body {
      margin: 0;
      min-height: 100%;
      background-color: #4355B6;
      background-size: 100% 100%;
    }

    .center {
      margin: 0;
      position: absolute;
      top: 50%;
      left: 50%;
      -ms-transform: translate(-50%, -50%);
      transform: translate(-50%, -50%);
    }

    .contain {
      display: block;
      width: 100%;
      height: 100%;
      object-fit: contain;
    }

    .stretch {
      display: block;
      width: 100%;
      height: 100%;
    }

    .cover {
      display: block;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .bottom {
      position: absolute;
      bottom: 0;
      left: 50%;
      -ms-transform: translate(-50%, 0);
      transform: translate(-50%, 0);
    }

    .bottomLeft {
      position: absolute;
      bottom: 0;
      left: 0;
    }

    .bottomRight {
      position: absolute;
      bottom: 0;
      right: 0;
    }
  </style>
  <script id="splash-screen-script">
    function removeSplashFromWeb() {
      document.getElementById("splash")?.remove();
      document.getElementById("splash-branding")?.remove();
      document.body.style.background = "transparent";
    }
  </script>
 <script type="application/javascript" src="assets/packages/flutter_inappwebview_web/assets/web/web_support.js" defer></script>

</head>
<body>
  <picture id="splash">
    <source
      srcset="splash/img/light-1x.png 1x, splash/img/light-2x.png 2x, splash/img/light-3x.png 3x, splash/img/light-4x.png 4x"
      media="(prefers-color-scheme: light)">
    <source
      srcset="splash/img/dark-1x.png 1x, splash/img/dark-2x.png 2x, splash/img/dark-3x.png 3x, splash/img/dark-4x.png 4x"
      media="(prefers-color-scheme: dark)">
    <img class="center" aria-hidden="true" src="splash/img/light-1x.png" alt="">
  </picture>
  <script src="assets/packages/libphonenumber_plugin/js/libphonenumber.js"></script>
  <script src="assets/packages/libphonenumber_plugin/js/stringbuffer.js"></script>


  <script>
    window.addEventListener('load', function (ev) {
      // Download main.dart.js
      //loadEntrypoint
      _flutter.loader.loadEntrypoint({
        serviceWorker: {
          serviceWorkerVersion: serviceWorkerVersion,
        },
        onEntrypointLoaded: function (engineInitializer) {
          engineInitializer.initializeEngine().then(function (appRunner) {
            appRunner.runApp();
          });
        }
      });
    });
  </script>


</body>

</html>