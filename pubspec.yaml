name: onekitty
version: 4.2.0+22
publish_to: none
environment:
  sdk: ">=3.0.0 <4.0.0"
description: onekitty helps to ease in funds collection and contributions betweeen groups
dependencies:
  accordion: ^2.6.0
  animations: ^2.0.11
  auto_size_text: ^3.0.0
  cached_network_image: ^3.3.1
  carousel_slider: ^5.0.0
  cherry_toast: ^1.2.1
  connectivity_checker: ^1.1.0
  contacts_service: ^0.6.3
  country_pickers: ^3.0.1
  currency_formatter: ^2.2.0
  data_table_2: ^2.5.15
  date_time_format: ^2.0.1
  device_info_plus: ^10.1.0
  dio: ^5.8.0+1
  encrypt: ^5.0.3
  excel: ^4.0.2
  fast_cached_network_image: 1.2.9
  file_picker: ^8.0.0+1
  firebase_analytics: ^11.3.3
  firebase_auth: ^5.3.1
  firebase_core: ^3.6.0
  firebase_crashlytics: ^4.1.3
  firebase_messaging: ^15.1.3
  firebase_remote_config: ^5.1.3
  firebase_storage: ^12.3.2
  fl_country_code_picker: ^0.1.9+1
  flex_color_scheme: ^7.3.1
  flutter:
    sdk: flutter
  flutter_animate: ^4.5.0
  flutter_animated_dialog_updated: ^1.0.1
  flutter_contacts: any
  flutter_easyloading: ^3.0.5
  flutter_expandable_fab: ^2.0.0
  flutter_flip_clock: ^0.0.1
  flutter_inappwebview: ^6.0.0-beta.30
  flutter_map: ^7.0.2
  flutter_markdown: ^0.7.3+2
  flutter_pdfview: ^1.3.2
  flutter_quill: ^10.8.2
  flutter_riverpod: ^2.5.1
  flutter_screenutil: ^5.9.0
  flutter_spinkit: ^5.2.0
  flutter_svg: ^2.0.9
  flutter_tawk: ^0.1.0
  fluttertoast: ^8.2.8
  font_awesome_flutter: ^10.8.0
  get: ^4.7.2
  get_storage_pro: ^0.1.9
  google_fonts: ^6.1.0
  google_generative_ai: ^0.4.6
  grouped_list: ^6.0.0
  image_picker: ^1.1.2
  insta_image_viewer: ^1.0.4
  intl: any
  intl_phone_number_input: ^0.7.4
  latlong2: ^0.9.1
  local_auth: ^2.1.7
  logger: ^2.0.2+1
  mime: ^1.0.5
  number_paginator: ^0.4.0
  open_file: any
  otp_autofill: any
  package_info_plus: ^8.0.0
  path: ^1.9.0
  path_provider: ^2.0.15
  pdf: ^3.10.8
  permission_handler: any
  photo_view: ^0.15.0
  pinput: ^5.0.0
  popover: ^0.3.0
  printing: any
  pull_to_refresh: ^2.0.0
  pluto_grid_plus: ^8.4.9
  qr_flutter: ^4.1.0
  readmore: ^3.0.0
  responsive_framework: ^1.0.0
  share_plus: ^10.0.2
  share_whatsapp: ^1.0.2
  shimmer: ^3.0.0
  simple_barcode_scanner: ^0.0.8
  syncfusion_flutter_datagrid: ^27.2.5
  syncfusion_flutter_xlsio: ^27.1.52
  time_since: ^1.0.1
  timeline_tile: ^2.0.0
  upgrader: ^11.2.0
  url_launcher: ^6.1.11
  # url_strategy: ^0.3.0
  vsc_quill_delta_to_html: ^1.0.5
dev_dependencies:
  change_app_package_name: ^1.1.0
  flutter_launcher_icons: ^0.14.1
  flutter_lints: ^5.0.0
  flutter_test:
    sdk: flutter
  pubspec_dependency_sorter: ^1.0.4
dependency_overrides:
  device_info_plus: ^10.0.1
  pdf_widget_wrapper: ^1.0.4
  super_native_extensions: ^0.8.16
flutter_icons:
  image_path: assets/images/launcher_2.png
  android: true
  ios: true
  remove_alpha_ios: true
flutter_native_splash:
  color: "#4355B6"
  image: assets/images/logo8.png
  android_12:
    color: "#4355B6"
    image: assets/images/logo8.png
  android: true
  ios: true
flutter:
  assets:
    - assets/images/
    - assets/images/icons/
    - assets/
  fonts:
    - family: Poppins
      fonts:
        - asset: assets/fonts/PoppinsSemiBold.ttf
          weight: 600
        - asset: assets/fonts/PoppinsRegular.ttf
          weight: 400
        - asset: assets/fonts/PoppinsMedium.ttf
          weight: 500
    - family: Inter
      fonts:
        - asset: assets/fonts/InterRegular.ttf
          weight: 400
    - family: Sen
      fonts:
        - asset: assets/fonts/SenRegular.ttf
          weight: 400
  uses-material-design: true
fonts:
  - family: Sen
    fonts:
      - asset: assets/fonts/Sen-VariableFont_wght.ttf
