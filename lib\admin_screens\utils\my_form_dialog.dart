import 'package:flutter/material.dart';
import 'package:onekitty/utils/my_button.dart';

void showMyFormDialog(
    {required BuildContext context,
    required String title,
    String? description,
    required List<Widget> contents,
    String? okText,
    String? cancelText,
    Function()? onOkPressed,
    Function()? onCancelPressed}) {
  showDialog(
    context: context,
    builder: (context) => MyFormDialog(
        title: title,
        contents: contents,
        description: description,
        okText: okText,
        cancelText: cancelText,
        onOkPressed: onOkPressed,
        onCancelPressed: onCancelPressed),
  );
}

class MyFormDialog extends StatelessWidget {
  final String title;
  final String? description;
  final List<Widget> contents;
  final String? okText;
  final String? cancelText;
  final Function()? onOkPressed;
  final Function()? onCancelPressed;

  const MyFormDialog(
      {super.key,
      required this.title,
      this.description,
      required this.contents,
      this.okText,
      this.cancelText,
      this.onOkPressed,
      this.onCancelPressed});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 600, minWidth: 380),
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 20,
                  )),
              if (description != null) Text(description!),
              ...contents,
              const SizedBox(height: 16),
              Row(
                children: [
                  if (onCancelPressed != null)
                    Expanded(
                      child: MyButton(
                          label: cancelText ?? 'Cancel',
                          onClick: onCancelPressed),
                    ),
                  if (onOkPressed != null)
                    Expanded(
                        child: MyButton(
                            label: okText ?? 'Ok', onClick: onOkPressed)),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}
