import 'package:animations/animations.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/beneficiary_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/connectivity_wrapper.dart';
import 'package:onekitty/models/kitty/beneficiary_model.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/contribution_kitties/create_beneficiary.dart';
import 'package:onekitty/utils/formatted_currency.dart';
import 'package:onekitty/utils/my_button.dart';
import 'package:onekitty/utils/size_config.dart';
import 'package:onekitty/utils/timeSince.dart';
import 'package:shimmer/shimmer.dart';
import 'package:timeline_tile/timeline_tile.dart';

class BeneficiariesPage extends StatefulWidget {
  const BeneficiariesPage({
    super.key,
  });

  @override
  State<BeneficiariesPage> createState() => _BeneficiariesPageState();
}

class _BeneficiariesPageState extends State<BeneficiariesPage> {
  final BeneficiaryController controller = Get.find();
  final DataController dataController = Get.find<DataController>();

  int kittyId = 0;
  String searchQuery = '';

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      kittyId = dataController.kitty.value.kitty?.id ?? 0;
      controller.getBeneficiaries(kittyId);
    });
    super.initState();
  }

  void updateSearchQuery(String query) {
    setState(() {
      searchQuery = query;
    });
    controller.filterBeneficiaries(query);
  }

  @override
  Widget build(BuildContext context) {
    return ConnectivityCheck(
      child: Scaffold(
          floatingActionButton: FloatingActionButton(
            onPressed: () {
              Get.to(
                () => CreateBeneficiary(kittyId: kittyId),
              );
            },
            child: const Icon(Icons.add),
          ),
          appBar: AppBar(title: const Text('Beneficiaries')),
          body: Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: CupertinoSearchTextField(
                  onChanged: updateSearchQuery,
                  placeholder: 'Search beneficiaries',
                ),
              ),
              Expanded(
                child: RefreshIndicator(
                  onRefresh: () async =>
                      await controller.getBeneficiaries(kittyId),
                  child: GetBuilder<BeneficiaryController>(
                    init: BeneficiaryController(),
                    initState: (state) async {
                      await state.controller?.getBeneficiaries(kittyId);
                    },
                    builder: (controller) {
                      if (controller.isFetchingBeneficiaries.value) {
                        return ListView.builder(
                          itemCount: 5,
                          itemBuilder: (context, index) {
                            return Container(
                              margin: EdgeInsets.all(8.spMin),
                              height: 80.h,
                              child: Shimmer.fromColors(
                                baseColor: Colors.grey[300]!,
                                highlightColor: Colors.grey[100]!,
                                child: Container(
                                  color: Colors.white,
                                ),
                              ),
                            );
                          },
                        );
                      } else if (controller.filteredBeneficiaries.isEmpty) {
                        return Center(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Text('No beneficiaries found'),
                              SizedBox(height: 18.h),
                              Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: SizeConfig.screenWidth * .2),
                                child: MyButton(
                                  onClick: () {
                                    Get.to(
                                      () => CreateBeneficiary(kittyId: kittyId),
                                    );
                                  },
                                  label: 'Create beneficiary',
                                ),
                              ),
                            ],
                          ),
                        );
                      } else {
                        return ListView.builder(
                            itemCount:
                                controller.filteredBeneficiaries.length + 1,
                            itemBuilder: (context, index) {
                              if (index ==
                                  controller.filteredBeneficiaries.length) {
                                return SizedBox(height: 200.h);
                              }

                              return Column(children: [
                                BeneficiaryCard(
                                    kittyId: kittyId,
                                    controller: controller,
                                    beneficiary: controller
                                        .filteredBeneficiaries[index]),
                                if (index !=
                                    controller.filteredBeneficiaries.length - 1)
                                  SizedBox(
                                    height: 40.h,
                                    child: Padding(
                                      padding: EdgeInsets.only(left: 20.0.w),
                                      child: TimelineTile(
                                        alignment: TimelineAlign.start,
                                        isFirst: false,
                                        isLast: false,
                                        indicatorStyle: IndicatorStyle(
                                          width: 12.w,
                                          color: AppColors.primary,
                                          padding: EdgeInsets.only(left: 15.w),
                                          iconStyle: IconStyle(
                                            iconData: Icons.circle,
                                            fontSize: 8,
                                            color: Colors.white,
                                          ),
                                        ),
                                        beforeLineStyle: const LineStyle(
                                          color: AppColors.primary,
                                          thickness: 2,
                                        ),
                                      ),
                                    ),
                                  )
                              ]);
                            });
                      }
                    },
                  ),
                ),
              ),
            ],
          )),
    );
  }
}

class BeneficiaryCard extends StatelessWidget {
  final BeneficiaryController controller;
  final BeneficiaryModel beneficiary;
  final int kittyId;
  const BeneficiaryCard(
      {super.key,
      required this.controller,
      required this.beneficiary,
      required this.kittyId});

  @override
  Widget build(BuildContext context) {
    final isExpanded = false.obs;
    return OpenContainer(
      closedElevation: 0,
      openBuilder: (context, action) =>
          CreateBeneficiary(kittyId: kittyId, edit: beneficiary),
      closedBuilder: (context, action) => Container(
          decoration: BoxDecoration(
            color: beneficiary.role == 'PRIMARY'
                ? AppColors.primary.withAlpha(150)
                : const Color(0xfffefefe),
            borderRadius: BorderRadius.circular(15),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.2),
                spreadRadius: 2,
                blurRadius: 5,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          margin: const EdgeInsets.symmetric(vertical: 2.0, horizontal: 16.0),
          child: ExpansionTile(
            onExpansionChanged: (state) {
              isExpanded(state);
            },
            childrenPadding: const EdgeInsets.all(8),
            leading: CircleAvatar(
              child: Text(
                beneficiary.accountName != null &&
                        beneficiary.accountName!.isNotEmpty &&
                        beneficiary.accountName!.split(' ').isNotEmpty
                    ? beneficiary.accountName!
                        .split(' ')
                        .map((word) => word.isNotEmpty ? word[0] : '')
                        .take(2)
                        .join()
                        .toUpperCase()
                    : '',
              ),
            ),
            title: Row(children: [
              Text(beneficiary.accountName ?? ''),
              if (beneficiary.role == 'PRIMARY')
                Container(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 8.0, vertical: 2.0),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.white),
                    borderRadius: BorderRadius.circular(25),
                    color: Colors.transparent,
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.verified_outlined,
                          color: Colors.white, size: 10.sp),
                      const SizedBox(width: 4),
                      Text(
                        'Primary',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 8.spMin,
                        ),
                      ),
                    ],
                  ),
                )
            ]),
            trailing: Obx(
              () => isExpanded.value
                  ? const SizedBox(width: 1) // Hide when expanded
                  : beneficiary.splitConfig == "AMOUNT"
                      ? Text(
                          "${FormattedCurrency().getFormattedCurrency(beneficiary.amount ?? 0)}",
                        )
                      : SizedBox(
                          width: 60.w,
                          height: 60.h,
                          child: Stack(
                            alignment: Alignment.center,
                            children: [
                              CircularProgressIndicator(
                                value: beneficiary.percentage?.toDouble(),
                                strokeWidth: 4,
                                backgroundColor: Colors.transparent,
                                valueColor: const AlwaysStoppedAnimation<Color>(
                                    AppColors.primary),
                              ),
                              Text(
                                '${((beneficiary.percentage ?? 0) * 100).toStringAsFixed(1)}%',
                                style: TextStyle(fontSize: 8.spMin),
                              ),
                            ],
                          ),
                        ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('AccNo: ${beneficiary.accountNumber}'),
                Row(children: [
                  if (beneficiary.transferMode == "PAYBILL")
                    Text(beneficiary.accountNumberRef),
                  if (beneficiary.transferMode == "PAYBILL")
                    SizedBox(width: 8.w),
                  Text(
                      "${beneficiary.transferMode} - ${beneficiary.channelName}"),
                ]),
              ],
            ),
            tilePadding: const EdgeInsets.all(4.0),
            shape: const RoundedRectangleBorder(),
            children: [
              Row(children: [
                Expanded(
                    child: Column(
                  children: [
                    Text(beneficiary.splitConfig!.toLowerCase()),
                    beneficiary.splitConfig == "AMOUNT"
                        ? SizedBox(
                            height: 40.h,
                            child: Center(
                              child: Text(
                                  "${FormattedCurrency().getFormattedCurrency(beneficiary.amount ?? 0)}",
                                  style: TextStyle(
                                      fontSize: 16.spMin,
                                      fontWeight: FontWeight.w600,
                                      letterSpacing: 1.6)),
                            ),
                          )
                        : SizedBox(
                            width: 60.w,
                            height: 40.h,
                            child: Stack(
                              alignment: Alignment.center,
                              children: [
                                CircularProgressIndicator(
                                  value: beneficiary.percentage?.toDouble(),
                                  strokeWidth: 4,
                                  backgroundColor: Colors.transparent,
                                  valueColor:
                                      const AlwaysStoppedAnimation<Color>(
                                          AppColors.primary),
                                ),
                                Text(
                                  '${((beneficiary.percentage ?? 0) * 100.0).toStringAsFixed(1)}%',
                                  style: TextStyle(fontSize: 8.spMin),
                                ),
                              ],
                            ),
                          ),
                  ],
                )),
                Expanded(
                    child: Column(children: [
                  const Text('end Date'),
                  SizedBox(
                      height: 40.h,
                      child: Center(
                          child: Text.rich(
                        textAlign: TextAlign.center,
                        TextSpan(
                          children: [
                            TextSpan(
                                text: beneficiary.endDate != null
                                    ? "${DateFormat('d MMM HH:MM a').format(beneficiary.endDate!)}\n"
                                    : '',
                                style: TextStyle(
                                    fontSize: 16.spMin,
                                    fontWeight: FontWeight.w600,
                                    letterSpacing: 1.6)),
                            TextSpan(
                                text: beneficiary.endDate != null
                                    ? (beneficiary.endDate!
                                                .toLocal()
                                                .difference(DateTime.now())
                                                .inDays <
                                            0
                                        ? 'expired - ${highPrecisiontimeSince(beneficiary.endDate!.toLocal())}'
                                        : highPrecisiontimeSince(
                                            beneficiary.endDate!.toLocal()))
                                    : 'N/A')
                          ],
                        ),
                      )))
                ]))
              ]),
              ListTile(
                  subtitle: Text("+${beneficiary.phoneNumber}"),
                  trailing: SizedBox(
                    width: 100.w,
                    child: Row(children: [
                      if (beneficiary.role != "PRIMARY")
                        IconButton(
                            icon: const Icon(Icons.delete),
                            onPressed: () {
                              Get.dialog(AlertDialog(
                                  title: const Text("Delete Beneficiary"),
                                  content: Text(
                                    "Are you sure you want to delete ${beneficiary.accountName}?",
                                  ),
                                  actions: [
                                    TextButton(
                                        onPressed: () => Get.back(),
                                        child: const Text("Cancel")),
                                    Obx(() => MyButton(
                                        width: 150.w,
                                        showLoading: controller
                                            .isDeletingBeneficiary.value,
                                        onClick: () {
                                          controller
                                              .deleteBeneficiary(
                                                  beneficiary.id, kittyId)
                                              .whenComplete(
                                                  () => Navigator.pop(context));
                                        },
                                        label: ("Delete")))
                                  ]));
                            }),
                      IconButton(
                          icon: const Icon(Icons.edit),
                          onPressed: () {
                            controller.getPercentage();
                            action.call();
                          })
                    ]),
                  )),
            ],
          )),
    );
  }
}
