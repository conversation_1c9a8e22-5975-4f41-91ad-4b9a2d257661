import 'package:flutter/material.dart';

Color getStatusColor(String status) {
  switch (status.toUpperCase()) {
    case 'PROCESSING':
      return Colors.orange;
    case 'INITIALIZED':
      return Colors.blue;
    case 'SUCCESS':
      return Colors.green;
    case 'BLOCKED':
      return Colors.red;
    case 'DRAFT':
      return Colors.grey;
    case 'ACTIVE':
      return Colors.green;
    case 'PENDING':
      return Colors.amber;
    case 'FAILED':
      return Colors.red[900]!;
    case 'CANCELLED':
      return Colors.grey[700]!;
    case 'ON_HOLD':
      return Colors.purple;
    case 'IN_REVIEW':
      return Colors.lightBlue;
    case 'COMPLETED':
      return Colors.teal;
    case 'EXPIRED':
      return Colors.brown;
    case 'SUSPENDED':
      return Colors.deepOrange;
    case 'INACTIVE':
      return Colors.blueGrey;
    case 'APPROVED':
      return Colors.green[700]!;
    case 'REJECTED':
      return Colors.red[700]!;
    case 'WAITING':
      return Colors.amber[600]!;
    case 'IN_PROGRESS':
      return Colors.lightBlue[300]!;
    case 'ON_PAUSE':
      return Colors.purple[300]!;
    default:
      return Colors.grey;
  }
}