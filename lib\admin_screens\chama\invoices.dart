import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/admin/chama/chama_admin_controller.dart';
import 'package:onekitty/models/chama/chama_model.dart';
import 'package:onekitty/models/chama/invoices.dart';
import 'package:onekitty/utils/my_button.dart';
import 'package:onekitty/utils/themes_colors.dart';

import 'widgets/table_footer.dart';

/*
Pending/Paid/overpaid/Underpaid                      
*/

class InvoicesPage extends StatefulWidget {
  final Chama chama;
  const InvoicesPage({super.key, required this.chama});

  @override
  State<InvoicesPage> createState() => _InvoicesPageState();
}

class _InvoicesPageState extends State<InvoicesPage> {
  final controller = Get.find<ChamaAdminController>();
  void onRefresh() {
    controller.getInvoices(chamaId: widget.chama.id!, page: 0);
  }

  void _onRowsPerPageChanged(int value) {
    controller.invoiceSize.value = value;
    controller.getInvoices(chamaId: widget.chama.id!, page: 0);
  }

  void _onRefresh() {
    controller.invoices.clear();
    controller.getInvoices(chamaId: widget.chama.id!, page: 0);
  }

  void _onPreviousPage() {
    if (!controller.invoiceIsFirst.value) {
      controller.getInvoices(
          chamaId: widget.chama.id!,
          page: controller.invoiceCurrentPage.value - 1);
    }
  }

  void _onNextPage() {
    if (!controller.invoiceIsLast.value) {
      controller.getInvoices(
          chamaId: widget.chama.id!,
          page: controller.invoiceCurrentPage.value + 1);
    }
  }

  @override
  initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      onRefresh();
      controller.clearInvoice();
    });
  }

  int? _sortColumnIndex;
  bool _sortAscending = true;
  void _sort<T>(Comparable<T> Function(Invoice invoice) getField,
      int columnIndex, bool ascending) {
    setState(() {
      _sortColumnIndex = columnIndex;
      _sortAscending = ascending;
      controller.invoices.sort((a, b) {
        final aValue = getField(a);
        final bValue = getField(b);
        return ascending
            ? Comparable.compare(aValue, bValue)
            : Comparable.compare(bValue, aValue);
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async => onRefresh,
      child: Scaffold(
        appBar: AppBar(
          actions: [
            IconButton(icon: const Icon(Icons.refresh), onPressed: onRefresh)
          ],
        ),
        bottomNavigationBar: Obx(
          () => controller.isLoading.value
              ? const SizedBox()
              : TablePaginationFooter(
                  currentPage: controller.invoiceCurrentPage.value,
                  totalPages: controller.invoiceTotalPages.value,
                  rowsPerPage: controller.invoiceSize.value,
                  availableRowsPerPage: const [15, 30, 50, 100],
                  onRowsPerPageChanged: _onRowsPerPageChanged,
                  onRefresh: _onRefresh,
                  onPreviousPage: _onPreviousPage,
                  onNextPage: _onNextPage,
                ),
        ),
        body: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                'Invoices',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: InvoiceFilter(
                controller: controller,
                chama: widget.chama,
              ),
            ),
            Expanded(
              child: Obx(
                () => controller.isLoading.value
                    ? const Center(child: CircularProgressIndicator())
                    : controller.invoices.isEmpty
                        ? const Center(child: Text('No Invoices'))
                        : DataTable2(
                            columnSpacing: 12,
                            horizontalMargin: 12,
                            minWidth: 600,
                            sortColumnIndex: _sortColumnIndex,
                            sortAscending: _sortAscending,
                            columns: [
                              DataColumn2(
                                label: const Text('ID'),
                                size: ColumnSize.S,
                                onSort: (columnIndex, ascending) {
                                  _sort<num>((invoice) => invoice.id,
                                      columnIndex, ascending);
                                },
                              ),
                              DataColumn2(
                                label: const Text('Created At'),
                                onSort: (columnIndex, ascending) {
                                  _sort<DateTime>(
                                      (invoice) =>
                                          invoice.createdAt ?? DateTime.now(),
                                      columnIndex,
                                      ascending);
                                },
                              ),
                              DataColumn2(
                                label: const Text('Updated At'),
                                onSort: (columnIndex, ascending) {
                                  _sort<DateTime>(
                                      (invoice) =>
                                          invoice.updatedAt ?? DateTime.now(),
                                      columnIndex,
                                      ascending);
                                },
                              ),
                              DataColumn2(
                                label: const Text('Due Amount'),
                                size: ColumnSize.L,
                                onSort: (columnIndex, ascending) {
                                  _sort<num>((invoice) => invoice.dueAmount,
                                      columnIndex, ascending);
                                },
                              ),
                              DataColumn2(
                                label: const Text('Due Date'),
                                onSort: (columnIndex, ascending) {
                                  _sort<DateTime>(
                                      (invoice) =>
                                          invoice.cycleStartDate ??
                                          DateTime.now(),
                                      columnIndex,
                                      ascending);
                                },
                              ),
                              DataColumn2(
                                label: const Text('Chama Balance'),
                                size: ColumnSize.L,
                                onSort: (columnIndex, ascending) {
                                  _sort<num>((invoice) => invoice.chamaBalance,
                                      columnIndex, ascending);
                                },
                              ),
                              DataColumn2(
                                label: const Text('Paid Amount'),
                                size: ColumnSize.L,
                                onSort: (columnIndex, ascending) {
                                  _sort<num>((invoice) => invoice.chamaBalance,
                                      columnIndex, ascending);
                                },
                              ),
                              // const DataColumn2(
                              //   label: Text('Actions'),
                              //   size: ColumnSize.L,
                              // ),
                            ],
                            rows: controller.invoices
                                .map((invoice) => DataRow2(cells: [
                                      DataCell(Text(invoice.id.toString())),
                                      DataCell(Text(DateFormat('yyyy-MM-dd')
                                          .format(invoice.createdAt ??
                                              DateTime.now()))),
                                      DataCell(Text(DateFormat('yyyy-MM-dd')
                                          .format(invoice.updatedAt ??
                                              DateTime.now()))),
                                      DataCell(
                                          Text(invoice.dueAmount.toString())),
                                      DataCell(Text(DateFormat('yyyy-MM-dd')
                                          .format(invoice.dueDate ??
                                              DateTime.now()))),
                                      DataCell(Text(
                                          invoice.chamaBalance.toString())),
                                      DataCell(
                                          Text(invoice.paidAmount.toString())),
                                      //   DataCell(PopupMenuButton<String>(
                                      //     icon: const Icon(Icons.settings),
                                      //     onSelected: (String result) {
                                      //       switch (result) {
                                      //         case 'view':
                                      //           break;
                                      //         case 'Penalties':
                                      //           break;
                                      //         case 'beneficiaries':
                                      //           break;
                                      //       }
                                      //     },
                                      //     itemBuilder: (BuildContext context) =>
                                      //         <PopupMenuEntry<String>>[
                                      //       const PopupMenuItem<String>(
                                      //         value: 'Some Options',
                                      //         child: Text('Some Options'),
                                      //       ),
                                      //     ],
                                      //   )),
                                    ]))
                                .toList(),
                          ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class InvoiceFilter extends StatelessWidget {
  final ChamaAdminController controller;
  final Chama chama;

  const InvoiceFilter({
    super.key,
    required this.controller,
    required this.chama,
  });

  @override
  Widget build(BuildContext context) {
    final cycleCount = TextEditingController();
    final memberId = TextEditingController();
    final selectedStatus = ''.obs;
    return Column(
      children: [
        Wrap(
          spacing: 8,
          alignment: WrapAlignment.start,
          crossAxisAlignment: WrapCrossAlignment.start,
          children: [
            SizedBox(
              width: 100.w,
              child: TextField(
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  hintText: 'Filter by Member ID...',
                  //filled: true,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide.none,
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: 100.w,
              child: TextField(
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  hintText: 'Filter by CycleCount',
                  //filled: true,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide.none,
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              height: 60,
              width: 170.w,
              child: Obx(() => Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      'Pending',
                      'Paid',
                      'Overpaid',
                      'Underpaid',
                    ]
                        .map((status) => Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: ActionChip(
                                label: Text(status),
                                backgroundColor: selectedStatus.value == status
                                    ? ColorUtil.blueColor
                                    : Colors.grey,
                                onPressed: () => selectedStatus.value = status,
                              ),
                            ))
                        .toList(),
                  )),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: MyButton(
                width: 80.w,
                label: 'Submit',
                onClick: () => controller.getInvoices(
                    chamaId: chama.id!,
                    page: 0,
                    filters:
                        "&cycle_count=${cycleCount.text}&member_id=${memberId.text}&status=${selectedStatus.value}"),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
