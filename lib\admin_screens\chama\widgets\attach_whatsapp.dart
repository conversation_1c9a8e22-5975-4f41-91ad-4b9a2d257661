import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onekitty/utils/my_button.dart';
import 'package:onekitty/utils/my_text_field.dart';

class AttachWhatsapp extends StatelessWidget {
  const AttachWhatsapp({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: const BoxConstraints(maxWidth: 600),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: Text('Add WhatsApp Link',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 20.spMin,
                  )),
              subtitle: const Text('Please add a valid WhatsApp link'),
              trailing: IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close)),
            ),
            SizedBox(height: 10.h),
            const MyTextFieldwValidator(
              title: 'WhatsApp Link:',
              hint: 'WhatsApp group Link',
            ),
            SizedBox(height: 10.h),
            const MyButton(width: 200, label: 'Submit'),
          ],
        ),
      ),
    );
  }
}
