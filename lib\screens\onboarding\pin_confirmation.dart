import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/auth_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';
import 'package:onekitty/screens/bottom_navbar_screens/botton_navigation_section/bottom_nav_section.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/custom_button.dart';

class PinConfirmPage extends StatefulWidget {
  const PinConfirmPage({super.key});

  @override
  State<PinConfirmPage> createState() => _PinConfirmPageState();
}

class _PinConfirmPageState extends State<PinConfirmPage> {
  final _formKey = GlobalKey<FormState>();
  bool hidePassword = true;
  bool hidePassword2 = true;
  TextEditingController pinController = TextEditingController();
  TextEditingController confimController = TextEditingController();
  final AuthenticationController authenticationController =
      Get.put(AuthenticationController());

  @override
  void dispose() {
    super.dispose();
    pinController.dispose();
    confimController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    return Scaffold(
      body: SingleChildScrollView(
        child: Container(
          height: screenSize.height * 0.9,
          margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
          child: Column(
            children: [
              Image.asset(
                "assets/images/logo-variation1.png",
                width: screenSize.width * 0.45,
              ),
              const Text(
                "Karibu",
                style: TextStyle(
                    color: AppColors.greyTextColor,
                    fontSize: 15,
                    fontWeight: FontWeight.w600),
              ),
              const SizedBox(
                height: 15,
              ),
              const Text(
                "Password Confirmation",
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
              ),
              Form(
                  key: _formKey,
                  autovalidateMode: AutovalidateMode.onUserInteraction,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 20.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text("Password",
                            style: TextStyle(
                                color: AppColors.dark,
                                fontWeight: FontWeight.w600)),
                        CustomTextField(
                            labelText: "Enter password",
                            isRequired: true,
                            controller: pinController,
                            obscureText: hidePassword,
                            suffixIcon: GestureDetector(
                              onTap: () {
                                setState(() {
                                  hidePassword = !hidePassword;
                                });
                              },
                              child: hidePassword
                                  ? const Icon(
                                      Icons.remove_red_eye,
                                    )
                                  : const Icon(
                                      Icons.visibility_off,
                                    ),
                            ),
                            validator: (value) {
                              if (value!.isEmpty) {
                                return "Enter password";
                              } else if (value.length < 8) {
                                return "Password cannot have less than 8 characters";
                              } else {
                                return null;
                              }
                            }),
                        const Text("Confirm Password",
                            style: TextStyle(
                                color: AppColors.dark,
                                fontWeight: FontWeight.w600)),
                        CustomTextField(
                            labelText: "Confirm password",
                            isRequired: true,
                            controller: confimController,
                            obscureText: hidePassword2,
                            suffixIcon: GestureDetector(
                              onTap: () {
                                setState(() {
                                  hidePassword2 = !hidePassword2;
                                });
                              },
                              child: hidePassword2
                                  ? const Icon(
                                      Icons.remove_red_eye,
                                    )
                                  : const Icon(
                                      Icons.visibility_off,
                                    ),
                            ),
                            validator: (value) {
                              if (value!.isEmpty) {
                                return "Enter password";
                              } else if (value != pinController.text) {
                                return "Pin does not match";
                              } else {
                                return null;
                              }
                            }),
                        const SizedBox(
                          height: 20,
                        ),
                        Obx(
                          () => CustomKtButton(
                            isLoading:
                                authenticationController.isPinloading.isTrue,
                            onPress: () async {
                              if (_formKey.currentState!.validate()) {
                                final res = await authenticationController
                                    .setPin(pinController.text);
                                if (res) {
                                  Get.offAll(() => BottomNavSection());
                                }
                                Snack.show(res,
                                    authenticationController.apiMessage.string);
                              }
                            },
                            text: "submit",
                            onPressed: () {},
                            btnText: 'Submit',
                          ),
                        )
                      ],
                    ),
                  )),
            ],
          ),
        ),
      ),
    );
  }
}
