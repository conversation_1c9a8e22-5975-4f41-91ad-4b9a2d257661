import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onekitty/utils/my_text_field.dart';

class Socials extends StatelessWidget {
  final TextEditingController website, facebook, xlink, instagram, tiktok;

  const Socials({
    super.key,
    required this.website,
    required this.facebook,
    required this.xlink,
    required this.instagram,
    required this.tiktok,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        MyTextFieldwValidator(
            controller: website,
            titleStyle:
                TextStyle(fontSize: 14.spMin, fontWeight: FontWeight.w500),
            title: 'website (optional)',
            hint: ''),
        SizedBox(height: 8.h),
        MyTextField(
            controller: facebook,
            titleStyle:
                TextStyle(fontSize: 14.spMin, fontWeight: FontWeight.w500),
            title: 'Facebook link (optional)',
            hint: ''),
        Si<PERSON><PERSON>ox(height: 8.h),
        MyTextFieldwValidator(
            controller: xlink,
            titleStyle:
                TextStyle(fontSize: 14.spMin, fontWeight: FontWeight.w500),
            title: 'X link (optional)',
            hint: ''),
        SizedBox(height: 8.h),
        MyTextFieldwValidator(
            controller: instagram,
            titleStyle:
                TextStyle(fontSize: 14.spMin, fontWeight: FontWeight.w500),
            title: 'Instagram link (optional)',
            hint: ''),
        SizedBox(height: 8.h),
        MyTextFieldwValidator(
            controller: tiktok,
            titleStyle:
                TextStyle(fontSize: 14.spMin, fontWeight: FontWeight.w500),
            title: 'Tiktok link (optional)',
            hint: ''),
        SizedBox(height: 8.h),
      ]),
    );
  }
}
