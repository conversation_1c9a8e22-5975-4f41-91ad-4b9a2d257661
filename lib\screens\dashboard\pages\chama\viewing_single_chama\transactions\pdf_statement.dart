import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/models/chama/chama_model.dart';
import 'package:onekitty/models/chama/chama_transactions.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/see_all_transactions_screen/widgets/pdf_make.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:pdf/widgets.dart' as pw;
import '../../../../../../utils/utils_exports.dart';

Future<Uint8List> makeChamaSinglePdf(Transaction transaction) async {
  UserKittyController userController = Get.find();
  final ChamaDataController dataController = Get.find();
  userController.getLocalUser();
  final dateformat = DateFormat('EE, dd MMMM h:mm a');

  final pdf = Document(
    title:
        "${dataController.chama.value.chama?.title}_${dataController.chama.value.chama?.id}_transaction statement",
    author: "onekitty.co.ke",
    producer: "onekitty.co.ke",
    subject: "Transaction Statement",
    theme: ThemeData(
      defaultTextStyle: TextStyle(
        font: Font.courier(),
      ),
    ),
  );

  final imageLogo =
      MemoryImage((await rootBundle.load(AssetUrl.logo2)).buffer.asUint8List());

  pdf.addPage(
    MultiPage(
      footer: (Context context) {
        return Padding(
          padding: const EdgeInsets.all(10),
          child: Center(
            child: Text(
              'Tel: +254 733550051 \n Email: <EMAIL> \n www.onekitty.co.ke',
              style: Theme.of(context).header3.copyWith(
                    fontStyle: FontStyle.italic,
                  ),
              textAlign: TextAlign.center,
            ),
          ),
        );
      },
      header: (Context context) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              children: [
                Text(
                  "${userController.getLocalUser()?.firstName} ${userController.getLocalUser()?.secondName}",
                ),
                Text("${userController.getLocalUser()?.phoneNumber}"),
              ],
              crossAxisAlignment: CrossAxisAlignment.start,
            ),
            SizedBox(
              height: 150,
              width: 150,
              child: Image(imageLogo),
            )
          ],
        );
      },
      build: (context) {
        return [
          Padding(
            child: Text(
              "${dataController.chama.value.chama?.title} transaction statement",
              textAlign: TextAlign.center,
            ),
            padding: const EdgeInsets.all(1),
          ),
          pw.UrlLink(
              child: pw.Text(
                  'Chama Link: onekitty.co.ke/chama/${dataController.chama.value.chama?.id}'),
              destination:
                  'https://onekitty.co.ke/chama/${dataController.chama.value.chama?.id}'),
          Text(
              "Chama Balance: KSH ${dataController.chama.value.chama?.balance}"),
          Padding(
            child: Text(
                "Accrued Balance: KSH ${dataController.chama.value.chama?.balance}"),
            padding: const EdgeInsets.only(bottom: 9),
          ),
          Table(
            border: TableBorder.all(color: PdfColors.black),
            children: [
              TableRow(
                children: [
                  Padding(
                    child: Text(
                      "Date",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                  Padding(
                    child: Text(
                      "Reference",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                  Padding(
                    child: Text(
                      "Details",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                  Padding(
                    child: Text(
                      "Amount",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                ],
              ),
              TableRow(
                decoration: BoxDecoration(color: PdfColor.fromHex("#DADEF4")),
                children: [
                  Expanded(
                    child: PaddedText(
                      dateformat.format(transaction.createdAt!.toLocal()),
                    ),
                  ),
                  Expanded(
                    child: PaddedText(transaction.transactionCode ?? ''),
                  ),
                  Expanded(
                    child: PaddedText(
                        // ignore: unnecessary_null_comparison
                        "${transaction.firstName}${transaction.secondName != null ? ' ' : ''}${transaction.secondName}\n"),
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(10),
                      child: Text(
                        transaction.typeInOut == "IN"
                            ? "+ KSH ${transaction.amount}"
                            : "- KSH ${transaction.amount}",
                        textAlign: TextAlign.right,
                        style: TextStyle(
                          color: transaction.typeInOut == "IN"
                              ? PdfColors.green
                              : PdfColors.red,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          Padding(
            child: Text(
              "Thank you for using onekitty",
              textAlign: TextAlign.center,
            ),
            padding: const EdgeInsets.all(20),
          ),
          Text(
            "Onekitty is a contribution platform that enables you to receive real-time contribution updates on Whatsapp, Telegram, email, and SMS.",
            textAlign: TextAlign.center,
          ),
          Divider(
            height: 1,
            borderStyle: BorderStyle.dashed,
          ),
        ];
      },
    ),
  );
  return pdf.save();
}

Future<Uint8List> makeChamaFullContributionsPdf(
    List<Transaction> tra, UserChama? chama) async {
  UserKittyController userController = Get.find();
  final ChamaDataController dataController = Get.put(ChamaDataController());
  userController.getLocalUser();
  final dateformat = DateFormat('EE, dd MMMM h:mm a');

  final pdf = Document(
    title:
        "${dataController.chama.value.chama?.title}_${dataController.chama.value.chama?.id}_transaction statement",
    author: "onekitty.co.ke",
    producer: "onekitty.co.ke",
    subject:
        "${dataController.chama.value.chama?.title}_${dataController.chama.value.chama?.id}}_transaction statement",
    theme: ThemeData(
      defaultTextStyle: TextStyle(
        font: Font.courier(),
      ),
    ),
  );
  final imageLogo =
      MemoryImage((await rootBundle.load(AssetUrl.logo2)).buffer.asUint8List());

  pdf.addPage(
    MultiPage(
      footer: (Context context) {
        return Padding(
          padding: const EdgeInsets.all(10),
          child: Center(
            child: Text(
              'Tel: +254 733550051 \n Email: <EMAIL> \n www.onekitty.co.ke',
              style: Theme.of(context).header3.copyWith(
                    fontStyle: FontStyle.italic,
                  ),
              textAlign: TextAlign.center,
            ),
          ),
        );
      },
      header: (Context context) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              children: [
                Text(
                    "${userController.getLocalUser()?.firstName} ${userController.getLocalUser()?.secondName}"),
                Text("${userController.getLocalUser()?.phoneNumber}"),
              ],
              crossAxisAlignment: CrossAxisAlignment.start,
            ),
            SizedBox(
              height: 150,
              width: 150,
              child: Image(imageLogo),
            )
          ],
        );
      },
      build: (context) {
        return [
          // Wrap(
          //   children: [
          Padding(
            child: Text(
              "${dataController.chama.value.chama?.title} transaction statement",
              textAlign: TextAlign.center,
            ),
            padding: const EdgeInsets.all(1),
          ),
          pw.UrlLink(
              child: pw.Text(
                  'Chama Link: onekitty.co.ke/chama/${dataController.chama.value.chama?.id}'),
              destination:
                  'https://onekitty.co.ke/chama/${dataController.chama.value.chama?.id}'),
          Text(
              "Chama Balance: KSH ${dataController.chama.value.chama?.balance}"),
          Padding(
            child: Text(
                "Accrued Balance: KSH ${dataController.chama.value.chama?.balance}"),

            // child: Text("Accrued Balance: KSH ${kitty?.availableBalance ?? 0}"),
            padding: const EdgeInsets.only(bottom: 9),
          ),
          Table(
            border: TableBorder.all(color: PdfColors.black),
            children: [
              TableRow(
                children: [
                  Padding(
                    child: Text(
                      "Date",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                  Padding(
                    child: Text(
                      "Reference",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                  Padding(
                    child: Text(
                      "Details",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                  Padding(
                    child: Text(
                      "Amount",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                ],
              ),
              ...tra.map(
                (e) {
                  final phone = UserKittyController().maskString(e.phoneNumber ?? '');

                  return TableRow(
                    decoration: BoxDecoration(
                      color: e.id!  / 2 == 0 ? PdfColor.fromHex("#DADEF4") : null,
                    ),
                    children: [
                      Expanded(
                        child: PaddedText(
                          dateformat.format(e.createdAt!.toLocal()),
                        ),
                      ),
                      Expanded(
                        child: PaddedText(
                          e.transactionCode ?? ''
                        ),
                        // flex: 2,
                      ),
                      Expanded(
                        child: PaddedText(
                            // ignore: unnecessary_null_comparison
                            "${e.firstName}${e.secondName != null ? ' ' : ''}${e.secondName}\n $phone"),
                      ),
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.all(10),
                          child: Text(
                              e.typeInOut == "IN"
                                  ? "+ KSH ${e.amount}"
                                  : "- KSH ${e.amount}",
                              textAlign: TextAlign.right,
                              style: TextStyle(
                                  color: e.typeInOut == "IN"
                                      ? PdfColors.green
                                      : PdfColors.red)),
                        ),
                        flex: 1,
                      ),
                    ],
                  );
                },
              ),
            ],
          ),
          Expanded(
              child: Padding(
            child: Text(
              "Thank you for using onekitty",
              textAlign: TextAlign.center,
            ),
            padding: const EdgeInsets.all(20),
          )),
          Text(
            "Onekitty is a contribution patform that enables you to receive realtime contribution updates on Whatsapp,Telegram,email and sms.",
            textAlign: TextAlign.center,
          ),
          Divider(
            height: 1,
            borderStyle: BorderStyle.dashed,
          ),
        ];
      },
    ),
  );
  return pdf.save();
}
