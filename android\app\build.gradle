plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}
def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

android {
    namespace "ke.co.onekitty"
    compileSdkVersion 34  // Use the latest SDK version for testing purposes
    ndkVersion flutter.ndkVersion
 
    compileOptions {  
        sourceCompatibility JavaVersion.VERSION_11      
        targetCompatibility JavaVersion.VERSION_11  
        // Flag to enable support for the new language APIs
        coreLibraryDesugaringEnabled true 
    }   
    kotlinOptions {     
        jvmTarget = '11'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig { 
        targetSdkVersion 34
        applicationId "ke.co.onekitty"
        multiDexEnabled true
        minSdkVersion 23
        targetSdkVersion flutter.targetSdkVersion  // This will be fetched from Flutter
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName 
    }

    signingConfigs {  
        release {
            // TODO: Uncomment and configure for production builds with actual signing credentials
            // keyAlias keystoreProperties['keyAlias']
            // keyPassword keystoreProperties['keyPassword']
            // storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            // storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            // TODO: Uncomment for release builds, configure signing with actual keystore
            signingConfig signingConfigs.debug
            
            minifyEnabled true
            shrinkResources true

            // Use default proguard rules for release optimization
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }

        debug {
            // Use debug signing for testing builds, no need for keystore setup
            signingConfig signingConfigs.debug
            minifyEnabled false  // Disable resource minification for testing
            shrinkResources false  // Disable shrinking for debug builds
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation 'androidx.window:window:1.0.0'
    implementation 'androidx.window:window-java:1.0.0'
    implementation 'androidx.core:core-ktx:1.6.0'
    implementation 'androidx.appcompat:appcompat:1.3.0'
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:1.1.5'
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.7.10"
}

// Enforce specific versions of dependencies
configurations.all {
    resolutionStrategy {
        force 'androidx.core:core-ktx:1.6.0'
        force 'androidx.appcompat:appcompat:1.3.0'
    }
}
