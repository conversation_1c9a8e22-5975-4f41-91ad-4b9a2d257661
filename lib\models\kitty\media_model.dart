// To parse this JSON data, do
//
//     final mediaModel = mediaModelFromJson(jsonString);

import 'dart:convert';

MediaModel mediaModelFromJson(String str) =>
    MediaModel.fromJson(json.decode(str));

String mediaModelToJson(MediaModel data) => json.encode(data.toJson());

class MediaModel {
  int? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;
  String? title;
  String? description;
  String? mediaUrl;
  String? type;
  String? actionUrl;
  String? intent;
  String? status;

  MediaModel({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.title,
    this.description,
    this.mediaUrl,
    this.type,
    this.actionUrl,
    this.intent,
    this.status,
  });

  factory MediaModel.fromJson(Map<String, dynamic> json) => MediaModel(
        id: json["ID"],
        createdAt: json["CreatedAt"] == null
            ? null
            : DateTime.parse(json["CreatedAt"]),
        updatedAt: json["UpdatedAt"] == null
            ? null
            : DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        title: json["title"],
        description: json["description"],
        mediaUrl: json["media_url"],
        type: json["type"],
        actionUrl: json["action_url"],
        intent: json["intent"],
        status: json["status"],
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "title": title,
        "description": description,
        "media_url": mediaUrl,
        "type": type,
        "action_url": actionUrl,
        "intent": intent,
        "status": status,
      };
}
