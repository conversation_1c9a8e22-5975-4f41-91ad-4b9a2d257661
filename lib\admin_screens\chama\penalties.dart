import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:onekitty/admin_screens/utils/my_form_dialog.dart';
import 'package:onekitty/admin_screens/utils/my_list_tile.dart';
import 'package:onekitty/controllers/admin/chama/chama_admin_controller.dart';
import 'package:onekitty/models/chama/chama_model.dart';
import 'package:onekitty/utils/my_text_field.dart';
import 'package:onekitty/utils/themes_colors.dart';

class Penalties extends StatefulWidget {
  final Chama chama; 
  const Penalties({super.key, required this.chama});

  @override
  State<Penalties> createState() => _PenaltiesState();
}

class _PenaltiesState extends State<Penalties> {
  final controller = Get.put(ChamaAdminController());
  @override
  initState() {
    super.initState();
    controller.getAllChamaDetails(chamaId: widget.chama.id!);
  }

  void onRefresh() {
    controller.getAllChamaDetails(chamaId: widget.chama.id!);
  }

  @override
  Widget build(BuildContext context) {
    final chama = widget.chama;
    return Scaffold(
      appBar: AppBar(title: const Text('Penalties')),
      body: RefreshIndicator(
        onRefresh: () async => onRefresh,
        child: Stack(
          children: [
            const SizedBox(width: double.infinity),
            Column(children: [
              Obx(
                () => MyListTile(
                  title: 'Total Penalties: ',
                  subtitle: controller.penaltyCount.value.toString(),
                ),
              ),
              Obx(
                () => MyListTile(
                  title: 'penaltyKittyBalance: ',
                  subtitle: controller.penaltyKittyBalance.value.toString(),
                ),
              ),
              Wrap(spacing: 4.0, children: [
                ActionChip(
                  avatar: const Icon(Icons.add, size: 18),
                  label: const Text('Add a general penalty'),
                  onPressed: () {
                    final titleController = TextEditingController();
                    final descriptionController = TextEditingController();
                    final amountController = TextEditingController();
                    final severityController = TextEditingController();

                    final res = showMyFormDialog(
                        context: context,
                        title: 'Add a general penalty',
                        contents: [
                          MyTextFieldwValidator(
                            title: 'Title:',
                            hint: 'Title',
                            controller: titleController,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter a title';
                              }
                              return null;
                            },
                          ),
                          MyTextFieldwValidator(
                            title: 'Description:',
                            hint: 'Description',
                            controller: descriptionController,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter a description';
                              }
                              return null;
                            },
                          ),
                          MyTextFieldwValidator(
                            title: 'Amount:',
                            hint: 'Amount',
                            controller: amountController,
                            keyboardType: TextInputType.number,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter an amount';
                              }
                              return null;
                            },
                          ),
                        ],
                        onOkPressed: () {
                          controller.addGeneralPenalty(update: false, params: {
                            "title": titleController.text,
                            "description": descriptionController.text,
                            "amount": double.tryParse(amountController.text),
                            "severity":
                                double.tryParse(severityController.text),
                            "status": "ACTIVE"
                          }).whenComplete(() => Navigator.pop(context));
                        });
                  },
                ),
                ActionChip(
                  avatar: const Icon(Icons.update_rounded, size: 18),
                  label: const Text('Update general penalty'),
                  onPressed: () {
                    final titleController = TextEditingController();
                    final descriptionController = TextEditingController();
                    final amountController = TextEditingController();
                    final severityController = TextEditingController();

                    final res = showMyFormDialog(
                        context: context,
                        title: 'Add a general penalty',
                        contents: [
                          MyTextFieldwValidator(
                            title: 'Title:',
                            hint: 'Title',
                            controller: titleController,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter a title';
                              }
                              return null;
                            },
                          ),
                          MyTextFieldwValidator(
                            title: 'Description:',
                            hint: 'Description',
                            controller: descriptionController,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter a description';
                              }
                              return null;
                            },
                          ),
                          MyTextFieldwValidator(
                            title: 'Amount:',
                            hint: 'Amount',
                            controller: amountController,
                            keyboardType: TextInputType.number,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter an amount';
                              }
                              return null;
                            },
                          ),
                        ],
                        onOkPressed: () {
                          controller.addGeneralPenalty(update: true, params: {
                            // TODO OBTAIN ID
                            "ID": 2,
                            "title": titleController.text,
                            "description": descriptionController.text,
                            "amount": double.tryParse(amountController.text),
                            "severity":
                                double.tryParse(severityController.text),
                            "status": "ACTIVE"
                          }).whenComplete(() => Navigator.pop(context));
                        });
                  },
                ),
                ActionChip(
                  avatar: const Icon(Icons.delete, size: 18),
                  label: const Text('Delete general penalty'),
                  onPressed: () {},
                ),
              ]),
            ]),
            Obx(() => controller.isGetAllChamaDetailsLoading.value
                ? Positioned.fill(
                    child: ColoredBox(
                        color: Colors.black.withOpacity(0.9),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SpinKitDualRing(
                              color: ColorUtil.blueColor,
                              lineWidth: 4,
                              size: 40.0,
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              "loading..",
                              style: TextStyle(
                                color: Colors.white,
                              ),
                            )
                          ],
                        )))
                : const SizedBox())
          ],
        ),
      ),
    );
  }
}
