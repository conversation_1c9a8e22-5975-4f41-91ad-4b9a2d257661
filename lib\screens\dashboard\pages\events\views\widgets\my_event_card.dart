import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onekitty/models/events/events_model.dart';
import 'package:onekitty/screens/dashboard/pages/events/views/screens/view_single_event_organizer.dart';
import 'package:onekitty/screens/dashboard/pages/events/views/widgets/attendees_widget.dart';
import 'package:onekitty/utils/date_formatter.dart';
import 'package:onekitty/screens/dashboard/pages/events/views/widgets/my_quill_editor.dart' show QuillEditorShortWidget;
import 'package:onekitty/utils/show_cached_network_image.dart';
import 'package:onekitty/utils/utils_exports.dart';
import '../../../../../../utils/event_details.dart';
import 'package:get/get.dart';
import '../../../../../../main.dart' as main show isLight;
import 'package:onekitty/screens/dashboard/pages/events/controllers/view_single_event.dart';
import '../../../../../../utils/event_description_optimizer.dart';

// Custom page route for smoother hero animations
class HeroPageRoute<T> extends PageRouteBuilder<T> {
  final Widget page;
  
  HeroPageRoute({required this.page})
      : super(
          pageBuilder: (context, animation, secondaryAnimation) => page,
          transitionDuration: const Duration(milliseconds: 600),
          reverseTransitionDuration: const Duration(milliseconds: 500),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            // Fade animation
            var fadeAnimation = CurvedAnimation(
              parent: animation,
              curve: Curves.easeInOut,
            );
            
            // Scale animation
            var scaleAnimation = Tween<double>(
              begin: 0.95,
              end: 1.0,
            ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.easeOutCubic,
            ));
            
            return FadeTransition(
              opacity: fadeAnimation,
              child: ScaleTransition(
                scale: scaleAnimation,
                child: child,
              ),
            );
          },
        );
}

class MyEventCards extends StatefulWidget {
  final bool? organizer;
  final MyEventsModel eventModel;
  const MyEventCards({super.key, required this.eventModel, this.organizer});

  @override
  State<MyEventCards> createState() => _MyEventCardsState();
}

class _MyEventCardsState extends State<MyEventCards> with AutomaticKeepAliveClientMixin {
  // Cache unique suffix to maintain consistency for hero animations
  late final String _uniqueSuffix;

  @override
  bool get wantKeepAlive => true; // Keep widget alive to maintain state

  @override
  void initState() {
    super.initState();
    _initializeUniqueSuffix();
  }

  void _initializeUniqueSuffix() {
    final event = widget.eventModel.event;
    final eventId = event.id ?? 0;
    // Generate unique suffix once and cache it for hero animations
    _uniqueSuffix = '${eventId}_${widget.eventModel.hashCode}';
  }

  // Compute values dynamically to ensure they reflect current event data
  String get _imageUrl {
    final event = widget.eventModel.event;
    return (event.eventMedia?.isNotEmpty == true && event.eventMedia![0].url?.isNotEmpty == true)
        ? event.eventMedia![0].url!
        : AssetUrl.onekittyBannnerUrl;
  }

  String get _locationText {
    final event = widget.eventModel.event;
    return "${event.locationTip} - ${event.venue}";
  }

  String get _dateTimeText {
    final event = widget.eventModel.event;
    if (event.startDate != null) {
      final localDate = event.startDate!.toLocal();
      return '${formatDate(localDate.toString())}, ${formatTime(localDate.toString())}';
    } else {
      return 'Date TBD';
    }
  }

  String get _formattedBalance {
    final event = widget.eventModel.event;
    return FormattedCurrency.getFormattedCurrency(event.balance ?? 0.0);
  }

  String get _heroImageTag {
    final eventId = widget.eventModel.event.id ?? 0;
    return 'image:${eventId}o:card:$_uniqueSuffix';
  }

  String get _heroTextTag {
    final eventId = widget.eventModel.event.id ?? 0;
    return 'text:${eventId}o:$_uniqueSuffix';
  }

  String get _heroCollectedTag {
    final eventId = widget.eventModel.event.id ?? 0;
    return 'collected${eventId}o:$_uniqueSuffix';
  }

  String get _locationId {
    final eventId = widget.eventModel.event.id ?? 0;
    return "location${eventId}o:$_uniqueSuffix";
  }

  String get _dateId {
    final eventId = widget.eventModel.event.id ?? 0;
    return "date${eventId}o:$_uniqueSuffix";
  }

  String get _quillTag {
    final eventId = widget.eventModel.event.id ?? 0;
    return EventDescriptionOptimizer.generateUniqueTag(eventId, _uniqueSuffix);
  }

  void _navigateToEvent() {
    // Clear any cached descriptions to prevent showing previous event data
    EventDescriptionOptimizer.clearAllCaches();

    // Update the event model with the hero tag suffix before navigating
    final updatedEventModel = MyEventsModel(
      event: widget.eventModel.event,
      count: widget.eventModel.count,
      hasSignatoryTransactions: widget.eventModel.hasSignatoryTransactions,
      heroTagSuffix: _uniqueSuffix,
    );

    // Properly set the event in the controller to clear previous state
    final controller = Get.put(ViewSingleEventController());
    controller.setEvent(widget.eventModel.event);

    // Use custom page route for smoother hero animations
    Navigator.push(
      context,
      HeroPageRoute(
        page: ViewSingleEventOrganizer(
          eventmodel: updatedEventModel,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return GestureDetector(
      onTap: _navigateToEvent,
      child: ValueListenableBuilder<bool>(
        valueListenable: main.isLight,
        builder: (context, isLight, _) {
          return Card(
            elevation: 4,
            margin: const EdgeInsets.all(8),
            color: isLight ? Colors.white : const Color(0xff26262e),
            child: Padding(
              padding: EdgeInsets.only(
                left: 6.spMin,
                right: 6.spMin,
                top: 6.spMin,
                bottom: 10.spMin,
              ),
              child: Column(
                children: [
                  Hero(
                    tag: _heroImageTag,
                    child: ShowCachedNetworkImage(
                      imageUrl: _imageUrl,
                      width: 390.w,
                      borderRadius: BorderRadius.circular(8.r),
                      height: 300.h,
                      useAdvancedLayout: true,
                    ),
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Material(
                          color: Colors.transparent,
                          textStyle: TextStyle(
                            color: Colors.grey.shade700,
                            fontWeight: FontWeight.w400,
                            fontSize: 14,
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.symmetric(vertical: 8.0),
                                child: Hero(
                                  tag: _heroTextTag,
                                  child: Material(
                                    color: Colors.transparent,
                                    child: Text(
                                      widget.eventModel.event.title,
                                      style: TextStyle(
                                        color: isLight ? Colors.black : Colors.white,
                                        fontWeight: FontWeight.w700,
                                        fontSize: 20,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              EventDetailsWidget(
                                id: _locationId,
                                label: _locationText,
                                image: 'assets/images/icons/location.png',
                                icon: Icons.location_on_outlined,
                              ),
                              EventDetailsWidget(
                                id: _dateId,
                                image: 'assets/images/icons/calendar.png',
                                label: _dateTimeText,
                                icon: Icons.calendar_month,
                              ),
                            ],
                          ),
                        ),
                      ),
                      if (widget.organizer ?? false)
                        Hero(
                          tag: _heroCollectedTag,
                          child: Material(
                            color: Colors.transparent,
                            child: Text.rich(
                              textAlign: TextAlign.end,
                              TextSpan(
                                children: [
                                  TextSpan(
                                    text: "$_formattedBalance\n",
                                    style: const TextStyle(
                                      color: Color(0xff4355b6),
                                      fontSize: 30,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  const TextSpan(text: 'Collected'),
                                ],
                              ),
                            ),
                          ),
                        )
                    ],
                  ),
                  Align(
                    alignment: Alignment.topLeft,
                    child: QuillEditorShortWidget(
                      maxLines: 2,
                      text: widget.eventModel.event.description,
                      tag: _quillTag,
                    ),
                  ),
                  if (widget.organizer ?? false)
                    AttendeesWidget(
                      count: widget.eventModel.count,
                      size: 18,
                      textSize: 15,
                    )
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
