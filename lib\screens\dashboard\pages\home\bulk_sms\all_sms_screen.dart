import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:grouped_list/grouped_list.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/bulksms_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/models/bulkSms/msg_model.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/widgets/sms_card.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/utils/size_config.dart';

import '../../../../../utils/utils_exports.dart';

class AllSmsScreen extends StatefulWidget {
  const AllSmsScreen({
    super.key,
  });

  @override
  State<AllSmsScreen> createState() => _AllSmsScreenState();
}

@override
class _AllSmsScreenState extends State<AllSmsScreen> {
  TextEditingController searchController = TextEditingController();

  TextEditingController messageController = TextEditingController();

  final UserKittyController _userController = Get.put(UserKittyController());
  final BulkSMSController _bulkSmsController = Get.put(BulkSMSController());

  String greeting = getGreeting();
  final dateformat = DateFormat('EE, dd MMMM');

  @override
  void initState() {
    _loadMessages();

    super.initState();
  }

  Future<void> _loadMessages() async {
    _bulkSmsController.isgetloading(true);
    try {
      await _bulkSmsController.getMessages(
        phoneNumber: _userController.getLocalUser()?.phoneNumber,
      );
      setState(() {});
    } catch (e) {
      print("Error loading messages: $e");
      // Handle error appropriately
    }
    _bulkSmsController.isgetloading(false);
  }

  @override
  void dispose() {
    _bulkSmsController.reset();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        //appBar: buildAppBar(context),
        resizeToAvoidBottomInset: false,
        body: GetBuilder(
          builder: (BulkSMSController smsController) {
            return Padding(
              padding: EdgeInsets.only(left: 20.w, right: 20.w, top: 10.h),
              child: Container(
                width: double.maxFinite,
                decoration: AppDecoration.fillGray,
                child: Column(
                  children: [
                    const RowAppBar(),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 24.w),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            children: [
                              Text(
                                "Total SMS Sent",
                                style: theme.textTheme.bodySmall,
                              ),
                              SizedBox(height: 1.h),
                              GetBuilder(
                                builder: (BulkSMSController bulkSMSController) {
                                  if (bulkSMSController.isgetloading.isTrue) {
                                    return const Text(
                                      "Loading...",
                                      style: TextStyle(
                                          color:
                                              Color.fromARGB(255, 17, 18, 18),
                                          fontSize: 15,
                                          fontStyle: FontStyle.italic),
                                    );
                                  }
                                  return Text(
                                    "${bulkSMSController.results.value!.total}",
                                    style: const TextStyle(
                                        color: Color.fromARGB(255, 5, 78, 138),
                                        fontSize: 25),
                                  );
                                },
                              ),
                            ],
                          ),
                          Column(
                            children: [
                              Text(
                                "My Wallet",
                                style: theme.textTheme.bodySmall,
                              ),
                              Text(
                                FormattedCurrency().getFormattedCurrency(
                                  _userController
                                          .getLocalUser()
                                          ?.balance
                                          ?.toStringAsFixed(2) ??
                                      "",
                                ),
                                style: const TextStyle(
                                    color: Color.fromARGB(255, 5, 78, 138),
                                    fontSize: 25),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 23.h),
                    Text(
                      "Send Bulk SMS",
                      style: theme.textTheme.titleLarge,
                    ),
                    SizedBox(height: 13.h),
                    Text(
                      "Conveniently send messages to people at once",
                      style: CustomTextStyles.bodyMediumBluegray700,
                    ),
                    SizedBox(height: 22.h),
                    // Align(
                    //   alignment: Alignment.centerLeft,
                    //   child: CustomSearchView(
                    //     controller: searchController,
                    //     hintText: "Search",
                    //     alignment: Alignment.centerLeft,
                    //   ),
                    // ),
                    // SizedBox(height: 26.h),
                    Align(
                      alignment: Alignment.centerLeft,
                      child: GetBuilder(
                        builder: (BulkSMSController _bulkSmsController) {
                          if (_bulkSmsController.messages.isEmpty) {
                            return Column(
                              children: [
                                Center(
                                  child: Text(
                                    "You Do not Have any Messages Yet",
                                    style: CustomTextStyles.titleMediumBlack900,
                                  ),
                                ),
                                SizedBox(height: 10.h),
                                CustomElevatedButton(
                                  onPressed: () {
                                    Get.toNamed(NavRoutes.crtsmsScreen);
                                    // Navigator.pushNamed(context, NavRoutes.crtsmsScreen);
                                  },
                                  text: "Try it Now",
                                  buttonStyle: CustomButtonStyles.fillPrimary,
                                  buttonTextStyle: theme.textTheme.titleMedium!,
                                ),
                              ],
                            );
                          }
                          return Text(
                            "Messages sent",
                            style: CustomTextStyles.titleMediumBlack900,
                          );
                        },
                      ),
                    ),
                    SizedBox(height: 10.h),
                    _buildMessages(context),
                  ],
                ),
              ),
            );
          },
        ),
        floatingActionButton: CustomElevatedButton(
          buttonStyle: CustomButtonStyles.fillPrimary,
          width: 140.w,
          height: 30.h,
          text: "Send Message",
          buttonTextStyle:
              TextStyle(fontSize: 12.h, fontWeight: FontWeight.bold),
          leftIcon: Container(
            margin: EdgeInsets.only(right: 5.w),
            color: Colors.white,
            child: CustomImageView(
                imagePath: AssetUrl.message, height: 12.h, width: 12.w),
          ),
          onPressed: () {
            Get.toNamed(NavRoutes.crtsmsScreen);
          },
          alignment: Alignment.bottomRight,
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      ),
    );
  }

  /// Section Widget
  Widget _buildMessages(BuildContext context) {
    return GetX<BulkSMSController>(
      builder: (smsController) {
        if (smsController.isgetloading.isTrue) {
          return SizedBox(
            height: SizeConfig.screenHeight * .33,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SpinKitDualRing(
                    color: ColorUtil.blueColor,
                    lineWidth: 4.sp,
                    size: 40.0.sp,
                  ),
                  const Text(
                    "loading..",
                    style: TextStyle(
                      color: Colors.white,
                      fontStyle: FontStyle.italic,
                    ),
                  )
                ],
              ),
            ),
          );
        }
        if (smsController.messages.isEmpty) {
          return Expanded(
            child: Container(
              height: 900.h,
              width: double.maxFinite,
              padding: EdgeInsets.symmetric(
                horizontal: 32.w,
                vertical: 5.h,
              ),
              child: Column(
                children: [
                  CustomImageView(
                    imagePath: AssetUrl.imgGroup7,
                    height: 174.h,
                    width: 325.w,
                  ),
                  SizedBox(height: 5.h),
                  Text(
                    "Send Bulk SMS",
                    style: theme.textTheme.titleLarge,
                  ),
                  SizedBox(height: 13.h),
                  Text(
                    "Conveniently send messages to people at once.",
                    style: CustomTextStyles.bodyMediumBluegray700,
                  ),
                ],
              ),
            ),
          );
        }
        return Expanded(
          child: Column(
            children: [
              Expanded(
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 16.w,
                      vertical: 18.h,
                    ),
                    decoration: AppDecoration.outlineGray.copyWith(
                      borderRadius: BorderRadiusStyle.circleBorder22,
                    ),
                    child: GroupedListView<msgItem, DateTime>(
                      controller: smsController.chcontroller,
                      sort: false,
                      elements: smsController.messages,
                      groupBy: (msgItem element) {
                        DateTime date = element.createdAt!;
                        return DateTime(date.year, date.month, date.day);
                      },
                      groupHeaderBuilder: (msgItem value) {
                        final date = dateformat.format(value.createdAt!);
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8.0),
                          child: Text(
                            date,
                            style: const TextStyle(
                              color: Colors.black,
                              fontWeight: FontWeight.bold,
                              fontSize: 18.0,
                            ),
                          ),
                        );
                      },
                      itemBuilder: (_, msgItem item) {
                        return SmsCardWidget(item: item);
                      },
                      separator: const Padding(
                        padding: EdgeInsets.symmetric(vertical: 8.0),
                      ),
                    ),
                  ),
                ),
              ),
              if (smsController.loadingMore.isTrue)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Column(
                    children: [
                      SpinKitDualRing(
                        color: ColorUtil.blueColor,
                        lineWidth: 4.sp,
                        size: 40.0.sp,
                      ),
                      const Text(
                        "loading..",
                        style: TextStyle(
                          color: Colors.white,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}
