// import 'package:appinio_video_player/appinio_video_player.dart';
import 'package:flutter/material.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/utils/size_config.dart';
import 'package:onekitty/utils/utils_exports.dart';

// ignore: must_be_immutable
class VideoWidget extends StatefulWidget {
  VideoWidget({super.key, required this.url});
  String url;
  @override
  _VideoWidgetState createState() => _VideoWidgetState();
}

class _VideoWidgetState extends State<VideoWidget> {
  // late CachedVideoPlayerController _videoPlayerController;
  // late CustomVideoPlayerController _customVideoPlayerController;

  @override
  void initState() {
    super.initState();
    // _videoPlayerController = CachedVideoPlayerController.network(
    //   widget.url,
    // )..initialize().then((value) => setState(() {}));
    // _customVideoPlayerController = CustomVideoPlayerController(
    //   context: context,
    //   customVideoPlayerSettings: const CustomVideoPlayerSettings(
    //     allowVolumeOnSlide: true,
    //   ),
    //   videoPlayerController: _videoPlayerController,
    // );
  }

  @override
  void dispose() {
    // _customVideoPlayerController.dispose();
    // _videoPlayerController.pause();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: AppColors.mainPurple,
                borderRadius: BorderRadius.circular(16),
              ),
              height: 120,
              width: SizeConfig.screenWidth * .55,
              child:
                  // _videoPlayerController.value.isInitialized
                  //     ? CustomVideoPlayer(
                  //         customVideoPlayerController: _customVideoPlayerController,
                  //       )
                  // :
                  Image.asset(AssetUrl.img2),
            ),
          ),
        ],
      ),
    );
  }
}
