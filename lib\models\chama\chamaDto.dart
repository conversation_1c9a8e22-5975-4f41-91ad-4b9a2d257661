import 'dart:convert';

CreateDto createDtoFromJson(String str) => CreateDto.fromJson(json.decode(str));

String createDtoToJson(CreateDto data) => json.encode(data.toJson());

class CreateDto {
  String title;
  dynamic description;
  String phoneNumber;
  String countryCode;
  String email;
  int? refererCode;
  String whatsAppLink;
  String frequency;
  DateTime nextOccurrence;
  num amount;
  String? imeiCode;
  String? deviceModel;
  String? latitude;
  String? longitude;

  CreateDto({
    required this.title,
    required this.description,
    required this.phoneNumber,
    required this.countryCode,
    required this.email,
    this.refererCode,
    required this.whatsAppLink,
    required this.frequency,
    required this.nextOccurrence,
    required this.amount,
    required this.imeiCode,
    required this.deviceModel,
    required this.latitude,
    required this.longitude,
  });

  factory CreateDto.fromJson(Map<String, dynamic> json) => CreateDto(
      title: json["title"],
      description: json["description"],
      phoneNumber: json["phone_number"],
      countryCode: json["country_code"],
      email: json["email"],
      refererCode: json["referer_code"],
      whatsAppLink: json["whats_app_link"],
      frequency: json["frequency"],
      nextOccurrence: DateTime.parse(json["next_occurrence"]),
      amount: json["amount"],
      imeiCode: json["imei_code"],
      deviceModel: json["device_model"],
      latitude: json["latitude"],
      longitude: json["longitude"]);

  Map<String, dynamic> toJson() => {
        "title": title,
        "description": description,
        "phone_number": phoneNumber,
        "country_code": countryCode,
        "email": email,
        "referer_code": refererCode,
        "whats_app_link": whatsAppLink,
        "frequency": frequency,
        "next_occurrence": nextOccurrence.toIso8601String(),
        "amount": amount,
        "device_model": deviceModel,
        "imei_code": imeiCode,
        "latitude": latitude,
        "longitude": longitude
      };
}

MembersDto membersDtoFromJson(String str) =>
    MembersDto.fromJson(json.decode(str));

String membersDtoToJson(MembersDto data) => json.encode(data.toJson());

class MembersDto {
  int chamaId;
  List<Member> members;

  MembersDto({
    required this.chamaId,
    required this.members,
  });

  factory MembersDto.fromJson(Map<String, dynamic> json) => MembersDto(
        chamaId: json["chama_id"],
        members:
            List<Member>.from(json["members"].map((x) => Member.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "chama_id": chamaId,
        "members": List<dynamic>.from(members.map((x) => x.toJson())),
      };
}

class Member {
  String phoneNumber;
  String firstName;
  String secondName;
  String role;
  int receivingOrder;
  String status;

  Member({
    required this.phoneNumber,
    required this.firstName,
    required this.secondName,
    required this.role,
    required this.receivingOrder,
    required this.status,
  });

  factory Member.fromJson(Map<String, dynamic> json) => Member(
        phoneNumber: json["phone_number"],
        firstName: json["first_name"],
        secondName: json["second_name"],
        role: json["role"],
        receivingOrder: json["receiving_order"],
        status: json["status"],
      );

  Map<String, dynamic> toJson() => {
        "phone_number": phoneNumber,
        "first_name": firstName,
        "second_name": secondName,
        "role": role,
        "receiving_order": receivingOrder,
        "status": status,
      };
}

// To parse this JSON data, do
//
//     final setOrderDto = setOrderDtoFromJson(jsonString);

SetOrderDto setOrderDtoFromJson(String str) =>
    SetOrderDto.fromJson(json.decode(str));

String setOrderDtoToJson(SetOrderDto data) => json.encode(data.toJson());

class SetOrderDto {
  int chamaId;
  List<member> members;

  SetOrderDto({
    required this.chamaId,
    required this.members,
  });

  factory SetOrderDto.fromJson(Map<String, dynamic> json) => SetOrderDto(
        chamaId: json["chama_id"],
        members:
            List<member>.from(json["members"].map((x) => member.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "chama_id": chamaId,
        "members": List<dynamic>.from(members.map((x) => x.toJson())),
      };
}

class member {
  int id;
  int receivingOrder;

  member({
    required this.id,
    required this.receivingOrder,
  });

  factory member.fromJson(Map<String, dynamic> json) => member(
        id: json["id"],
        receivingOrder: json["receiving_order"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "receiving_order": receivingOrder,
      };
}

// To parse this JSON data, do
//
//     final uploadRDto = uploadRDtoFromJson(jsonString);

UploadRDto uploadRDtoFromJson(String str) =>
    UploadRDto.fromJson(json.decode(str));

String uploadRDtoToJson(UploadRDto data) => json.encode(data.toJson());

class UploadRDto {
  int? id;
  String title;
  String description;
  String? mediaUrl;
  int chamaId;
  String type;

  UploadRDto({
    this.id,
    required this.title,
    required this.description,
    this.mediaUrl,
    required this.chamaId,
    required this.type,
  });

  factory UploadRDto.fromJson(Map<String, dynamic> json) => UploadRDto(
        id: json["ID"],
        title: json["title"],
        description: json["description"],
        mediaUrl: json["media_url"],
        chamaId: json["chama_id"],
        type: json["type"],
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "title": title,
        "description": description,
        "media_url": mediaUrl,
        "chama_id": chamaId,
        "type": type,
      };
}

// To parse this JSON data, do
//
//     final updateDto = updateDtoFromJson(jsonString);

UpdateDto updateDtoFromJson(String str) => UpdateDto.fromJson(json.decode(str));

String updateDtoToJson(UpdateDto data) => json.encode(data.toJson());

class UpdateDto {
  int id;
  String title;
  String description;
  String email;
  String frequency;
  DateTime nextOccurrence;
  num amount;
  String? profile;

  UpdateDto({
    required this.id,
    required this.title,
    required this.description,
    required this.email,
    required this.frequency,
    required this.nextOccurrence,
    required this.amount,
    this.profile
  });

  factory UpdateDto.fromJson(Map<String, dynamic> json) => UpdateDto(
        id: json["id"],
        title: json["title"],
        description: json["description"],
        email: json["email"],
        frequency: json["frequency"],
        amount: json["amount"],
        nextOccurrence: DateTime.parse(json["next_occurrence"]),
        profile: json['profile_url'] ?? '', 
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "description": description,
        "email": email,
        "frequency": frequency,
        "amount": amount,
        "next_occurrence": nextOccurrence.toIso8601String(),
        "profile_url" : profile ?? '',
      };
}

// To parse this JSON data, do
//
//     final updateBenf = updateBenfFromJson(jsonString);

UpdateBenf updateBenfFromJson(String str) =>
    UpdateBenf.fromJson(json.decode(str));

String updateBenfToJson(UpdateBenf data) => json.encode(data.toJson());

class UpdateBenf {
  int id;
  int memberId;
  int chamaId;
  String transferMode;
  int paymentChannel;
  int Channel;
  String account;
  String accountRef;
  double ratio;

  UpdateBenf({
    required this.id,
    required this.memberId,
    required this.chamaId,
    required this.transferMode,
    required this.paymentChannel,
    required this.Channel,
    required this.account,
    required this.accountRef,
    required this.ratio,
  });

  factory UpdateBenf.fromJson(Map<String, dynamic> json) => UpdateBenf(
        id: json["ID"],
        memberId: json["member_id"],
        chamaId: json["chama_id"],
        transferMode: json["transfer_mode"],
        paymentChannel: json["payment_channel"],
        Channel: json["channel"],
        account: json["account_number"],
        accountRef: json["account_number_ref"],
        ratio: json["percentage"]?.toDouble(),
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "member_id": memberId,
        "chama_id": chamaId,
        "transfer_mode": transferMode,
        "payment_channel": paymentChannel,
        "channel": Channel,
        "account_number": account,
        "account_number_ref": accountRef,
        "percentage": ratio,
      };
}
