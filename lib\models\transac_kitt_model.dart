import 'dart:convert';

import 'events/tickets_model.dart';

Transaction transactionModelFromJson(String str) =>
    Transaction.fromJson(json.decode(str));

String transactionModelToJson(Transaction data) => json.encode(data.toJson());

class Transaction {
  bool? status;
  String? message;
  DataClass? data;

  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;

  Transaction({
    this.status,
    this.message,
    this.data,
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
  });

  factory Transaction.fromJson(Map<String, dynamic> json) => Transaction(
        status: json["status"],
        message: json["message"],
        data: json["data"] != null ? DataClass.fromJson(json["data"]) : null,
        id: json["ID"] ?? 0,
        createdAt: json["CreatedAt"] != null
            ? DateTime.parse(json["CreatedAt"])
            : null,
        updatedAt: json["UpdatedAt"] != null
            ? DateTime.parse(json["UpdatedAt"])
            : null,
        deletedAt: json["DeletedAt"],
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data?.toJson(),
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String() ?? '',
        "UpdatedAt": updatedAt?.toIso8601String() ?? '',
        "DeletedAt": deletedAt,
      };
}

class DataClass {
  Results? results;

  DataClass({
    this.results,
  });

  factory DataClass.fromJson(Map<String, dynamic> json) => DataClass(
        results:
            json["results"] != null ? Results.fromJson(json["results"]) : null,
      );

  Map<String, dynamic> toJson() => {
        "results": results?.toJson(),
      };
}

class Results {
  List<TransactionModel>? items;
  int? page;
  int? size;
  int? maxPage;
  int? totalPages;
  int? total;
  bool? last;
  bool? first;
  int? visible;

  Results({
    this.items,
    this.page,
    this.size,
    this.maxPage,
    this.totalPages,
    this.total,
    this.last,
    this.first,
    this.visible,
  });

  factory Results.fromJson(Map<String, dynamic> json) => Results(
        items: List<TransactionModel>.from(
            (json["items"] ?? []).map((x) => TransactionModel.fromJson(x))),
        page: json["page"],
        size: json["size"],
        maxPage: json["max_page"],
        totalPages: json["total_pages"],
        total: json["total"],
        last: json["last"],
        first: json["first"],
        visible: json["visible"],
      );

  Map<String, dynamic> toJson() => {
        "items": List<dynamic>.from(items!.map((x) => x.toJson())),
        "page": page,
        "size": size,
        "max_page": maxPage,
        "total_pages": totalPages,
        "total": total,
        "last": last,
        "first": first,
        "visible": visible,
      };
}

class TransactionModel {
  final int? eventId;
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? deletedAt;
  final String? firstName;
  final String? secondName;
  final String? transactionCode;
  final String? transactionCodeOther;
  final String? internalId;
  final String? phoneNumber;
  final String? accounNumber;
  final String? accounNumberRef;
  final int? kittyId;
  final String? checkoutRequestId;
  final String? channelCode;
  final String? transactionDate;
  final num? amount;
  final String? transAmount;
  final String? status;
  final String? transactionType;
  final String? transactionCategory;
  final String? currencyCode;
  final String? typeInOut;
  final String? product;
  final int? metadataId;
  final bool? showNumber;
  final bool? showNames;
  final List<TransactionTicket>? transactionTicket;
  final String? accountNumber;
  final String? paymentRef;
  final int? memberId;
  final dynamic penaltyId;
  final int? userId;
  final String? channelName;

  TransactionModel({
    this.accountNumber,
    this.paymentRef,
    this.memberId,
    this.penaltyId,
    this.userId,
    this.channelName,
    this.id,
    this.eventId,
    this.transactionTicket,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.firstName,
    this.secondName,
    this.transactionCode,
    this.transactionCodeOther,
    this.internalId,
    this.currencyCode,
    this.phoneNumber,
    this.kittyId,
    this.typeInOut,
    this.checkoutRequestId,
    this.channelCode,
    this.transactionDate,
    this.amount,
    this.transAmount,
    this.status,
    this.accounNumber,
    this.accounNumberRef,
    this.transactionType,
    this.transactionCategory,
    this.product,
    this.metadataId,
    this.showNumber,
    this.showNames,
  });

  factory TransactionModel.fromJson(Map<String, dynamic> json) =>
      TransactionModel(
        id: json["ID"],
        createdAt: json["CreatedAt"] != null
            ? DateTime.parse(json["CreatedAt"])
            : null,
        updatedAt: json["UpdatedAt"] != null
            ? DateTime.parse(json["UpdatedAt"])
            : null,
        deletedAt: json["DeletedAt"],
        accounNumber: json["account_number"],
        accounNumberRef: json["account_number_ref"],
        firstName: json["first_name"],
        secondName: json["second_name"],
        currencyCode: json["currency_code"],
        typeInOut: json["type_in_out"],
        transactionCode: json["transaction_code"],
        transactionCodeOther: json["transaction_code_other"],
        internalId: json["internal_id"],
        phoneNumber: json["phone_number"],
        kittyId: json["kitty_id"],
        checkoutRequestId: json["checkout_request_id"],
        channelCode: json["channel_code"],
        transactionDate: json["transaction_date"],
        amount: num.tryParse(json["amount"]?.toString() ?? '0.0'),
        transAmount: json["trans_amount"],
        status: json["status"],
        transactionType: json["transaction_type"],
        transactionCategory: json["transaction_category"],
        product: json["product"],
        metadataId: json["metadata_id"],
        showNumber: json["show_number"],
        showNames: json["show_names"],
        accountNumber: json["account_number"] ?? '',
        paymentRef: json["payment_ref"] ?? '',
        memberId: json["member_id"] ?? 0,
        penaltyId: json["penalty_id"],
        userId: json["user_id"] ?? 0,
        channelName: json["channel_name"] ?? '',
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "first_name": firstName,
        "currency_code": currencyCode,
        "second_name": secondName,
        "transaction_code": transactionCode,
        "transaction_code_other": transactionCodeOther,
        "internal_id": internalId,
        "phone_number": phoneNumber,
        "kitty_id": kittyId,
        "type_in_out": typeInOut,
        "checkout_request_id": checkoutRequestId,
        "channel_code": channelCode,
        "transaction_date": transactionDate,
        "amount": amount,
        "trans_amount": transAmount,
        "status": status,
        "transaction_type": transactionType,
        "transaction_category": transactionCategory,
        "product": product,
        "metadata_id": metadataId,
        "show_number": showNumber,
        "show_names": showNames,
        "account_number": accountNumber,
        "payment_ref": paymentRef,
        "member_id": memberId,
        "penalty_id": penaltyId,
        "user_id": userId,
        "channel_name": channelName,
      };
}

enum TransactionsModelStatus { INITIALIZED, SUCCESS }

final transactionsModelStatusValues = EnumValues({
  "INITIALIZED": TransactionsModelStatus.INITIALIZED,
  "SUCCESS": TransactionsModelStatus.SUCCESS
});

class TransactionTicket {
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final int? amount;
  final int? quantity;
  final dynamic confirmedById;
  final ConfirmedBy? confirmedBy;
  final TransactionsModelStatus? status;
  final int? ticketId;
  final Ticket? ticket;
  final int? transactionId;
  final Transaction? transaction;
  final String? internalReference;

  TransactionTicket({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.amount = 0,
    this.quantity = 0,
    this.confirmedById,
    this.confirmedBy,
    this.status,
    this.ticketId = 0,
    this.ticket,
    this.transactionId = 0,
    this.transaction,
    this.internalReference = '',
  });

  factory TransactionTicket.fromJson(Map<String, dynamic> json) =>
      TransactionTicket(
        id: json["ID"],
        createdAt: json["CreatedAt"] != null
            ? DateTime.parse(json["CreatedAt"])
            : null,
        updatedAt: json["UpdatedAt"] != null
            ? DateTime.parse(json["UpdatedAt"])
            : null,
        deletedAt: json["DeletedAt"],
        amount: json["amount"] ?? 0,
        quantity: json["quantity"] ?? 0,
        confirmedById: json["confirmed_by_id"],
        confirmedBy: json["confirmed_by"] != null
            ? ConfirmedBy.fromJson(json["confirmed_by"])
            : null,
        status: transactionsModelStatusValues.map[json["status"]],
        ticketId: json["ticket_id"] ?? 0,
        ticket: json["ticket"] != null ? Ticket.fromJson(json["ticket"]) : null,
        transactionId: json["transaction_id"] ?? 0,
        transaction: json["transaction"] != null
            ? Transaction.fromJson(json["transaction"])
            : null,
        internalReference: json["internal_reference"] ?? '',
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String() ?? '',
        "UpdatedAt": updatedAt?.toIso8601String() ?? '',
        "DeletedAt": deletedAt,
        "amount": amount ?? 0,
        "quantity": quantity ?? 0,
        "confirmed_by_id": confirmedById,
        "confirmed_by": confirmedBy?.toJson() ?? {},
        "status": transactionsModelStatusValues.reverse[status] ?? '',
        "ticket_id": ticketId ?? 0,
        "ticket": ticket?.toJson() ?? {},
        "transaction_id": transactionId ?? 0,
        "transaction": transaction?.toJson() ?? {},
        "internal_reference": internalReference ?? '',
      };
}

class ConfirmedBy {
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String? phoneNumber;
  final String? firstName;
  final String? secondName;
  final String? email;
  final int? idNumber;
  final int? balance;
  final String? birthDate;
  final String? countryCode;
  final String? county;
  final String? subCounty;
  final String? latitude;
  final String? longitude;
  final String? secondaryNumber;
  final String? profileUrl;
  final int? status;

  ConfirmedBy({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.phoneNumber = '',
    this.firstName = '',
    this.secondName = '',
    this.email = '',
    this.idNumber = 0,
    this.balance = 0,
    this.birthDate = '',
    this.countryCode = '',
    this.county = '',
    this.subCounty = '',
    this.latitude = '',
    this.longitude = '',
    this.secondaryNumber = '',
    this.profileUrl = '',
    this.status = 0,
  });

  factory ConfirmedBy.fromJson(Map<String, dynamic> json) => ConfirmedBy(
        id: json["ID"],
        createdAt: json["CreatedAt"] != null
            ? DateTime.parse(json["CreatedAt"])
            : null,
        updatedAt: json["UpdatedAt"] != null
            ? DateTime.parse(json["UpdatedAt"])
            : null,
        deletedAt: json["DeletedAt"],
        phoneNumber: json["phone_number"] ?? '',
        firstName: json["first_name"] ?? '',
        secondName: json["second_name"] ?? '',
        email: json["email"] ?? '',
        idNumber: json["id_number"] ?? 0,
        balance: json["balance"] ?? 0,
        birthDate: json["birth_date"] ?? '',
        countryCode: json["country_code"] ?? '',
        county: json["county"] ?? '',
        subCounty: json["sub_county"] ?? '',
        latitude: json["latitude"] ?? '',
        longitude: json["longitude"] ?? '',
        secondaryNumber: json["secondary_number"] ?? '',
        profileUrl: json["profile_url"] ?? '',
        status: json["status"] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String() ?? '',
        "UpdatedAt": updatedAt?.toIso8601String() ?? '',
        "DeletedAt": deletedAt,
        "phone_number": phoneNumber ?? '',
        "first_name": firstName ?? '',
        "second_name": secondName ?? '',
        "email": email ?? '',
        "id_number": idNumber ?? 0,
        "balance": balance ?? 0,
        "birth_date": birthDate ?? '',
        "country_code": countryCode ?? '',
        "county": county ?? '',
        "sub_county": subCounty ?? '',
        "latitude": latitude ?? '',
        "longitude": longitude ?? '',
        "secondary_number": secondaryNumber ?? '',
        "profile_url": profileUrl ?? '',
        "status": status ?? 0,
      };
}

enum Currency { KES }

final currencyValues = EnumValues({"KES": Currency.KES});

enum TransactionType { PURCHASE }

final transactionTypeValues =
    EnumValues({"PURCHASE": TransactionType.PURCHASE});

class EnumValues<T> {
  Map<String, T> map;
  late Map<T, String> reverseMap;

  EnumValues(this.map);

  Map<T, String> get reverse {
    reverseMap = map.map((k, v) => MapEntry(v, k));
    return reverseMap;
  }
}
