import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';

import '../../../../utils/utils_exports.dart';

// ignore_for_file: must_be_immutable
class CrtContributionKittyPage extends StatelessWidget {
  const CrtContributionKittyPage({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: Container(
          width: double.maxFinite,
          decoration: AppDecoration.fillGray,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 32.w),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SizedBox(height: 10.h),
                Sized<PERSON><PERSON>(
                  // height: 200.h,
                  width: 314.w,
                  child: Stack(
                    alignment: Alignment.topCenter,
                    children: [
                      CustomImageView(
                        imagePath: AssetUrl.imgGroup6,
                        height: 179.h,
                        width: 235.w,
                        alignment: Alignment.center,
                      ),
                    ],
                  ),
                ),
                Sized<PERSON><PERSON>(height: 70.h),
                Text(
                  "Create a contribution Kitty",
                  style: theme.textTheme.titleLarge,
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 6.h),
                Container(
                  width: 350.w,
                  margin: EdgeInsets.symmetric(horizontal: 11.w),
                  child: Text(
                    "Create your first kitty today and fuel the spark for something big.",
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                    style: theme.textTheme.headlineSmall,
                  ),
                ),
                SizedBox(height: 22.h),
                _buildCreateAContributionRow(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Section Widget
  Widget _buildCreateAContributionRow(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Expanded(
          child: CustomElevatedButton(
            onPressed: () {
              Navigator.pushNamed(context, NavRoutes.createkittyScreen);
            },
            height: 40.h,
            text: "Create a Kitty",
            margin: EdgeInsets.only(right: 4.w),
          ),
        ),
        Expanded(
          child: CustomOutlinedButton(
            onPressed: () {
              // Get.toNamed(NavRoutes.urlScreen);
              Navigator.pushNamed(context, NavRoutes.urlScreen);
            },
            text: "Contribute to Kitty",
            buttonStyle: CustomButtonStyles.outlinePrimary,
          ),
        ),
      ],
    );
  }
}
