import 'dart:async';

import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/admin/chama/chama_admin_controller.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/controllers/events/controllers.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/models/chama/chama_memebers_model.dart';
import 'package:onekitty/models/chama/chama_model.dart';
import 'package:onekitty/screens/dashboard/pages/chama/members/invite.dart';
import 'package:onekitty/screens/dashboard/pages/chama/members/operations.dart';
import 'package:onekitty/screens/dashboard/pages/chama/signatories/signatories.dart';
import 'package:onekitty/screens/dashboard/pages/chama/viewing_single_chama/transactions/chama_transactions.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/asset_urls.dart';
import 'package:onekitty/utils/custom_button.dart';
import 'package:onekitty/utils/custom_image_view.dart';
import 'package:onekitty/utils/size_config.dart';
import 'package:onekitty/utils/themes_colors.dart';

class ChamaBeneficiariesPage extends StatefulWidget {
  final Chama chama;
  const ChamaBeneficiariesPage({super.key, required this.chama});

  @override
  State<ChamaBeneficiariesPage> createState() => _BeneficiariesState();
}

class _BeneficiariesState extends State<ChamaBeneficiariesPage> {
  final controller = Get.find<ChamaAdminController>();
  int? _sortColumnIndex;

  final roles = <String>[].obs;
  bool _sortAscending = true;

  final chamaDataController = Get.put(ChamaDataController());
  final chamaController = Get.put(ChamaController());

  void _sort<T>(Comparable<T> Function(ChamaMembers chamamember) getField,
      int columnIndex, bool ascending) {
    setState(() {
      _sortColumnIndex = columnIndex;
      _sortAscending = ascending;
      controller.members.sort((a, b) {
        final aValue = getField(a);
        final bValue = getField(b);
        return ascending
            ? Comparable.compare(aValue, bValue)
            : Comparable.compare(bValue, aValue);
      });
    });
  }

  void onRefresh() {
    controller.getAllChamaDetails(chamaId: widget.chama.id!);
  }

  @override
  initState() {
    chamaController.chamaMembers(controller.members);
    chamaDataController.chama.value.chama = widget.chama;
    roles.value = chamaController.roles.map((role) => role.role).toList();
    super.initState();
    // onRefresh();
  }

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: () async => onRefresh,
      child: Scaffold(
        appBar: AppBar(),
        body: Stack(
          children: [
            DefaultTabController(
              length: 3,
              child: Column(
                children: [
                  const TabBar(
                    tabs: [
                      Tab(text: 'Members'),
                      Tab(text: 'Beneficiaries'),
                      Tab(text: 'Signatories'),
                    ],
                  ),
                  Expanded(
                    child: TabBarView(
                      children: [
                        // First tab content
                        // Second tab content
                        Column(
                          children: [
                            BeneficiaryDashBoard(
                              chama: widget.chama,
                            ),
                            Expanded(
                              child: Obx(
                                () => controller.isLoading.value
                                    ? const Center(
                                        child: CircularProgressIndicator())
                                    : DataTable2(
                                        columnSpacing: 12,
                                        horizontalMargin: 12,
                                        minWidth: 600,
                                        sortColumnIndex: _sortColumnIndex,
                                        sortAscending: _sortAscending,
                                        columns: [
                                          DataColumn2(
                                            label: const Text('ID'),
                                            size: ColumnSize.S,
                                            onSort: (columnIndex, ascending) {
                                              _sort<num>(
                                                  (ChamaMember) =>
                                                      ChamaMember.id ?? 0,
                                                  columnIndex,
                                                  ascending);
                                            },
                                          ),
                                          DataColumn2(
                                            label: const Text('Created At'),
                                            onSort: (columnIndex, ascending) {
                                              _sort<DateTime>(
                                                  (ChamaMember) =>
                                                      ChamaMember.createdAt ??
                                                      DateTime.now(),
                                                  columnIndex,
                                                  ascending);
                                            },
                                          ),
                                          DataColumn2(
                                            label: const Text('Updated At'),
                                            onSort: (columnIndex, ascending) {
                                              _sort<DateTime>(
                                                  (ChamaMember) =>
                                                      ChamaMember.updatedAt ??
                                                      DateTime.now(),
                                                  columnIndex,
                                                  ascending);
                                            },
                                          ),
                                          DataColumn2(
                                            label: const Text('first Name'),
                                            size: ColumnSize.L,
                                            onSort: (columnIndex, ascending) {
                                              _sort<String>(
                                                  (ChamaMember) =>
                                                      ChamaMember.firstName ??
                                                      '',
                                                  columnIndex,
                                                  ascending);
                                            },
                                          ),
                                          DataColumn2(
                                            label: const Text('Last Name'),
                                            size: ColumnSize.L,
                                            onSort: (columnIndex, ascending) {
                                              _sort<String>(
                                                  (ChamaMember) =>
                                                      ChamaMember.secondName ??
                                                      '',
                                                  columnIndex,
                                                  ascending);
                                            },
                                          ),
                                          DataColumn2(
                                            label: const Text('Actions'),
                                            size: ColumnSize.L,
                                            onSort: (columnIndex, ascending) {
                                              _sort<String>(
                                                  (ChamaMember) =>
                                                      ChamaMember.secondName ??
                                                      '',
                                                  columnIndex,
                                                  ascending);
                                            },
                                          ),
                                        ],
                                        rows: controller.members
                                            .map((ChamaMember) =>
                                                DataRow2(cells: [
                                                  DataCell(Text(ChamaMember.id
                                                      .toString())),
                                                  DataCell(Text(
                                                      DateFormat('yyyy-MM-dd')
                                                          .format(ChamaMember
                                                                  .createdAt ??
                                                              DateTime.now()))),
                                                  DataCell(Text(
                                                      DateFormat('yyyy-MM-dd')
                                                          .format(ChamaMember
                                                                  .updatedAt ??
                                                              DateTime.now()))),
                                                  DataCell(Text(
                                                      ChamaMember.firstName ??
                                                          '')),
                                                  DataCell(Text(
                                                      ChamaMember.secondName ??
                                                          '')),
                                                  DataCell(
                                                    IconButton(
                                                      icon: const Icon(
                                                        Icons.edit,
                                                        color: AppColors
                                                            .blueButtonColor,
                                                      ),
                                                      padding:
                                                          EdgeInsets.symmetric(
                                                              vertical: 10.h),
                                                      onPressed: () {
                                                        _memberOptionsDialog(
                                                            context,
                                                            ChamaMember.id ?? 0,
                                                            ChamaMember);
                                                      },
                                                    ),
                                                  )
                                                ]))
                                            .toList(),
                                      ),
                              ),
                            ),
                          ],
                        ),
                        //second tab
                        Obx(
                          () => DataTable2(
                            columnSpacing: 12,
                            horizontalMargin: 12,
                            minWidth: 600,
                            columns: const [
                              DataColumn2(
                                  label: Text('First Name'),
                                  size: ColumnSize.M),
                              DataColumn2(
                                  label: Text('Last Name'), size: ColumnSize.M),
                              DataColumn2(
                                  label: Text('Phone Number'),
                                  size: ColumnSize.L),
                              DataColumn2(
                                  label: Text('receiving order'),
                                  size: ColumnSize.S),
                              DataColumn2(
                                  label: Text('Status'), size: ColumnSize.S),
                              DataColumn2(
                                  label: Text('action'), size: ColumnSize.S),
                            ],
                            rows: controller.beneficiaries.map((beneficiary) {
                              return DataRow2(cells: [
                                DataCell(
                                    Text(beneficiary.member?.firstName ?? '')),
                                DataCell(
                                    Text(beneficiary.member?.secondName ?? '')),
                                DataCell(Text(
                                    beneficiary.member?.phoneNumber ?? '')),
                                DataCell(Text(
                                    '${beneficiary.member?.receivingOrder}')),
                                DataCell(
                                    Text(beneficiary.member?.status ?? '')),
                                DataCell(TextButton.icon(
                                    label: const Text('View'),
                                    onPressed: () {
                                      showDialog(
                                        context: context,
                                        builder: (BuildContext context) {
                                          return AlertDialog(
                                            title: const Text(
                                                'Beneficiary Details'),
                                            content: SingleChildScrollView(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                      'First Name: ${beneficiary.member?.firstName ?? 'N/A'}'),
                                                  Text(
                                                      'Last Name: ${beneficiary.member?.secondName ?? 'N/A'}'),
                                                  Text(
                                                      'Phone Number: ${beneficiary.member?.phoneNumber ?? 'N/A'}'),
                                                  Text(
                                                      'Receiving Order: ${beneficiary.member?.receivingOrder ?? 'N/A'}'),
                                                  Text(
                                                      'Status: ${beneficiary.member?.status ?? 'N/A'}'),
                                                  Text(
                                                      'Chama Percentage: ${beneficiary.beneficiaryPercentage ?? 'N/A'}'),
                                                ],
                                              ),
                                            ),
                                            actions: [
                                              TextButton(
                                                onPressed: () =>
                                                    Navigator.of(context).pop(),
                                                child: const Text('Close'),
                                              ),
                                            ],
                                          );
                                        },
                                      );
                                    },
                                    icon: const Icon(Icons.launch_outlined)))
                              ]);
                            }).toList(),
                          ),
                        ),
                        //third tab
                        const Signatories()
                      ],
                    ),
                  )
                ],
              ),
            ),
            Obx(() => controller.isGetAllChamaDetailsLoading.value
                ? Positioned.fill(
                    child: ColoredBox(
                        color: Colors.black.withOpacity(0.9),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SpinKitDualRing(
                              color: ColorUtil.blueColor,
                              lineWidth: 4,
                              size: 40.0,
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              "loading..",
                              style: TextStyle(
                                color: Colors.white,
                              ),
                            )
                          ],
                        )))
                : const SizedBox())
          ],
        ),
      ),
    );
  }

  void _memberOptionsDialog(
      BuildContext context, int index, ChamaMembers member) {
    showDialog(
        context: context,
        builder: (BuildContext context) {
          bool showForm = false;
          bool showDropdown = false;
          TextEditingController firstNameController = TextEditingController();
          TextEditingController lastNameController = TextEditingController();
          TextEditingController role = TextEditingController();

          firstNameController.text = member.firstName!;
          lastNameController.text = member.secondName!;
          String? selectedRole = member.role;

          return StatefulBuilder(
            builder: (BuildContext context, StateSetter setState) {
              return Align(
                alignment: Alignment.centerRight,
                child: AlertDialog(
                  title: Row(
                    children: [
                      const Expanded(
                        child: Text(
                          "Update Member Details",
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                      const SizedBox(
                        width: 4,
                      ),
                      InkWell(
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                        child: CustomImageView(
                          imagePath: AssetUrl.imgIconoirCancel,
                          height: 18.h,
                          width: 18.w,
                          margin: EdgeInsets.symmetric(
                              vertical: 9.h, horizontal: 5.h),
                        ),
                      ),
                    ],
                  ),
                  content: SingleChildScrollView(
                    child: Column(
                      children: [
                        Row(
                          children: [
                            CustomImageView(
                              imagePath: AssetUrl.crownsv,
                            ),
                            InkWell(
                              onTap: () {
                                setState(() {
                                  showDropdown = true;
                                });
                              },
                              child: const Padding(
                                padding: EdgeInsets.all(8.0),
                                child: Text("Update Role"),
                              ),
                            ),
                          ],
                        ),
                        if (showDropdown)
                          Column(
                            children: [
                              Obx(
                                () => DropdownButtonFormField<String>(
                                  decoration: const InputDecoration(
                                    labelText: "Select Role",
                                    // fillColor:
                                        // Colors.blueAccent.withOpacity(0.1),
                                  ),
                                  isExpanded: true,
                                  items: roles
                                      .map(
                                        (String item) =>
                                            DropdownMenuItem<String>(
                                          value: item,
                                          child: Text(
                                            item,
                                            style: const TextStyle(
                                              fontSize: 14,
                                            ),
                                          ),
                                        ),
                                      )
                                      .toList(),
                                  value: selectedRole,
                                  onChanged: (String? value) {
                                    setState(() {
                                      selectedRole = value;
                                      role.text = value!;
                                    });
                                  },
                                ),
                              ),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceEvenly,
                                children: [
                                  TextButton(
                                    onPressed: () {
                                      setState(
                                        () {
                                          showDropdown = false;
                                        },
                                      );
                                    },
                                    child: const Text("cancel"),
                                  ),
                                  const Spacer(),
                                  Obx(
                                    () => CustomKtButton(
                                      isLoading:
                                          chamaController.isUpdatingM.isTrue,
                                      onPress: () async {
                                        final res =
                                            await chamaController.updateRole(
                                                chId: chamaDataController
                                                        .singleChamaDts
                                                        .value
                                                        .id ??
                                                    0,
                                                mId: member.id ?? 0,
                                                role: selectedRole ?? "");
                                        if (res) {
                                          ToastUtils.showInfoToast(
                                            context,
                                            chamaController.apiMessage
                                                .toString(),
                                            "Info",
                                          );
                                          chamaController.getChamaMembers(
                                              chamaId: chamaDataController
                                                      .singleChamaDts
                                                      .value
                                                      .id ??
                                                  0,
                                              sort: "LEADERS");
                                          chamaController.reset();

                                          Navigator.of(context).pop();
                                        } else {
                                          ToastUtils.showErrorToast(
                                            context,
                                            chamaController.apiMessage
                                                .toString(),
                                            "Error",
                                          );
                                        }
                                      },
                                      height: 20.h,
                                      width: 60.w,
                                      btnText: "Save",
                                    ),
                                  ),
                                  SizedBox(
                                    height: 10.sp,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        SizedBox(
                          height: 8.h,
                        ),
                        InkWell(
                          onTap: () {
                            Get.off(() => ChamaTransactionsPage(
                                  isFullPage: false,
                                  isFromMemberTransactions: true,
                                  accountNo: member.phoneNumber,
                                  member: member,
                                ));
                            // showMemberTransactions(
                            //     accountNo: member.phoneNumber ?? "");
                          },
                          child: Row(
                            children: [
                              CustomImageView(
                                imagePath: AssetUrl.scroll,
                              ),
                              Expanded(
                                child: Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Text(
                                      "View all ${member.firstName} transactions"),
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 8.h,
                        ),
                        InkWell(
                          onTap: () {
                            setState(
                              () {
                                showForm = true;
                              },
                            );
                          },
                          child: Row(
                            children: [
                              CustomImageView(
                                imagePath: AssetUrl.contact,
                              ),
                              const Padding(
                                padding: EdgeInsets.all(8.0),
                                child: Text("Update member info"),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 8.h,
                        ),
                        if (showForm)
                          Column(
                            children: [
                              CustomTextField(
                                labelText: "Enter First Name",
                                controller: firstNameController,
                              ),
                              CustomTextField(
                                labelText: "Enter Last Name",
                                controller: lastNameController,
                              ),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceEvenly,
                                children: [
                                  TextButton(
                                    onPressed: () {
                                      setState(
                                        () {
                                          showForm = false;
                                        },
                                      );
                                    },
                                    child: const Text("cancel"),
                                  ),
                                  const Spacer(),
                                  Obx(
                                    () => CustomKtButton(
                                      isLoading:
                                          chamaController.isUpdatingM.isTrue,
                                      onPress: () async {
                                        final updtDto = UpdtMemDto(
                                            chamaId: member.chamaId ?? 0,
                                            memberId: member.id ?? 0,
                                            firstName: firstNameController.text,
                                            secondName: lastNameController.text,
                                            profileUlr: "",
                                            role: selectedRole ?? "",
                                            status: member.status ?? "");
                                        final res = await chamaController
                                            .updateMember(updtDto: updtDto);
                                        if (res) {
                                          setState(() {
                                            showForm = false;
                                          });
                                          ToastUtils.showSuccessToast(
                                              context,
                                              chamaController.apiMessage
                                                  .toString(),
                                              "Success");
                                          chamaController.getChamaMembers(
                                              chamaId: chamaDataController
                                                      .singleChamaDts
                                                      .value
                                                      .id ??
                                                  0,
                                              sort: "LEADERS");
                                          chamaController.reset();
                                          Navigator.of(context).pop();
                                        } else {
                                          setState(() {
                                            showForm = false;
                                          });
                                          ToastUtils.showErrorToast(
                                              context,
                                              chamaController.apiMessage
                                                  .toString(),
                                              "Error");
                                        }
                                      },
                                      height: 20.h,
                                      width: 60.w,
                                      btnText: "Save",
                                    ),
                                  ),
                                  SizedBox(
                                    height: 10.sp,
                                  ),
                                ],
                              )
                            ],
                          ),
                        InkWell(
                          onTap: () {
                            showDialog(
                              context: context,
                              builder: (BuildContext context) {
                                final showAccRef = false.obs;
                                final transferModeController =
                                    TextEditingController(text: 'WALLET');
                                final paymentChannelController =
                                    TextEditingController(text: '63902');
                                final accountController =
                                    TextEditingController();
                                final accountRefController =
                                    TextEditingController();
                                final ratioController = TextEditingController();

                                return AlertDialog(
                                  title: const Text('Add Beneficiary Account'),
                                  content: SingleChildScrollView(
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        DropdownButtonFormField<String>(
                                          decoration: const InputDecoration(
                                              labelText: 'Transfer Mode'),
                                          value: transferModeController.text,
                                          items: [
                                            'WALLET',
                                            'PAYBILL',
                                            'TILL',
                                            'BANK'
                                          ]
                                              .map((mode) => DropdownMenuItem(
                                                    value: mode,
                                                    child: Text(mode),
                                                  ))
                                              .toList(),
                                          onChanged: (value) {
                                            if (value != null) {
                                              if (value == 'PAYBILL') {
                                                showAccRef.value = true;
                                              } else {
                                                showAccRef.value = false;
                                              }
                                              transferModeController.text =
                                                  value;
                                            }
                                          },
                                        ),
                                        const SizedBox(height: 10),
                                        DropdownButtonFormField<int>(
                                          decoration: const InputDecoration(
                                              labelText: 'Transfer Mode'),
                                          value: int.tryParse(
                                                  paymentChannelController
                                                      .text) ??
                                              0,
                                          items: Get.find<GlobalControllers>()
                                              .paymentChannels
                                              .map((mode) => DropdownMenuItem(
                                                    value: mode.channelCode,
                                                    child: FittedBox(
                                                        child: Text(mode.name)),
                                                  ))
                                              .toList(),
                                          onChanged: (value) {
                                            if (value != null) {
                                              transferModeController.text =
                                                  value.toString();
                                            }
                                          },
                                        ),
                                        const SizedBox(height: 10),
                                        CustomTextField(
                                          labelText: 'Account',
                                          controller: accountController,
                                        ),
                                        Obx(
                                          () => showAccRef.value
                                              ? CustomTextField(
                                                  labelText: 'Account Ref',
                                                  controller:
                                                      accountRefController,
                                                )
                                              : const SizedBox(),
                                        ),
                                        const SizedBox(height: 10),
                                        CustomTextField(
                                          labelText: 'Ratio(%)',
                                          controller: ratioController,
                                          keyboardType: const TextInputType
                                              .numberWithOptions(decimal: true),
                                        ),
                                      ],
                                    ),
                                  ),
                                  actions: [
                                    TextButton(
                                      onPressed: () =>
                                          Navigator.of(context).pop(),
                                      child: const Text('Cancel'),
                                    ),
                                    ElevatedButton(
                                      onPressed: () async {
                                        final params = {
                                          "member_id": member.id,
                                          "chama_id": widget.chama.id,
                                          'transfer_mode':
                                              transferModeController.text,
                                          'payment_channel': int.parse(
                                              paymentChannelController.text),
                                          'account': accountController.text,
                                          'account_ref': showAccRef.value
                                              ? accountRefController.text
                                              : null,
                                          'ratio': (double.tryParse(
                                                      ratioController.text) ??
                                                  0) /
                                              100,
                                        };
                                        await Get.find<ChamaAdminController>()
                                            .addBeneficiaryAccount(params);
                                        Navigator.of(context).pop();
                                      },
                                      child: const Text('Add'),
                                    ),
                                  ],
                                );
                              },
                            );
                          },
                          child: const Row(
                            children: [
                              Icon(
                                Icons.add,
                                color: AppColors.primary,
                              ),
                              Padding(
                                padding: EdgeInsets.all(8.0),
                                child: Text("Add Beneficiary Account"),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 8.h),
                        InkWell(
                          onTap: () {
                            setState(
                              () {
                                showPenaltiesBottomSheet(members: member);
                              },
                            );
                          },
                          child: Row(
                            children: [
                              CustomImageView(
                                imagePath: AssetUrl.gravel,
                              ),
                              Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Text(
                                    "Issue a Penalty to ${member.firstName}"),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 8.h,
                        ),
                        InkWell(
                          onTap: () {
                            showMemberPenalties(
                                memberId: member.id ?? 0,
                                memberName:
                                    "${member.firstName} ${member.secondName}");
                          },
                          child: Row(
                            children: [
                              CustomImageView(
                                imagePath: AssetUrl.gravel,
                              ),
                              Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Text(
                                    "View all ${member.firstName} penalties"),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 8.h,
                        ),
                        InkWell(
                          onTap: () async {
                            // Show the confirmation dialog
                            bool confirmed = await _showConfirmationDialog(
                                context, member.firstName ?? "");

                            if (confirmed) {
                              final res = await chamaController.RmMember(
                                  chId: member.chamaId ?? 0,
                                  mId: member.id ?? 0,
                                  status: "REMOVED");
                              if (res) {
                                ToastUtils.showSuccessToast(
                                    context,
                                    chamaController.apiMessage.toString(),
                                    "Success");
                                chamaController.getChamaMembers(
                                    chamaId: chamaDataController
                                            .singleChamaDts.value.id ??
                                        0,
                                    sort: "LEADERS");
                                chamaController.reset();

                                Navigator.pop(context);
                              } else {
                                ToastUtils.showErrorToast(
                                    context,
                                    chamaController.apiMessage.toString(),
                                    "Error");
                                Navigator.pop(context);
                              }
                            }
                          },
                          child: Row(
                            children: [
                              CustomImageView(
                                imagePath: AssetUrl.trash,
                              ),
                              Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Text("Remove ${member.firstName}"),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              );
            },
          );
        });
  }

  Future<bool> _showConfirmationDialog(
      BuildContext context, String memberName) async {
    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'Confirm Removal?',
            style: TextStyle(fontSize: 20),
          ),
          content: Text('Are you sure you want to remove $memberName?'),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(false); // User pressed No
              },
              child: const Text('No'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(true); // User pressed Yes
              },
              child: const Text('Yes'),
            ),
          ],
        );
      },
    ).then((value) => value ?? false); // Ensure a boolean is returned
  }

  void showMemberPenalties(
      {required int memberId, required String memberName}) {
    showModalBottomSheet(
        isScrollControlled: true,
        context: context,
        builder: (context) {
          return DraggableScrollableSheet(
              maxChildSize: 0.97,
              initialChildSize: 0.7,
              expand: false,
              builder: (context, scrollController) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12.0, vertical: 5),
                      child: Text(
                        "${memberName.toUpperCase()} PENALTIES",
                        style: context.titleText
                            ?.copyWith(decoration: TextDecoration.underline),
                      ),
                    ),
                    const SizedBox(height: 7),
                    Expanded(
                      child: GetX(
                          init: ChamaController(),
                          initState: (state) {
                            Future.delayed(Duration.zero, () async {
                              try {
                                await state.controller?.getMemberPenalties(
                                    memeberId: memberId,
                                    chamaId: chamaDataController
                                            .chama.value.chama?.id ??
                                        0);
                              } catch (e) {
                                throw e;
                              }
                            });
                          },
                          builder: (ChamaController chamaController) {
                            if (chamaController
                                .isGetMemberPenaltiesLoading.isTrue) {
                              return SizedBox(
                                height: SizeConfig.screenHeight * .33,
                                child: Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      SpinKitDualRing(
                                        color: ColorUtil.blueColor,
                                        lineWidth: 4.sp,
                                        size: 40.0.sp,
                                      ),
                                      const Text(
                                        "loading..",
                                        style: TextStyle(
                                          color: Colors.white,
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                              );
                            } else if (chamaController
                                .memberPenalties.isEmpty) {
                              return const Padding(
                                padding: EdgeInsets.all(13.0),
                                child: Text("You don't have any penalties yet"),
                              );
                            } else if (chamaController
                                .memberPenalties.isNotEmpty) {
                              return ListView.separated(
                                separatorBuilder: (context, index) {
                                  return const Divider();
                                },
                                itemBuilder: (context, index) {
                                  final memberPenalty =
                                      chamaController.memberPenalties[index];
                                  return Container(
                                    margin: const EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 12),
                                    child: ListTile(
                                      leading: Text("${index + 1}"),
                                      title: Text(
                                          "REASON: ${memberPenalty.reason}",
                                          style: context.dividerTextLarge),
                                      subtitle: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                              "STATUS: ${memberPenalty.status}"),
                                          Text(
                                              "AMOUNT PAID: ${memberPenalty.paidAmount ?? "0"}"),
                                          Text(
                                              "ISSUED ON: ${DateFormat("yyyy-MM-dd").format(memberPenalty.createdAt ?? DateTime.now())}")
                                        ],
                                      ),
                                      trailing: Text("${memberPenalty.amount}",
                                          style: context.dividerTextLarge
                                              ?.copyWith(color: Colors.red)),
                                    ),
                                  );
                                },
                                itemCount:
                                    chamaController.memberPenalties.length,
                              );
                            }
                            return const Text("No penalties here yet");
                          }),
                    ),
                  ],
                );
              });
        });
  }

  void showPenaltiesBottomSheet({required ChamaMembers members}) {
    showModalBottomSheet(
        isScrollControlled: true,
        context: context,
        builder: (context) {
          return DraggableScrollableSheet(
              maxChildSize: 0.97,
              initialChildSize: 0.87,
              expand: false,
              builder: (context, scrollController) {
                return PenaltiesBottomSheet(
                  member: members,
                );
              });
        });
  }
}

class BeneficiaryDashBoard extends StatelessWidget {
  final Chama chama;
  const BeneficiaryDashBoard({super.key, required this.chama});

  @override
  Widget build(BuildContext context) {
    RxBool isSearching = false.obs;
    final status = 'Active'.obs;
    final textController = TextEditingController();
    final controller = Get.find<ChamaAdminController>();
    String filter = '';
    Timer? _debounce;
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: textController,
                  style: TextStyle(fontSize: 14.spMin),
                  onChanged: (val) {
                    if (_debounce?.isActive ?? false) {
                      _debounce!.cancel();
                    }

                    _debounce = Timer(const Duration(seconds: 1), () async {
                      // Trigger the searching state
                      isSearching(true);

                      // Perform the search operation
                      controller
                          .getChamaTrnsactions(
                              chamaId: chama.id!, page: 0, filters: filter)
                          .whenComplete(() {
                        isSearching(false);
                      }).onError((e, s) => isSearching(false));
                    });
                  },
                  decoration: InputDecoration(
                    hintText: ' Search',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(25),
                      borderSide:
                          const BorderSide(color: primaryColor, width: 0.4),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(25),
                      borderSide:
                          const BorderSide(color: primaryColor, width: 0.4),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(25),
                      borderSide:
                          const BorderSide(color: primaryColor, width: 0.4),
                    ),
                    hintStyle: TextStyle(
                      fontWeight: FontWeight.w400,
                      fontSize: 16.spMin,
                    ),
                    suffixIcon: isSearching.value
                        ? SizedBox(
                            height: 16.spMin,
                            width: 16.spMin,
                            child: const CupertinoActivityIndicator(),
                          )
                        : IconButton(
                            onPressed: () async {},
                            icon: const Icon(Icons.search),
                          ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Obx(
                () => DropdownButton<String>(
                  hint: const Text('Filter by status'),
                  value: status.value,
                  items: ['Active', 'Inactive', 'Pending']
                      .map((status) => DropdownMenuItem(
                            value: status,
                            child: Text(status),
                          ))
                      .toList(),
                  onChanged: (value) {
                    status.value = value ?? '';
                    filter = 'status=${status.toUpperCase()}';
                    controller.getChamaTrnsactions(
                        chamaId: chama.id ?? 0, page: 0, filters: filter);
                  },
                ),
              ),
            ],
          ),
          Wrap(
            spacing: 8,
            children: [
              ActionChip(
                avatar: const Icon(Icons.group_add_outlined, size: 18),
                label: const Text('Add Member'),
                onPressed: () async {
                  final res = await Get.to(() => const Invite());
                  if (res ?? false) {
                    controller.getChamaTrnsactions(
                        chamaId: chama.id!, page: 0, filters: filter);
                  }
                },
              ),
            ],
          )
        ],
      ),
    );
  }
}
