import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/controllers.dart';
import 'package:onekitty/models/auth/payments_channels.dart' as pm;
import 'package:onekitty/utils/my_text_field.dart';
import 'package:onekitty/utils/show_cached_network_image.dart';

class BankTab extends StatelessWidget {
  final TextEditingController accountNumber;
  const BankTab({super.key, required this.accountNumber});

  @override
  Widget build(BuildContext context) {
    
  final controller =
      Get.find<ChamaDataController>();
    return Obx(
      () => Column(
        children: [
          Align(
            alignment: Alignment.topLeft,
            child: Text(
              'select_bank_label'.tr,
              style: TextStyle(
                  fontSize: 14.spMin,
                  color: Colors.black,
                  fontWeight: FontWeight.w600),
            ),
          ),
          GestureDetector(
              onTap: () {
                showModalBottomSheet(
                    context: context,
                    isScrollControlled: true,
                    builder: (_) {
                      return DraggableScrollableSheet(
                          maxChildSize: 0.97,
                          initialChildSize: 0.7,
                          expand: false,
                          builder: (context, scrollController) {
                            final GlobalControllers globalController =
                                Get.find<GlobalControllers>();
                            final RxString searchText = ''.obs;

                            final banksList = globalController.paymentChannels
                                .where((e) => e.category == pm.Category.BANK)
                                .toList();

                            return Padding(
                              padding: const EdgeInsets.all(12.0),
                              child: Column(
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: CupertinoSearchTextField(
                                      onChanged: (value) {
                                        searchText.value = value;
                                      },
                                    ),
                                  ),
                                  Expanded(
                                    child: Obx(() {
                                      final filteredBanks =
                                          banksList.where((bank) {
                                        return bank.name.toLowerCase().contains(
                                            searchText.value.toLowerCase());
                                      }).toList();

                                      return ListView.builder(
                                          itemCount: filteredBanks.length,
                                          itemBuilder: (context, index) {
                                            return GestureDetector(
                                                onTap: () {
                                                  controller
                                                          .selectedBank.value =
                                                      filteredBanks[index];
                                                  accountNumber.text = "";
                                                  controller.channelName.value =
                                                      'BANK';
                                                  controller.channel =
                                                      filteredBanks[index]
                                                          .channelCode;
                                                  Navigator.of(context).pop();
                                                },
                                                child: ListTile(
                                                  leading:
                                                      ShowCachedNetworkImage(
                                                    imageUrl:
                                                        filteredBanks[index]
                                                            .imageUrl,
                                                    height: 30,
                                                    width: 30,
                                                    errorWidget: const Icon(
                                                        Icons.account_balance),
                                                  ),
                                                  title: Text(
                                                      filteredBanks[index]
                                                          .name),
                                                ));
                                          });
                                    }),
                                  ),
                                ],
                              ),
                            );
                          });
                    });
              },
              child: Container(
                  height: 60.h,
                  padding: const EdgeInsets.all(8),
                  alignment: Alignment.center,
                  width: 390.w,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Obx(
                    () => ListTile(
                      leading: ShowCachedNetworkImage(
                        imageUrl: controller.selectedBank.value?.imageUrl ?? '',
                        height: 30,
                        width: 30,
                        errorWidget: const Icon(Icons.account_balance),
                      ),
                      title: Text(controller.selectedBank.value?.name ?? ''),
                    ),
                  ))),
          SizedBox(height: 12.h),
          if (controller.selectedBank.value != null)
            MyTextFieldwValidator(
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'bank_account_required_validation'.tr;
                }
                return null;
              },
              controller: accountNumber,
              title: 'bank_account_label'.tr,
              onChanged: (val) {
              },
            )
        ],
      ),
    );
  }
}
