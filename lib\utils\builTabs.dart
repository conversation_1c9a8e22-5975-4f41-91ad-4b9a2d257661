import 'package:flutter/material.dart';
import 'package:onekitty/helpers/colors.dart';

Widget buildTabs(TabController tabController, context) {
  return Container(
    padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 5),
    decoration: BoxDecoration(
      color: AppColors.slate,
      borderRadius: BorderRadius.circular(15),
    ),
    child: TabB<PERSON>(
        controller: tabController,
        physics: const ClampingScrollPhysics(),
        padding: const EdgeInsets.only(left: 5, right: 5),
        unselectedLabelColor: Colors.black,
        labelColor: Theme.of(context).primaryColor,
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
        indicator: BoxDecoration(
            borderRadius: BorderRadius.circular(12), color: Colors.white),
        tabs: const [
          Tab(
            child: Text("Mobile Money"),
          ),
          Tab(
            child: Text("Paybill"),
          ),
          Tab(
            child: Text("Till number"),
          ),
          //TODO ADD BANK
          /*Tab(
            child: Text("Bank"),
          ),*/
        ]),
  );
}
