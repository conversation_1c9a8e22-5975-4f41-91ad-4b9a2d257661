import 'package:flutter/material.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/widgets/repository.dart';

final getContactsProvider = FutureProvider<List<Contact>>((ref) async {
  final selectContactRepository = ref.watch(selectContactsRepositoryProvider);
  return selectContactRepository.getContacts();
});

final selectContactControllerProvider =
    StateNotifierProvider<SelectContactController, bool>((ref) {
  final selectContactRepository = ref.watch(selectContactsRepositoryProvider);
  return SelectContactController(
      selectContactRepository: selectContactRepository, selectedContacts: []);
});

class SelectContactController extends StateNotifier<bool> {
  // ignore: unused_field
  final SelectContactRepository _selectContactRepository;
  final List<Contact> _selectedContacts;
  SelectContactController({
    required SelectContactRepository selectContactRepository,
    required List<Contact> selectedContacts,
  })  : _selectContactRepository = selectContactRepository,
        _selectedContacts = selectedContacts,
        super(false);

  void selectContact(Contact selectedContact, BuildContext context) {
    // String selectedPhoneNum = selectedContact.phones[0].number.replaceAll(
    //   ' ',
    //   '',
    // );

    _selectedContacts.add(selectedContact);

    state = _selectedContacts.isNotEmpty;
  }

  void removeSelectedContact(Contact contact) {
    _selectedContacts.remove(contact);
    state = _selectedContacts.isNotEmpty;
  }

  void removeAllSelectedContacts() {
    _selectedContacts.clear(); // Clears all elements from the list
  }

  // Method to return the list of selected contacts
  List<Contact> getSelectedContacts() {
    return _selectedContacts;
  }
}
