import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/admin_screens/utils/my_list_tile.dart';
import 'package:onekitty/controllers/admin/chama/chama_admin_controller.dart';
import 'package:onekitty/models/chama/chama_model.dart';
import 'package:onekitty/screens/dashboard/pages/chama/meetings/add_meeting.dart';
import 'package:onekitty/screens/dashboard/pages/chama/meetings/meetings.dart';
import 'package:onekitty/screens/dashboard/pages/chama/members/operations.dart';
import 'package:onekitty/screens/dashboard/pages/chama/tabs/update_chama.dart';
import 'package:onekitty/utils/themes_colors.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import '../../controllers/chama/chama_controller.dart';
import '../../screens/dashboard/pages/chama/resources/resources.dart';
import '../../utils/asset_urls.dart';
import '../../utils/custom_image_view.dart';
import '../../utils/formatted_currency.dart';
import 'chama_members.dart';
import 'chama_occurence.dart';
import 'chama_settings.dart';
import 'invoices.dart';
import 'penalties.dart';
import 'transactions.dart';
import 'widgets/attach_whatsapp.dart';

class ChamaDetailsPage extends StatefulWidget {
  final Chama chama;

  const ChamaDetailsPage({super.key, required this.chama});

  @override
  State<ChamaDetailsPage> createState() => _ChamaDetailsPageState();
}

class _ChamaDetailsPageState extends State<ChamaDetailsPage> {
  final PageController _pageController =
      PageController(initialPage: 1, viewportFraction: 0.8);
  final chamaDataController = Get.put(ChamaDataController());
  final chamaController = Get.put(ChamaController());
  final controller = Get.find<ChamaAdminController>();

  @override
  initState() {
    super.initState();
    controller.getAllChamaDetails(chamaId: widget.chama.id!).whenComplete(() {
      chamaDataController.singleChamaDts(widget.chama);
      chamaDataController.chama(UserChama(chama: widget.chama));
      // chamaDataController.members(widget.chama.);
      chamaController.frequencies = controller.frequencies;
      chamaDataController.singleChamaDts.value.frequency =
          widget.chama.frequency ?? "";
      chamaDataController.singleChamaDts.value.title = widget.chama.title ?? "";
      chamaDataController.singleChamaDts.value.description =
          widget.chama.description ?? "";
      chamaDataController.singleChamaDts.value.email = widget.chama.email ?? "";
      chamaDataController.singleChamaDts.value.amount =
          widget.chama.amount ?? 0;
      chamaDataController.singleChamaDts.value.nextOccurrence =
          widget.chama.nextOccurrence ?? DateTime.now();
    });
  }

  @override
  Widget build(BuildContext context) {
    void onRefresh() {
      controller.getAllChamaDetails(chamaId: widget.chama.id!);
    }

    debugPrint(json.encode(widget.chama.toJson()));

    return RefreshIndicator(
      onRefresh: () async => onRefresh(),
      child: Scaffold(
        appBar: AppBar(
          title: Text(widget.chama.title ?? 'Chama'),
          centerTitle: true,
        ),
        body: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Action Buttons
                    Align(
                      alignment: Alignment.center,
                      child: Wrap(
                        spacing: 4.0,
                        children: [
                          ActionChip(
                            avatar: const Icon(Icons.gavel, size: 18),
                            label: const Text('Penalties'),
                            onPressed: () {
                              Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => Penalties(
                                          
                                          chama: widget.chama)));
                            },
                          ),
                          ActionChip(
                            avatar: const Icon(Icons.receipt_long, size: 18),
                            label: const Text('Invoices'),
                            onPressed: () {
                              Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          InvoicesPage(chama: widget.chama)));
                            },
                          ),
                          ActionChip(
                            avatar: const Icon(Icons.calendar_today, size: 18),
                            label: const Text('Occurence'),
                            onPressed: () {
                              Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          ChamaOccurence(chama: widget.chama)));
                            },
                          ),
                          ActionChip(
                            avatar: const Icon(Icons.edit, size: 18),
                            label: const Text('Edit Chama'),
                            onPressed: () {
                              Get.to(() => const UpdateChama());
                            },
                          ),
                          ActionChip(
                            avatar: const Icon(Icons.settings, size: 18),
                            label: const Text('Operations'),
                            onPressed: () {
                              Get.to(() => const Operations());
                            },
                          ),
                          ActionChip(
                            avatar: const Icon(Icons.library_books, size: 18),
                            label: const Text('Chama Resources'),
                            onPressed: () {
                              Get.to(() => const ResourcesWidget());
                            },
                          ),
                          ActionChip(
                            avatar: const Icon(Icons.manage_accounts_outlined,
                                size: 18),
                            label: const Text('Members'),
                            onPressed: () {
                              Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          ChamaBeneficiariesPage(
                                              chama: widget.chama)));
                            },
                          ),
                          ActionChip(
                            avatar: const Icon(Icons.block, size: 18),
                            label: const Text('Block Chama'),
                            onPressed: () {},
                          ),
                          ActionChip(
                            avatar: const Icon(Icons.settings, size: 18),
                            label: const Text('Chama Settings'),
                            onPressed: () {
                              Get.to(() =>
                                  ChamaSettingsScreen(id: widget.chama.id!));
                            },
                          ),
                          ActionChip(
                            avatar: const Icon(Icons.settings, size: 18),
                            label: const Text('Meetings'),
                            onPressed: () {
                              Get.to(() => const MeetingsPage());
                            },
                          ),
                          ActionChip(
                            avatar: const Icon(Icons.add, size: 18),
                            label: const Text('Add Meeting'),
                            onPressed: () {
                              Get.to(() =>
                                  const AddMeetingPage(isUpdating: false));
                            },
                          ),
                          ActionChip(
                            avatar: CustomImageView(
                              imagePath: AssetUrl.whatsapp,
                              height: 18,
                              width: 18,
                            ),
                            label: const Text('Add WhatsApp'),
                            onPressed: () {
                              showDialog(
                                  context: context,
                                  builder: (context) {
                                    return const Dialog(
                                      child: AttachWhatsapp(),
                                    );
                                  });
                            },
                          ),
                          ActionChip(
                            avatar: const Icon(Icons.link, size: 18),
                            label: const Text('Send funds to beneficiary'),
                            onPressed: () {
                              controller.sendFundsToBeneficiary(
                                  id: widget.chama.id!);
                            },
                          ),
                          ActionChip(
                            avatar: const Icon(Icons.list, size: 18),
                            label: const Text('Chama Transactions'),
                            onPressed: () {
                              Get.to(() => TransactionTable(
                                    chamaId: widget.chama.id!,
                                  ));
                            },
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 20),
                    Column(
                      children: [
                        Text(
                          "Chama Balance: ${FormattedCurrency().getFormattedCurrency(widget.chama.totaBal)}",
                        ),
                      ],
                    ),
                    Text(
                      "Paid Penalties: ${FormattedCurrency().getFormattedCurrency(
                        controller.penaltyBal,
                      )}",
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontStyle: FontStyle.italic,
                      ),
                    ),

                    _buildFrame(context),

                    // Details Section
                    _buildDetailRow('Chama ID', widget.chama.id?.toString()),
                    _buildDetailRow('Phone Number', widget.chama.phoneNumber),
                    _buildDetailRow(
                        'Balance', widget.chama.balance?.toStringAsFixed(2)),
                    _buildDetailRow(
                        'Next Occurrence',
                        widget.chama.nextOccurrence != null
                            ? DateFormat('MMMM d, yyyy')
                                .format(widget.chama.nextOccurrence!)
                            : 'N/A'),
                    _buildDetailRow(
                        'Created At',
                        widget.chama.createdAt != null
                            ? DateFormat('MMMM d, yyyy')
                                .format(widget.chama.createdAt!)
                            : 'N/A'),
                    _buildDetailRow('Description', widget.chama.description),
                    _buildDetailRow(
                        'Account Number', widget.chama.accountNumber),
                    _buildDetailRow('Status', widget.chama.status),
                  ],
                ),
              ),
            ),
            Obx(() => controller.isGetAllChamaDetailsLoading.value
                ? Positioned.fill(
                    child: ColoredBox(
                        color: Colors.black.withOpacity(0.9),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SpinKitDualRing(
                              color: ColorUtil.blueColor,
                              lineWidth: 4,
                              size: 40.0,
                            ),
                            const SizedBox(height: 8),
                            const Text(
                              "loading..",
                              style: TextStyle(
                                color: Colors.white,
                              ),
                            )
                          ],
                        )))
                : const SizedBox())
          ],
        ),
      ),
    );
  }

  // Helper Method to Build Detail Rows
  Widget _buildDetailRow(String label, String? value) {
    return MyListTile(title: (label), subtitle: (value ?? 'N/A'));
  }

  Widget _buildFrame(BuildContext context) {
    List<String> circleImages = [
      AssetUrl.group6,
      AssetUrl.winter,
      AssetUrl.imgEllipse1,
    ];
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        MyListTile(
          title: 'Amount',
          subtitle:
              "${FormattedCurrency().getFormattedCurrency(widget.chama.amount ?? 0)}/${widget.chama.frequency}",
        ),
        MyListTile(
          title: 'No. of penalties accured:',
          subtitle: controller.penaltyCount.toString(),
        ),
        const SizedBox(height: 5),
        MyListTile(
          title: 'DeadLine',
          subtitle: DateFormat('MMM dd, yyyy')
              .format(widget.chama.nextOccurrence?.toLocal() ?? DateTime.now()),
        ),
      ],
    );
  }
}

// class BeneficiariesTab extends StatelessWidget {
//   const BeneficiariesTab({super.key});

//   @override
//   Widget build(BuildContext context) {
//     final controller = Get.find<DataController>();
//     return ListView.builder( 
//       itemCount: controller.beneficiaries.length, 
 
//      itemBuilder: (context, index){ 
        
//         return ListTile(
//           title: Text('${}')
//          );
//       }
//     );
//   }
// }