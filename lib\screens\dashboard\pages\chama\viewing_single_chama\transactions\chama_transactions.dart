// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:grouped_list/grouped_list.dart';
import 'package:intl/intl.dart';
import 'package:number_paginator/number_paginator.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/models/chama/chama_memebers_model.dart';
import 'package:onekitty/models/chama/chama_transactions.dart';
import 'package:onekitty/screens/dashboard/pages/chama/viewing_single_chama/transactions/export_widget.dart';
import 'package:onekitty/screens/dashboard/pages/chama/viewing_single_chama/transactions/view_transaction_details.dart';
import 'package:onekitty/utils/size_config.dart';

import '../../../../../../utils/utils_exports.dart';

class ChamaTransactionsPage extends StatefulWidget {
  final bool isFullPage;
  final bool isFromMemberTransactions;
  final String? accountNo;
  final ChamaMembers? member;

  const ChamaTransactionsPage(
      {super.key,
      required this.isFullPage,
      this.accountNo,
      this.member,
      required this.isFromMemberTransactions});

  @override
  State<ChamaTransactionsPage> createState() => _ChamaTransactionsPageState();
}

class _ChamaTransactionsPageState extends State<ChamaTransactionsPage> {
  TextEditingController searchController = TextEditingController();
  final ChamaDataController chamaDataController =
      Get.put(ChamaDataController());
  final dateformat = DateFormat('EE, dd MMMM');
  List<Transaction> filterByName = [];
  final ChamaController chamaController = Get.put(ChamaController());
  bool singleTrans = false;
  List<String> dropdownItemList = ["code", "Date", "Account No", "category"];
  List<String> categoryFilter = ["ALL", "PENALTY", "CONTRIBUTION"];
  String selectedCategoryFilter = "ALL";
  String selectedFilter = "";
  TextEditingController startDate = TextEditingController();
  TextEditingController endDate = TextEditingController();
  TextEditingController accountController = TextEditingController();
  TextEditingController codeController = TextEditingController();
  TextEditingController categoryController = TextEditingController();

  @override
  void initState() {
    super.initState();
    filterByName = chamaController.chamaTransactions;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: widget.isFullPage
          ? AppBar(
              backgroundColor: Colors.transparent,
              leading: IconButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  icon: Icon(
                    Icons.arrow_back,
                    color: Colors.black,
                  )),
              title: SizedBox(
                height: 40.h,
                child: Row(
                  children: [
                    Expanded(
                        child: TextField(
                      controller: searchController,
                      onChanged: (value) {
                        filterByName = chamaController.chamaTransactions
                            .where((p0) => (p0.firstName?.toLowerCase() ?? '')
                                .contains(value.toLowerCase()))
                            .toList();
                        setState(() {});
                      },
                      decoration: InputDecoration(
                        labelText: "Search",
                        focusedBorder:
                            OutlineInputBorder(borderSide: BorderSide.none),
                        enabledBorder:
                            OutlineInputBorder(borderSide: BorderSide.none),
                        border: OutlineInputBorder(borderSide: BorderSide.none),
                        suffixIcon: IconButton(
                            onPressed: () {}, icon: Icon(Icons.search)),
                        //filled: true,
                        // fillColor: Colors.white,
                      ),
                    )),
                    SizedBox(
                      width: 10,
                    ),
                    CustomDropDown(
                        // fillColor: Colors.blue,
                        width: 120.w,
                        hintText: "Filter",
                        hintStyle: TextStyle(
                            fontSize: 12.h, color: appTheme.indigo500),
                        items: dropdownItemList,
                        prefix: Container(
                          margin: EdgeInsets.fromLTRB(4.w, 11.h, 0.w, 11.h),
                          child: CustomImageView(
                              imagePath: AssetUrl.imgIconIndigo500,
                              height: 40.h,
                              width: 18.w),
                        ),
                        prefixConstraints:
                            BoxConstraints(maxHeight: 40.h, maxWidth: 20.h),
                        onChanged: (value) {
                          setState(() {
                            selectedFilter = value;
                          });
                        }),
                  ],
                ),
              ),
            )
          : AppBar(
              backgroundColor: AppColors.background,
              //title: ExportButton(singleTrans: singleTrans),
              actions: [
                TextButton(
                    onPressed: () {
                      widget.isFromMemberTransactions
                          ? null
                          : Get.to(() => ChamaTransactionsPage(
                                isFullPage: true,
                                isFromMemberTransactions: false,
                              ));
                    },
                    child: Text(
                      widget.isFromMemberTransactions
                          ? "${widget.member?.firstName} ${widget.member?.secondName}'s Transactions"
                          : "See all",
                      style: context.dividerTextLarge?.copyWith(
                          color: AppColors.blueButtonColor, fontSize: 17),
                    ))
              ],
            ),
      body: Container(
        margin: EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.isFullPage)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 12.0),
                child: Text("Transactions"),
              ),
            if (widget.isFullPage && selectedFilter == "Account No")
              _buildAccountFilterUI(),
            if (widget.isFullPage && selectedFilter == "code")
              _buildCodeChamaFilterUI(),
            if (widget.isFullPage && selectedFilter == "Date")
              _buildDateChamaFilterUI(),
            if (widget.isFullPage && selectedFilter == "category")
              _buildCategoryChamaFilterUI(),
            if (widget.isFullPage && selectedFilter == "") SizedBox(),
            _buildAllTransacions(),

            //startDate.text.isNotEmpty && endDate.text.isNotEmpty || accountController.text.isNotEmpty || codeController.text.isNotEmpty || categoryController.text.isNotEmpty ?
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.inversePrimary,
              ),
              child: Obx(() {
                if (chamaController.chamaTransactions.isNotEmpty &&
                    chamaController.results.value!.totalPages! > 0) {
                  return Visibility(
                    visible: true,
                    child: NumberPaginator(
                      numberPages:
                          chamaController.results.value?.totalPages ?? 0,
                      onPageChange: (int index) async {
                        await chamaController.getChamaTrnsactions(
                          chamaId:
                              chamaDataController.chama.value.chama?.id ?? 0,
                          page: index,
                        );
                        setState(() {});
                      },
                    ),
                  );
                } else {
                  return Container(); // Empty container when transactions is empty
                }
              }),
            )
          ],
        ),
      ),
      floatingActionButton: ExportButton(singleTrans: singleTrans),
      floatingActionButtonLocation: FloatingActionButtonLocation.endContained,
    );
  }

  //ALL TRANSACTIONS
  Widget _buildAllTransacions() {
    return GetX(
        init: ChamaController(),
        initState: (state) {
          Future.delayed(Duration.zero, () async {
            try {
              await state.controller?.getChamaTrnsactions(
                chamaId: chamaDataController.chama.value.chama?.id ?? 0,
                accountNo:
                    widget.isFromMemberTransactions ? widget.accountNo : null,
              );
              // ignore: empty_catches
            } catch (e) {}
          });
        },
        builder: (ChamaController chamaController) {
          if (chamaController.isGetChamaTransactionLoading.isTrue) {
            return SizedBox(
              height: SizeConfig.screenHeight * .1,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SpinKitDualRing(
                      color: ColorUtil.blueColor,
                      lineWidth: 4.sp,
                      size: 40.0.sp,
                    ),
                    const Text(
                      "loading..",
                      style: TextStyle(
                        color: Colors.white,
                      ),
                    )
                  ],
                ),
              ),
            );
          } else if (chamaController.chamaTransactions.isEmpty) {
            return Center(child: Text("No transactions yet"));
          } else if (filterByName.isEmpty) {
            return Center(child: Text("Transaction not found"));
          } else if (filterByName.isNotEmpty) {
            return Expanded(
              child: GroupedListView<Transaction, DateTime>(
                sort: false,
                useStickyGroupSeparators: true,
                elements: filterByName,
                groupBy: (Transaction element) {
                  DateTime date = element.createdAt!.toLocal();
                  return DateTime(date.year, date.month, date.day);
                },
                //groupSeparatorBuilder: (value) => Container(decoration: BoxDecoration(color: Colors.red,border: Border.all(color: AppColors.blueButtonColor)),),
                groupHeaderBuilder: (value) {
                  final date = dateformat.format(value.createdAt!.toLocal());
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Text(
                      date,
                      style: const TextStyle(
                        color: Colors.black,
                        fontWeight: FontWeight.bold,
                        fontSize: 17.0,
                      ),
                    ),
                  );
                },
                itemBuilder: (context, Transaction transaction) {
                  return SingleChamaTransaction(item: transaction);
                },

                separator: const Padding(
                  padding: EdgeInsets.symmetric(vertical: 8.0),
                ),
              ),
            );
          }
          return Text("No transaction yet");
        });
  }

  Widget _buildAccountFilterUI() {
    return Padding(
      padding: EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          CustomSearchView(
            width: 170.w,
            controller: accountController,
            hintText: "Filter phone number",
            contentPadding: EdgeInsets.all(5.h),
            onChanged: (p0) {
              _updateFilter();
            },
          ),
          InkWell(
            onTap: () {
              setState(() {
                selectedFilter = "Filter";
                accountController.clear();
              });
            },
            child: CustomImageView(
              imagePath: AssetUrl.imgIconoirCancel,
              height: 24.h,
              width: 24.w,
            ),
          ),
        ],
      ),
    );
  }

  //TRANSACTION CODE FILTER
  Widget _buildCodeChamaFilterUI() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomSearchView(
            width: 170.w,
            controller: codeController,
            hintText: "Filter with code",
            contentPadding: EdgeInsets.all(5.h),
            onChanged: (p0) {
              _updateFilter();
            },
          ),
          InkWell(
            onTap: () {
              setState(() {
                selectedFilter = "Filter";
                codeController.clear();
              });
            },
            child: CustomImageView(
              imagePath: AssetUrl.imgIconoirCancel,
              height: 24.h,
              width: 24.w,
            ),
          ),
        ],
      ),
    );
  }

  //DATE CHAMA FILTER
  Widget _buildDateChamaFilterUI() {
    return Padding(
      padding: const EdgeInsets.only(left: 12.0, right: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 120.w,
            height: 35.h,
            child: TextFormField(
              controller: startDate,
              style: const TextStyle(fontSize: 15),
              readOnly: false,
              onTap: () async {
                final DateTime? pickedDate = await showDatePicker(
                  context: context,
                  initialDate:
                      DateTime.now(), // Set initial date to current date
                  firstDate: DateTime(2000),
                  lastDate:
                      DateTime(2100), // Set the latest date that can be picked
                );

                if (pickedDate != null) {
                  final formattedDate =
                      DateFormat('yyyy-MM-dd').format(pickedDate);
                  startDate.text = formattedDate;
                  // _updateDateFilter();
                }
              },
              decoration: InputDecoration(
                labelText: 'Start Date',
                border: const OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.black87),
                ),
                focusedBorder: const OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.black87)),
                suffixIcon: Icon(
                  Icons.calendar_today,
                  size: 18.h,
                ),
              ),
            ),
          ),
          SizedBox(
            width: 8.w,
          ),
          SizedBox(
            width: 120.w,
            height: 35.h,
            child: TextFormField(
              onEditingComplete: () {
                // _loadFiltrTransactions(startDate.text, endDate.text);
              },
              controller: endDate,
              style: const TextStyle(fontSize: 15),
              readOnly: false,
              onTap: () async {
                final DateTime? pickedDate = await showDatePicker(
                  context: context,
                  initialDate: DateTime.now(),
                  firstDate: DateTime(2000),
                  lastDate: DateTime(2100),
                );

                if (pickedDate != null) {
                  final formattedDate =
                      DateFormat('yyyy-MM-dd').format(pickedDate);
                  endDate.text = formattedDate;
                  _updateFilter();
                }
              },
              decoration: InputDecoration(
                labelText: 'End Date',
                border: const OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.black87),
                ),
                focusedBorder: const OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.black87)),
                suffixIcon: Icon(
                  Icons.calendar_today,
                  size: 18.h,
                ),
              ),
            ),
          ),
          SizedBox(
            width: 8.w,
          ),
          InkWell(
            onTap: () {
              setState(() {
                selectedFilter = "Filter";

                startDate.clear();
                endDate.clear();
              });
            },
            child: CustomImageView(
              imagePath: AssetUrl.imgIconoirCancel,
              height: 24.h,
              width: 24.w,
            ),
          ),
        ],
      ),
    );
  }

  //CATEGORY CHAMA FILTER
  Widget _buildCategoryChamaFilterUI() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: SizedBox(
        height: 35,
        width: SizeConfig.screenWidth,
        child: Row(
          children: [
            Expanded(
                child: ListView(
              scrollDirection: Axis.horizontal,
              children: [
                ...categoryFilter.map((category) {
                  return GestureDetector(
                    onTap: () => onSelectCategoryFilter(category),
                    child: Container(
                      margin: EdgeInsets.only(right: 5),
                      padding:
                          EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                          color: selectedCategoryFilter == category
                              ? AppColors.primary
                              : AppColors.primary.withOpacity(0.5),
                          borderRadius: BorderRadius.circular(20)),
                      child: Text(
                        category,
                        style: context.dividerTextLarge?.copyWith(
                            color: selectedCategoryFilter == category
                                ? Colors.white
                                : Colors.black.withOpacity(0.5)),
                      ),
                    ),
                  );
                }).toList()
              ],
            ))
          ],
        ),
      ),
    );
  }

  void onSelectCategoryFilter(String category) {
    setState(() {
      selectedCategoryFilter = category;
    });
    _updateFilter();
  }

  void _updateFilter() {
    setState(() {
      _fetchFilteredChamaTransactions();
    });
  }

  void _fetchFilteredChamaTransactions() async {
    try {
      await Get.find<ChamaController>().getChamaTrnsactions(
          chamaId: chamaDataController.chama.value.chama?.id ?? 0,
          startDate: startDate.text.trim(),
          endDate: endDate.text.trim(),
          accountNo: accountController.text.trim(),
          code: codeController.text.trim(),
          category: selectedCategoryFilter);
      setState(() {
        Get.find<ChamaController>().isGetChamaTransactionLoading(false);
      });
    } catch (e) {
      rethrow;
    }
  }
}

class ExportButton extends StatelessWidget {
  final bool singleTrans;
  const ExportButton({super.key, required this.singleTrans});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 60.0),
      child: CustomElevatedButton(
        buttonStyle: ButtonStyle(
          backgroundColor: WidgetStateProperty.all<Color>(
              const Color.fromARGB(255, 184, 129, 57)),
        ),
        width: 90.w,
        height: 30.h,
        text: "Export",
        buttonTextStyle: TextStyle(fontSize: 12.h, fontWeight: FontWeight.bold),
        leftIcon: Container(
            margin: EdgeInsets.only(right: 1.w),
            child: CustomImageView(
                imagePath: AssetUrl.expIcon, height: 12.h, width: 12.w)),
        onPressed: () {
          showModalBottomSheet(
            context: context,
            builder: (BuildContext context) {
              return ExportChamaContentWidget(singleTrans: singleTrans);
            },
          );
        },
        alignment: Alignment.bottomRight,
      ),
    );
  }
}
