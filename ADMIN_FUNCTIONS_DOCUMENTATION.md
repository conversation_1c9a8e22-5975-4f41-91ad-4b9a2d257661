# OneKitty Admin Functions Documentation

## Overview
This document provides a comprehensive guide to all admin functions, screens, controllers, and dialogs in the OneKitty Flutter project. Use this as a reference to replicate the admin system in another project.

## Architecture Overview

### Main Entry Point
- **File**: `lib/admin_screens/admin_screen.dart`
- **Purpose**: Main admin screen with responsive layout
- **Features**: 
  - Responsive drawer navigation (mobile/tablet/desktop)
  - Dynamic page switching based on screen size
  - Integrated with `DashboardController`

### Navigation System
- **Controller**: `lib/controllers/admin/dashboard_controller.dart`
- **Drawer**: `lib/admin_screens/drawer.dart`
- **Navigation Method**: Page-based switching using `pageNo.obs`

## Admin Modules

### 1. Chama Dashboard (Page 0)
**Screen**: `lib/admin_screens/chama/chama_admin.dart`
**Controller**: `lib/controllers/admin/chama/chama_admin_controller.dart`

#### Features:
- Data grid with Syncfusion DataGrid
- Filtering and pagination
- View individual chama details
- Export functionality

#### Sub-screens:
- **View Chama**: `lib/admin_screens/chama/view_chama.dart`
  - Detailed chama information
  - Tab-based navigation for different aspects
  - Navigation to members, settings, transactions, etc.

- **Chama Members**: `lib/admin_screens/chama/chama_members.dart`
  - Member management
  - Penalties bottom sheet
  - Add member functionality
  - Status filtering

- **Chama Settings**: `lib/admin_screens/chama/chama_settings.dart`
  - View and edit chama settings
  - Beneficiary configuration
  - Signature threshold management

- **Edit Chama**: `lib/admin_screens/chama/edit_chama.dart`
  - Chama modification interface

- **Transactions**: `lib/admin_screens/chama/transactions.dart`
  - Transaction history and management

- **Invoices**: `lib/admin_screens/chama/invoices.dart`
  - Invoice management system

- **Penalties**: `lib/admin_screens/chama/penalties.dart`
  - Penalty tracking and management

- **Notifications**: `lib/admin_screens/chama/notifications.dart`
  - Notification management

- **Chama Occurrence**: `lib/admin_screens/chama/chama_occurence.dart`
  - Occurrence tracking with dialog details

#### Widgets:
- **Filter Widget**: `lib/admin_screens/chama/widgets/filter_widget.dart`
- **Table Footer**: `lib/admin_screens/chama/widgets/table_footer.dart`
- **Beneficiaries Data Table**: `lib/admin_screens/chama/widgets/beneficiaries_data_table.dart`
- **Attach WhatsApp**: `lib/admin_screens/chama/widgets/attach_whatsapp.dart`

### 2. Events Dashboard (Page 1)
**Screen**: `lib/admin_screens/events/events_admin.dart`
**Controller**: `lib/controllers/admin/events/events_admin_controller.dart`

#### Features:
- Event listing with toggle view (table/list)
- Event filtering
- Excel export functionality
- Event blocking/unblocking

#### Sub-screens:
- **Events Table**: `lib/admin_screens/events/events_table.dart`
- **All Transactions**: `lib/admin_screens/events/alltransactions_admin.dart`
- **Block Event**: `lib/admin_screens/events/block_event_page.dart`
  - Reason input with image upload
  - Context menu for unblocking

#### Widgets:
- **Events Filter**: `lib/admin_screens/events/widgets/events_filter_widget.dart`

### 3. Settings (Page 2)
**Screen**: `lib/admin_screens/settings/settings.dart`

#### Features:
- Simple list-based navigation
- Access to all events view

#### Sub-screens:
- **Get All Events**: `lib/admin_screens/settings/pages/getAllEvents.dart`
  - Excel generation functionality
  - Complete events overview

### 4. Kitty Dashboard (Page 3)
**Screen**: `lib/admin_screens/kitty/kitty_admin.dart`
**Controller**: `lib/controllers/admin/kitty/kitty_controller.dart`

#### Features:
- Kitty management interface
- Data grid implementation
- Filtering capabilities

## Common Patterns

### Controller Structure
All admin controllers follow this pattern:
```dart
class AdminController extends GetxController implements GetxService {
  // Observable variables
  final isLoading = false.obs;
  final currentPage = 0.obs;
  final size = 15.obs;
  final search = ''.obs;
  final startDate = ''.obs;
  final endDate = ''.obs;
  
  // Services
  final HttpService apiProvider = Get.find();
  final logger = Logger(filter: CustomLogFilter());
  
  // Data collections
  final items = <Model>[].obs;
  
  // Pagination variables
  final maxPage = 0.obs;
  final totalPages = 0.obs;
  final isLast = false.obs;
  final isFirst = true.obs;
}
```

### Dialog Implementations

#### 1. Custom Form Dialog
**File**: `lib/admin_screens/utils/my_form_dialog.dart`
```dart
void showMyFormDialog({
  required BuildContext context,
  required String title,
  String? description,
  required List<Widget> contents,
  String? okText,
  String? cancelText,
  Function()? onOkPressed,
  Function()? onCancelPressed
})
```

#### 2. Bottom Sheet Pattern
```dart
void showBottomSheet() {
  showModalBottomSheet(
    isScrollControlled: true,
    context: context,
    builder: (context) {
      return DraggableScrollableSheet(
        maxChildSize: 0.97,
        initialChildSize: 0.87,
        expand: false,
        builder: (context, scrollController) {
          return YourWidget();
        }
      );
    }
  );
}
```

#### 3. Get.dialog Pattern
```dart
Get.dialog(YourDialogWidget());
```

#### 4. Context Menu Pattern
```dart
showMenu(
  context: context,
  position: const RelativeRect.fromLTRB(100, 0, 0, 0),
  items: [
    PopupMenuItem(
      child: const Text('Action'),
      onTap: () => performAction(),
    ),
  ],
);
```

### Common Utilities

#### 1. List Tile Component
**File**: `lib/admin_screens/utils/my_list_tile.dart`
- Reusable list tile for admin interfaces

#### 2. Color Codes
**File**: `lib/admin_screens/common/color_codes.dart`
- Centralized color definitions

#### 3. Column Widths
**File**: `lib/admin_screens/common/colum_widths.dart`
- Standardized column width configurations

## Navigation Routes
Admin screens integrate with the main navigation system through:
- `lib/screens/bottom_navbar_screens/nav_routes/nav_routes.dart`
- Route definitions for all admin sub-screens

## Authentication
- Logout functionality integrated with `AuthenticationManager`
- Password verification screen integration
- Session management through GetX state management

## Data Management
- HTTP service integration for API calls
- Pagination support across all modules
- Filtering and search capabilities
- Excel export functionality for data analysis

## Responsive Design
- Mobile-first approach with responsive breakpoints
- Adaptive drawer behavior (overlay/permanent)
- Screen size-based component rendering
- Tooltip support for compact views

This documentation provides the foundation for replicating the admin system. Each module follows consistent patterns for controllers, views, and data management.
