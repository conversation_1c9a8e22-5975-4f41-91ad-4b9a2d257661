// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';

import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/utils/utils_exports.dart';

class EmptyBulkSmsScreen extends StatelessWidget {
  EmptyBulkSmsScreen({super.key});

  final UserKittyController _userController = Get.put(UserKittyController());

  GlobalKey<NavigatorState> navigatorKey = GlobalKey();

  String greeting = getGreeting();

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: Container(
          width: double.maxFinite,
          padding: EdgeInsets.symmetric(
            horizontal: 32.w,
            vertical: 23.h,
          ),
          child: Column(
            children: [
              Align(
                alignment: Alignment.centerLeft,
                child: Row(
                  children: [
                    CustomImageView(
                      imagePath: AssetUrl.imgEllipse1,
                      height: 44.h,
                      width: 44.w,
                      radius: BorderRadius.circular(
                        22.h,
                      ),
                    ),
                    Container(
                      width: 106.w,
                      margin: EdgeInsets.only(left: 6.w),
                      child: Text(
                        "$greeting,\n${_userController.getLocalUser()?.firstName ?? ""}",
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: CustomTextStyles.titleSmallGray90001,
                      ),
                    ),
                  ],
                ),
              ),
              const Spacer(
                flex: 50,
              ),
              CustomImageView(
                imagePath: AssetUrl.imgGroup7,
                height: 174.h,
                width: 325.w,
              ),
              SizedBox(height: 23.h),
              Text(
                "Send Bulk SMS",
                style: theme.textTheme.titleLarge,
              ),
              SizedBox(height: 13.h),
              Text(
                "Conveniently send messages to people at once.",
                style: CustomTextStyles.bodyMediumBluegray700,
              ),
              SizedBox(height: 22.h),
              CustomElevatedButton(
                onPressed: () {
                  Get.toNamed(NavRoutes.mainbulksms);
                  // Navigator.pushNamed(context, NavRoutes.crtsmsScreen);
                },
                text: "Send Messages",
                buttonStyle: CustomButtonStyles.fillPrimary,
                buttonTextStyle: theme.textTheme.titleMedium!,
              ),
              const SizedBox(
                height: 20,
              ),
              const Spacer(
                flex: 49,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
