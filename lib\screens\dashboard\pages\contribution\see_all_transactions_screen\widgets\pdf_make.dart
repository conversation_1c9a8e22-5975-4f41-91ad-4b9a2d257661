import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/models/transac_kitt_model.dart';
import 'package:onekitty/models/user_transaction_model.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart';
import 'package:flutter/services.dart' show rootBundle;

import '../../../../../../controllers/user_ktty_controller.dart';
import '../../../../../../utils/utils_exports.dart';

Future<Uint8List> makeFullPdf(List<TransactionModel> tra) async {
  UserKittyController userController = Get.find();
  userController.getLocalUser();
  final dateformat = DateFormat('EE, dd MMMM h:mm a');

  final pdf = Document(
    title: "onekitty transaction statement",
    author: "onekitty.co.ke",
    producer: "onekitty.co.ke",
    subject: "onekitty_transaction statement",
    theme: ThemeData(
      defaultTextStyle: TextStyle(
        font: Font.courier(),
      ),
    ),
  );
  final imageLogo =
      MemoryImage((await rootBundle.load(AssetUrl.logo2)).buffer.asUint8List());

  pdf.addPage(
    MultiPage(
      footer: (Context context) {
        return Padding(
          padding: const EdgeInsets.all(10),
          child: Text(
            'Tel: +254 733550051 \n Email: <EMAIL> \n www.onekitty.co.ke',
            style: Theme.of(context).header3.copyWith(
                  fontStyle: FontStyle.italic,
                ),
            textAlign: TextAlign.center,
          ),
        );
      },
      header: (Context context) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              children: [
                Text(
                    "${userController.getLocalUser()?.firstName ?? ''} ${userController.getLocalUser()?.secondName ?? ''}"
),
                Text("${userController.getLocalUser()?.phoneNumber}"),
              ],
              crossAxisAlignment: CrossAxisAlignment.start,
            ),
            SizedBox(
              height: 150,
              width: 150,
              child: Image(imageLogo),
            )
          ],
        );
      },
      build: (context) {
        return [
          // Wrap(
          //   children: [

          Table(
            border: TableBorder.all(color: PdfColors.black),
            children: [
              TableRow(
                children: [
                  Padding(
                    child: Text(
                      "Date",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                  Padding(
                    child: Text(
                      "Reference",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                  Padding(
                    child: Text(
                      "Details",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                  Padding(
                    child: Text(
                      "Amount",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                ],
              ),
              ...tra.map(
                (e) => TableRow(
                  decoration: BoxDecoration(
                    color: e.id! / 2 == 0 ? PdfColor.fromHex("#DADEF4") : null,
                  ),
                  children: [
                    Expanded(
                      child: PaddedText(
                        dateformat.format(e.createdAt!.toLocal()),
                      ),
                    ),
                    Expanded(
                      child: PaddedText(
                        "${e.transactionCode}",
                      ),
                      // flex: 2,
                    ),
                    Expanded(
                      child: PaddedText(
                          "Kitty Id: ${e.kittyId}\n${e.channelCode}\n${e.firstName} ${e.secondName}"),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(10),
                        child: Text(
                          e.transactionType == "Contribution"
                              ? "KSH ${e.amount}"
                              : "KSH ${e.amount}",
                          textAlign: TextAlign.right,
                          style: TextStyle(
                            color: e.transactionType == "Contribution"
                                ? PdfColor.fromHex("#0D0E24")
                                : PdfColor.fromHex("#008000"),
                          ),
                        ),
                      ),
                      flex: 1,
                    ),
                  ],
                ),
              ),
            ],
          ),
          Expanded(
              child: Padding(
            child: Text(
              "Thank you for using onekitty",
              style: Theme.of(context).header2,
              textAlign: TextAlign.center,
            ),
            padding: const EdgeInsets.all(20),
          )),
          Text(
            "Onekitty is a contribution patform that enables you to receive realtime contribution updates on Whatsapp,Telegram,email and sms.",
            textAlign: TextAlign.center,
          ),
          Divider(
            height: 1,
            borderStyle: BorderStyle.dashed,
          ),
        ];
      },
    ),
  );
  return pdf.save();
}

Widget PaddedText(
  final String text, {
  final TextAlign align = TextAlign.left,
}) =>
    Padding(
      padding: const EdgeInsets.all(10),
      child: Text(
        text,
        textAlign: align,
      ),
    );

//USER TRANSACTIONS

Future<Uint8List> makeFullUserPdf(List<Item> tra) async {
  UserKittyController userController = Get.find();
  userController.getLocalUser();
  final dateformat = DateFormat('EE, dd MMMM h:mm a');

  final pdf = Document(
    title: "onekitty transaction statement",
    author: "onekitty.co.ke",
    producer: "onekitty.co.ke",
    subject: "onekitty_transaction statement",
    theme: ThemeData(
      defaultTextStyle: TextStyle(
        font: Font.courier(),
      ),
    ),
  );
  final imageLogo =
      MemoryImage((await rootBundle.load(AssetUrl.logo2)).buffer.asUint8List());

  pdf.addPage(
    MultiPage(
      footer: (Context context) {
        return Padding(
          padding: const EdgeInsets.all(10),
          child: Text(
            'Tel: +254 733550051 \n Email: <EMAIL> \n www.onekitty.co.ke',
            style: Theme.of(context).header3.copyWith(
                  fontStyle: FontStyle.italic,
                ),
            textAlign: TextAlign.center,
          ),
        );
      },
      header: (Context context) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              children: [
                Text(
                    "${userController.getLocalUser()?.firstName ?? ''} ${userController.getLocalUser()?.secondName ?? ''}"
),
                Text("${userController.getLocalUser()?.phoneNumber}"),
              ],
              crossAxisAlignment: CrossAxisAlignment.start,
            ),
            SizedBox(
              height: 150,
              width: 150,
              child: Image(imageLogo),
            )
          ],
        );
      },
      build: (context) {
        return [
          // Wrap(
          //   children: [

          Table(
            border: TableBorder.all(color: PdfColors.black),
            children: [
              TableRow(
                children: [
                  Padding(
                    child: Text(
                      "Date",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                  Padding(
                    child: Text(
                      "Reference",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                  Padding(
                    child: Text(
                      "Details",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                  Padding(
                    child: Text(
                      "Amount",
                      style: Theme.of(context).header4,
                      textAlign: TextAlign.center,
                    ),
                    padding: const EdgeInsets.all(20),
                  ),
                ],
              ),
              ...tra.map(
                (e) => TableRow(
                  decoration: BoxDecoration(
                    color: e.id! / 2 == 0 ? PdfColor.fromHex("#DADEF4") : null,
                  ),
                  children: [
                    Expanded(
                      child: PaddedText(
                        dateformat.format(e.createdAt!.toLocal()),
                      ),
                    ),
                    Expanded(
                      child: PaddedText(
                        "${e.transactionCode}",
                      ),
                      // flex: 2,
                    ),
                    Expanded(
                      child: PaddedText(
                          "Kitty Id: ${e.kittyId}\n${e.channelCode}\n${e.fullName} ${e.phoneNumber}"),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.all(10),
                        child: Text(
                          e.transactionType == "Contribution"
                              ? "KSH ${e.amount}"
                              : "KSH ${e.amount}",
                          textAlign: TextAlign.right,
                          style: TextStyle(
                            color: e.transactionType == "Contribution"
                                ? PdfColor.fromHex("#0D0E24")
                                : PdfColor.fromHex("#008000"),
                          ),
                        ),
                      ),
                      flex: 1,
                    ),
                  ],
                ),
              ),
            ],
          ),
          Expanded(
              child: Padding(
            child: Text(
              "Thank you for using onekitty",
              style: Theme.of(context).header2,
              textAlign: TextAlign.center,
            ),
            padding: const EdgeInsets.all(20),
          )),
          Text(
            "Onekitty is a contribution patform that enables you to receive realtime contribution updates on Whatsapp,Telegram,email and sms.",
            textAlign: TextAlign.center,
          ),
          Divider(
            height: 1,
            borderStyle: BorderStyle.dashed,
          ),
        ];
      },
    ),
  );
  return pdf.save();
}