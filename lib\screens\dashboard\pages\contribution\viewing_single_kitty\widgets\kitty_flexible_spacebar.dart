import 'dart:io';
import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart' as c;
import 'package:flutter/rendering.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/utils/asset_urls.dart';
import 'package:onekitty/utils/image_popup.dart';
import 'package:onekitty/helpers/showCachedNetworkImage.dart';
import 'package:onekitty/utils/size_config.dart';
import 'package:path_provider/path_provider.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:share_plus/share_plus.dart';

class KittyFlexibleSpacebar extends StatelessWidget {
  KittyFlexibleSpacebar({super.key});
  final GlobalKey globalKeyy = GlobalKey();
  @override
  Widget build(BuildContext context) {
    final DataController dataController = Get.find();
    final activeIndex = 0.obs;
    final UserKittyController controller = Get.find();
    final KittyController kittyController = Get.find();
    final ContributeController getKittyController = Get.find();

    return FlexibleSpaceBar(
      background: Padding(
        padding: const EdgeInsets.all(2.0),
        child: ClipRRect(
          borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(25),
              bottomRight: Radius.circular(25)),
          child: Stack(
            children: [
              c.CarouselSlider.builder(
                itemCount: getKittyController.kittyMedia.length,
                itemBuilder: (context, index, realIndex) {
                  return InkWell(
                    onTap: () {
                      Get.to(() => ImagePopup(
                          pos: realIndex,
                          title: '',
                          imageUrl: getKittyController.kittyMedia
                              .map((e) => e.url ?? '')
                              .toList()));
                    },
                    child: ShowCachedNetworkImage(
                      fit: BoxFit.cover,
                      telegramLoading: true,
                      black: true,
                      width: MediaQuery.sizeOf(context).width,
                      imageurl: getKittyController.kittyMedia[index].url ?? '',
                    ),
                  );
                },
                options: c.CarouselOptions(
                  height: 260.h,
                  viewportFraction: 1.0,
                  enableInfiniteScroll: false,
                  onPageChanged: (index, reason) {
                    activeIndex(index);
                    // carouselController.jumpToPage(index);
                  },
                ),
              ),
              Positioned(
                bottom: 20.h,
                left: 0,
                right: 0,
                child: Obx(
                  () => Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      getKittyController.kittyMedia.length,
                      (index) => Container(
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: activeIndex.value == index
                              ? Colors.white70
                              : Colors.white24,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              if (getKittyController.kittyMedia.isNotEmpty)
                Positioned(
                    top: 8.h,
                    right: 12.w,
                    child: Column(
                      children: [
                        InkWell(
                          onTap: () async {
                            if (kittyController.isUploadingImage.value) {
                              return;
                            }
                            kittyController.pickImage(
                                context: context,
                                kittyId:
                                    dataController.kitty.value.kitty?.id ?? 0,
                                name: dataController.kitty.value.kitty?.title ??
                                    '');
                          },
                          child: Obx(
                            () => kittyController.isUploadingImage.value
                                ? const CircleAvatar(
                                    backgroundColor: Colors.black38,
                                    child: CircularProgressIndicator(
                                        backgroundColor: Colors.white))
                                : const CircleAvatar(
                                    backgroundColor: Colors.black38,
                                    child: Icon(Icons.add_a_photo_outlined,
                                        color: Colors.white)),
                          ),
                        ),
                        SizedBox(height: 8.h),
                        InkWell(
                          onTap: () async {
                            String shareMsg =
                                "Kitty Title: ${dataController.kitty.value.kitty?.title ?? ""}\nClick: https://onekitty.co.ke/kitty/${dataController.kitty.value.kitty?.id ?? 0}\n to Pay";
                            await Share.share(shareMsg,
                                subject: 'Kitty Details');
                          },
                          child: const CircleAvatar(
                              backgroundColor: Colors.black38,
                              child: Icon(Icons.share, color: Colors.white)),
                        ),
                        SizedBox(height: 8.h),
                        InkWell(
                          onTap: () {
                            showModalBottomSheet(
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10.0),
                              ),
                              context: context,
                              isScrollControlled: true,
                              constraints: BoxConstraints(
                                maxHeight: SizeConfig.screenHeight * .8,
                                maxWidth: SizeConfig.screenWidth,
                              ),
                              builder: (_) => SizedBox(
                                height: SizeConfig.screenHeight * .7,
                                width: SizeConfig.screenWidth,
                                child: Column(
                                  children: [
                                    const SizedBox(
                                      height: 10,
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.all(15.0),
                                      child: RepaintBoundary(
                                        key: globalKeyy,
                                        child: Container(
                                          margin: const EdgeInsets.all(20.0),
                                          color: Colors.white,
                                          child: Column(
                                            children: [
                                              const Text(
                                                "Scan to Pay",
                                                style: TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 20,
                                                ),
                                              ),
                                              QrImageView(
                                                padding:
                                                    const EdgeInsets.all(10.0),
                                                data:
                                                    "onekitty.co.ke/kitty/${dataController.kitty.value.kitty?.id}",
                                                version: QrVersions.auto,
                                                gapless: false,
                                                errorCorrectionLevel:
                                                    QrErrorCorrectLevel.H,
                                                embeddedImage:
                                                    AssetImage(AssetUrl.logo4),
                                                embeddedImageStyle:
                                                    const QrEmbeddedImageStyle(
                                                  size: Size(80, 80),
                                                ),
                                                size: 250.0,
                                              ),
                                              const SizedBox(
                                                height: 5.0,
                                              ),
                                              Text(
                                                "  onekitty.co.ke/kitty/${dataController.kitty.value.kitty?.id} ",
                                                style: const TextStyle(
                                                  color: Colors.blue,
                                                  fontSize: 16,
                                                ),
                                              ),
                                              const SizedBox(height: 10),
                                              Text(
                                                dataController.kitty.value.kitty
                                                        ?.title ??
                                                    "" "\n",
                                                style: const TextStyle(
                                                    fontSize: 17),
                                              )
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                    Container(
                                      margin:
                                          const EdgeInsets.only(right: 10.0),
                                      child: ElevatedButton(
                                          style: ElevatedButton.styleFrom(
                                            backgroundColor: Theme.of(context)
                                                .colorScheme
                                                .primary,
                                            fixedSize: const Size(250, 20),
                                          ),
                                          child: Text(
                                            "share".tr,
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontSize: 15,
                                            ),
                                          ),
                                          onPressed: () async {
                                            _captureAndSharePng();
                                          }),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                          child: const CircleAvatar(
                              backgroundColor: Colors.black38,
                              child: Icon(Icons.qr_code_2, color: Colors.white)
                              /* QrImageView(
                                                          data:
                                                              "https://onekitty.co.ke/kitty/${dataController.kitty.value.kitty?.id}",
                                                          version: QrVersions.auto,
                                                          gapless: false,
                                                          size: 48,
                                                        ),*/
                              ),
                        ),
                      ],
                    ))
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _captureAndSharePng() async {
    try {
      RenderRepaintBoundary boundary = globalKeyy.currentContext
          ?.findRenderObject() as RenderRepaintBoundary;

      var image = await boundary.toImage(pixelRatio: 5.0);

      ByteData byteData =
          await image.toByteData(format: ImageByteFormat.png) as ByteData;
      Uint8List pngBytes = byteData.buffer.asUint8List();

      final tempDir = await getTemporaryDirectory();
      final file = await File('${tempDir.path}/image.png').create();
      await file.writeAsBytes(pngBytes);

      await Share.shareXFiles(
        [XFile(file.path)],
      );
    } catch (e) {
      if (kDebugMode) {
        print(e.toString());
      }
    }
  }
}
