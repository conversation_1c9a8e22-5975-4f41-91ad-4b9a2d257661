import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:fl_country_code_picker/fl_country_code_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_quill/flutter_quill.dart' as q;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage_pro/get_storage_pro.dart';
import 'package:intl/intl.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/models/chama/chamaDto.dart';
import 'package:onekitty/models/chama/configs_model.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/widgets/contacts_controller.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/services/analytics.dart';
import 'package:onekitty/utils/cache_keys.dart';
import 'package:onekitty/utils/common_strings.dart';
import '../../../../utils/utils_exports.dart';
import '../contribution/widgets/date_picker.dart';
import '../contribution/widgets/success.dart';

class ChamaStepper extends ConsumerStatefulWidget {
  const ChamaStepper({super.key});

  @override
  _ChamaStepperState createState() => _ChamaStepperState();
}

enum Role {
  admin,
  treasurer,
  secretary,
  member,
}

class _ChamaStepperState extends ConsumerState<ChamaStepper> {
  final ChamaController _chamaController = Get.find<ChamaController>();
  final UserKittyController _userController = Get.find<UserKittyController>();

  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final GlobalKey<FormState> formKey1 = GlobalKey<FormState>();
  final GlobalKey<FormState> formKey2 = GlobalKey<FormState>();

  Map<String, Role> roleStatusMap = {};
  Role selectedStatus = Role.member;

  // Map<int, bool> adminStatusMap = {};
  TextEditingController chamaNameController = TextEditingController();
  DateTime combinedDateTime = DateTime.now();
  TextEditingController emailController = TextEditingController();
  TextEditingController descrController = TextEditingController();
  TextEditingController amountController = TextEditingController();
  TextEditingController linkController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  TextEditingController freqcyController = TextEditingController();
  TextEditingController accountrefController = TextEditingController();
  TextEditingController refController = TextEditingController();
  TextEditingController firstNameController = TextEditingController();
  TextEditingController lastNameController = TextEditingController();

  TextEditingController dateController = TextEditingController();
  TextEditingController timeController = TextEditingController();
  final q.QuillController _controller = q.QuillController.basic();
  String invitePhone = "";
  bool isInvite = true;
  bool isFormValid = false;
  PhoneNumber num = PhoneNumber(isoCode: 'KE');

  int currentStep = 0;
  var params = Get.parameters;

  String dropdownValue = 'Option 1';
  String? selectedvalue;
  String selectedRole = "MEMBER";

  final countryPicker = const FlCountryCodePicker();
  CountryCode? countryCode;

  List<String> dropdownItems = [];
  String deviceModel = "";
  String imeiCode = "";
  List<String> roleItems = [];
  final box = GetStorage();

  final _streamController = StreamController<void>();
  Stream<void> get _stream => _streamController.stream;

  void startTimer() {
    Timer.periodic(const Duration(seconds: 1), (timer) {
      _streamController.add(null);
    });
  }

  void cancelTimer() {
    _streamController.close();
  }

  @override
  void initState() {
    startTimer();
    _chamaController.getConfigs();
    dateController.text = DateFormat.yMd().format(DateTime.now());
    timeController.text = DateFormat().add_jm().format(DateTime.now());
    roleItems = _chamaController.roles.map((role) => role.role).toList();
    getfrequency();
    getDeviceInfo();
    super.initState();
  }

  Set<dynamic> selectedContacts = Set<dynamic>();

  final GlobalKey _tooltipKey1 = GlobalKey();
  final GlobalKey _tooltipKey2 = GlobalKey();
  final GlobalKey _tooltipKey3 = GlobalKey();

  void selectContact(
    dynamic selectedContact,
    BuildContext context,
  ) {
    if (!selectedContacts.contains(selectedContact)) {
      selectedContact.name.prefix = "MEMBER";
      ref
          .read(selectContactControllerProvider.notifier)
          .selectContact(selectedContact, context);
      selectedContacts.add(selectedContact);
      setState(() {});
    } else {
      ToastUtils.showInfoToast(context, "dynamic Already Selected",
          "Check"); // Show SnackBar if contact is already selected
    }
  }

  void addMyPhone() {
    String phoneNumber = _userController.getLocalUser()!.phoneNumber!;

    // Phone phone = Phone(phoneNumber, normalizedNumber: phoneNumber);

    // dynamic contact = dynamic(
    //   phones: [phone],
    // );
    // selectContact(contact, context);
  }

  void getfrequency() {
    // _chamaController.getfrequency();
    setState(() {
      dropdownItems = _chamaController.frequencies
          .map((frequency) => frequency.frequency)
          .toList();
      selectedvalue = _chamaController.frequencies.first.frequency;
    });
  }

  void clearContacts() async {
    ref
        .read(selectContactControllerProvider.notifier)
        .removeAllSelectedContacts();
  }

  @override
  void dispose() {
    //  cancelTimer();
    super.dispose();
  }

  void getDeviceInfo() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      setState(() {
        imeiCode = androidInfo.id;
        deviceModel = androidInfo.model;
      });
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      print(
          'Running on ${iosInfo.utsname.machine} ${iosInfo.identifierForVendor} ${iosInfo.model}');
      setState(() {
        imeiCode = iosInfo.identifierForVendor!;
        deviceModel = iosInfo.model;
      });
    } else {
      WebBrowserInfo webBrowserInfo = await deviceInfo.webBrowserInfo;
      setState(() {
        imeiCode = webBrowserInfo.userAgent!;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        minimum: const EdgeInsets.symmetric(vertical: 20),
        child: Column(
          children: [
            const RowAppBar(),
            Text("Create a Chama",
                style: Theme.of(context)
                    .textTheme
                    .titleLarge
                    ?.copyWith(fontWeight: FontWeight.bold, fontSize: 22)),
            const SizedBox(
              height: 10,
            ),
            Expanded(
              child: Theme(
                data: Theme.of(context).copyWith(
                    colorScheme:
                        const ColorScheme.light(primary: AppColors.primary)),
                child: Stepper(
                  connectorThickness: 2,
                  margin: const EdgeInsets.all(1),
                  elevation: 0,
                  currentStep: currentStep,
                  type: StepperType.horizontal,
                  steps: getSteps(),
                  onStepContinue: () {
                    final isLastStep = currentStep == getSteps().length - 1;
                    if (!isLastStep) {
                      setState(() {
                        // if(formKey.currentState!.validate()){
                        currentStep += 1;
                        // }
                      });
                    }
                  },
                  onStepCancel: currentStep == 0
                      ? null
                      : () {
                          setState(() {
                            currentStep -= 1;
                          });
                        },
                  controlsBuilder: (context, details) {
                    return SizedBox(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          currentStep != 0
                              ? OutlinedButton(
                                  onPressed: details.onStepCancel,
                                  child: const Text("Back"))
                              : Container(),
                          if (currentStep == 2)
                            Obx(
                              () => CustomKtButton(
                                isLoading: _chamaController.isAdding.isTrue,
                                onPress: () async {
                                  AnalyticsEngine.userCreatesKitty();
                                  final selected = ref
                                      .watch(selectContactControllerProvider
                                          .notifier)
                                      .getSelectedContacts();
                                  try {
                                    final List<Member> members = [];
                                    for (var index = 0 + 1;
                                        index < selected.length;
                                        index++) {
                                      final contact = selected[index];
                                      final phoneNumber =
                                          contact.phones.first.normalizedNumber;

                                      final member = Member(
                                        phoneNumber: phoneNumber,
                                        firstName: contact.name.first,
                                        secondName: contact.name.last,
                                        role: contact.name.prefix,
                                        receivingOrder: 1 + index,
                                        status: "ACTIVE",
                                      );
                                      members.add(member);
                                    }
                                    final chamaMembers = MembersDto(
                                        chamaId:
                                            _chamaController.createRes["ID"],
                                        members: members);

                                    final resp = await _chamaController
                                        .addMember(memebersDto: chamaMembers);
                                    if (resp) {
                                      ToastUtils.showSuccessToast(
                                        context,
                                        _chamaController.apiMessage.string,
                                        "success",
                                      );

                                      Get.to(SucessPage(
                                        text: 'created',
                                        kittyName: chamaNameController.text,
                                      ));
                                      clearContacts();
                                    } else {
                                      ToastUtils.showErrorToast(
                                        context,
                                        _chamaController.apiMessage.string,
                                        "Error",
                                      );
                                    }
                                  } catch (e) {
                                    ToastUtils.showErrorToast(
                                      context,
                                      "An error occurred while adding member.",
                                      "Error",
                                    );
                                  }
                                },
                                width: 100,
                                height: 40,
                                btnText: "Finish",
                              ),
                            ),
                          if (currentStep == 1)
                            Obx(
                              () => CustomKtButton(
                                isLoading: _chamaController.isloading.isTrue,
                                onPress: () async {
                                  if (formKey1.currentState!.validate()) {
                                    int? referCode;
                                    if (params.isNotEmpty) {
                                      referCode =
                                          int.tryParse(params["id"].toString());
                                    }
                                    DateTime date = DateFormat.yMd()
                                        .parse(dateController.text);

                                    TimeOfDay time = TimeOfDay.fromDateTime(
                                        DateFormat.Hm()
                                            .parse(timeController.text));

                                    DateTime combinedDateTime = DateTime(
                                      date.year,
                                      date.month,
                                      date.day,
                                      time.hour,
                                      time.minute,
                                    );
                                    final descr = jsonEncode(
                                      _controller.document.toDelta().toJson(),
                                    );

                                    try {
                                      final createDto = CreateDto(
                                          title: chamaNameController.text,
                                          description: descr.toString(),
                                          // description: _controller,
                                          phoneNumber:
                                              '+${_userController.getLocalUser()?.phoneNumber ?? ''}',
                                          countryCode: _userController
                                                  .getLocalUser()!
                                                  .countryCode ??
                                              "KE",
                                          email: emailController.text,
                                          refererCode: int.tryParse(
                                              refController.text.trim()),
                                          whatsAppLink: linkController.text,
                                          frequency: freqcyController.text,
                                          nextOccurrence:
                                              combinedDateTime.toUtc(),
                                          amount: int.parse(
                                            amountController.text.trim(),
                                          ),
                                          imeiCode: imeiCode,
                                          deviceModel: deviceModel,
                                          latitude: box.read(CacheKeys.lat),
                                          longitude: box.read(CacheKeys.long));

                                      final res = await _chamaController
                                          .createChama(createDto: createDto);
                                      if (res) {
                                        ToastUtils.showSuccessToast(
                                          context,
                                          _chamaController.apiMessage.string,
                                          "success",
                                        );
                                        addMyPhone();
                                        details.onStepContinue!();
                                      } else {
                                        ToastUtils.showErrorToast(
                                          context,
                                          _chamaController.apiMessage.string,
                                          "Error",
                                        );
                                      }
                                    } catch (e) {}
                                  }
                                },
                                height: 30.h,
                                width: 60.w,
                                btnText: "Next",
                              ),
                            ),
                          if (currentStep == 0)
                            CustomKtButton(
                              onPress: () {
                                if (formKey.currentState!.validate()) {
                                  details.onStepContinue!();
                                }
                              },
                              height: 30.h,
                              width: 60.w,
                              btnText: "Next",
                            ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Step> getSteps() => [
        Step(
            state: currentStep > 0 ? StepState.complete : StepState.indexed,
            title: const Divider(),
            label: const Text("Chama\n Details"),
            content: buildStepOne(context),
            isActive: currentStep >= 0),
        Step(
            state: currentStep > 1 ? StepState.complete : StepState.indexed,
            title: const Divider(),
            label: const Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // const Text("Add"),
                Text("Details"),
              ],
            ),
            content: buildStepTwo(context),
            isActive: currentStep >= 1),
        Step(
            state: currentStep == 2 ? StepState.complete : StepState.indexed,
            title: const Divider(),
            label: const Center(child: Text("Members")),
            content: buildStepThree(context),
            isActive: currentStep >= 2),
      ];

  Widget buildStepOne(BuildContext context) {
    return Form(
      key: formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("Chama Name",
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15)),
          CustomTextField(
            controller: chamaNameController,
            hintText: "e.g Wasafi Chama",
            labelText: "Chama Name",
            isRequired: true,
            validator: (p0) {
              if (p0!.isEmpty) {
                return "Chama Name cannot be empty";
              } else if (p0.length < 5) {
                return "Chama Name must be between 5 and 300";
              }
              return null;
            },
          ),
          Text("Chama Description",
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15)),
          SizedBox(
            height: 5.h,
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.blueAccent.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.0),
                border: Border.all(
                  color: AppColors.blueButtonColor,
                  width: 1.0,
                ),
              ),
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 8),
                  q.QuillToolbar.simple(
                    configurations: q.QuillSimpleToolbarConfigurations(
                      multiRowsDisplay: false,
                      sharedConfigurations: const q.QuillSharedConfigurations(
                        locale: Locale('de'),
                      ),
                      controller: _controller,
                    ),
                  ),
                  const SizedBox(height: 15),
                  q.QuillEditor.basic(
                    configurations: q.QuillEditorConfigurations(
                      placeholder: "e.g purpose of chama",
                      controller: _controller,
                      // readOnly: false,
                      autoFocus: false,
                      enableInteractiveSelection:
                          true, // Enable interactive selection to allow text editing

                      sharedConfigurations: const q.QuillSharedConfigurations(
                        locale: Locale('de'),
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                ],
              ),
            ),
          ),
          SizedBox(
            height: 10.h,
          ),
          Text(
            "Whatsapp group link(optional)",
            style: Theme.of(context)
                .textTheme
                .titleLarge
                ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15),
          ),
          SizedBox(
            height: 5.h,
          ),
          CustomTextField(
            labelText: "Group link",
            controller: linkController,
          ),
          SizedBox(
            height: 10.h,
          ),
          Text("Enter Referer code(Optional)",
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15)),
          SizedBox(
            height: 5.h,
          ),
          CustomTextField(
            controller: refController,
            hintText: "e.g 12",
            labelText: "Referral code",
          ),
        ],
      ),
    );
  }

  Widget buildStepTwo(BuildContext context) {
    String? description;
    for (Frequency freq in _chamaController.frequencies) {
      if (freq.frequency == freqcyController.text) {
        description = freq.description;
        break;
      }
    }
    return Form(
      key: formKey1,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Enter Chama Email",
            style: Theme.of(context)
                .textTheme
                .titleLarge
                ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15),
          ),
          SizedBox(
            height: 5.h,
          ),
          CustomTextField(
            controller: emailController,
            hintText: "e.g <EMAIL>",
            labelText: "",
            validator: (p0) {
              if (p0!.isEmpty) {
                return "Email cannot be empty";
              } else if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                  .hasMatch(p0)) {
                return "Enter a valid email address";
              }
              return null;
            },
          ),
          Row(
            children: [
              Text("How Often Do Members Contribute",
                  style: Theme.of(context)
                      .textTheme
                      .titleLarge
                      ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15)),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  final dynamic tooltip = _tooltipKey1.currentState;
                  tooltip.ensureTooltipVisible();
                },
                child: Tooltip(
                  key: _tooltipKey1,
                  message: KtStrings.often,
                  child: CustomImageView(
                    imagePath: AssetUrl.imgInbox,
                    height: 15.h,
                    width: 15.w,
                    margin: EdgeInsets.only(left: 3.w, top: 2.h, bottom: 3.h),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(
            height: 5.h,
          ),
          DropdownButtonFormField<String>(
            decoration: const InputDecoration(
              labelText: "Select Frequency",
              // fillColor: Colors.blueAccent.withOpacity(0.1),
            ),
            isExpanded: true,
            items: dropdownItems
                .map(
                  (String item) => DropdownMenuItem<String>(
                    value: item,
                    child: Text(
                      item,
                      style: const TextStyle(
                        fontSize: 14,
                      ),
                    ),
                  ),
                )
                .toList(),
            value: selectedvalue,
            onChanged: (String? value) {
              setState(() {
                selectedvalue = value;
                freqcyController.text = value!;
              });
            },
          ),
          Text(
            description ?? "",
            style: const TextStyle(fontStyle: FontStyle.italic),
          ),
          SizedBox(
            height: 5.h,
          ),
          Row(
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 8.0),
                child: Text("How Much Does each Member Contribute",
                    style: Theme.of(context)
                        .textTheme
                        .titleLarge
                        ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15)),
              ),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  final dynamic tooltip = _tooltipKey2.currentState;
                  tooltip.ensureTooltipVisible();
                },
                child: Tooltip(
                  key: _tooltipKey2,
                  message: KtStrings.amount,
                  child: CustomImageView(
                    imagePath: AssetUrl.imgInbox,
                    height: 15.h,
                    width: 15.w,
                    margin: EdgeInsets.only(left: 3.w, top: 2.h, bottom: 3.h),
                  ),
                ),
              ),
            ],
          ),
          CustomTextField(
            inputFormatters: <TextInputFormatter>[
              FilteringTextInputFormatter.allow(RegExp("[0-9]")),
            ],
            controller: amountController,
            hintText: "1000",
            isRequired: true,
            labelText: "Amount",
            validator: (p0) {
              if (p0!.isEmpty) {
                return "Amount is required";
              }
              return null;
            },
          ),
          const SizedBox(
            height: 10,
          ),
          Row(
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 8.0),
                child: Text(
                    "Set a Deadline For Your ${freqcyController.text} Contribution",
                    style: Theme.of(context)
                        .textTheme
                        .titleLarge
                        ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15)),
              ),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  final dynamic tooltip = _tooltipKey3.currentState;
                  tooltip.ensureTooltipVisible();
                },
                child: Tooltip(
                  key: _tooltipKey3,
                  message: KtStrings.deadline,
                  child: CustomImageView(
                    imagePath: AssetUrl.imgInbox,
                    height: 15.h,
                    width: 15.w,
                    margin: EdgeInsets.only(left: 3.w, top: 2.h, bottom: 3.h),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(
            height: 8.h,
          ),
          DatePick(
            date: dateController,
            time: timeController,
            combinedDateTime: combinedDateTime,
          ),
          SizedBox(
            height: 10.h,
          ),
        ],
      ),
    );
  }

  Widget buildStepThree(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        width: double.maxFinite,
        padding: EdgeInsets.all(10.w),
        child: Column(
          children: [
            Text("Invite Chama Members", style: theme.textTheme.titleLarge),
            SizedBox(height: 20.h),
            _buildFrame(context),
            SizedBox(height: 20.h),
            _buildAddNumber(context),
            SizedBox(height: 20.h),
            StreamBuilder<void>(
              stream: _stream,
              builder: (context, snapshot) {
                return buildInviteContacts(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFrame(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 2.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
              padding: EdgeInsets.only(left: 4.w),
              child: Text("Phone Number",
                  style: CustomTextStyles.titleSmallGray90001)),
          SizedBox(height: 8.h),
          _inputPhoneNumber(context)
        ],
      ),
    );
  }

  Widget _inputPhoneNumber(BuildContext context) {
    return Form(
      key: formKey2,
      child: Row(
        children: [
          Expanded(
            child: InternationalPhoneNumberInput(
              onInputChanged: (num) {
                setState(() {
                  invitePhone = num.phoneNumber!;
                });
              },
              onInputValidated: (bool value) {},
              selectorConfig: const SelectorConfig(
                selectorType: PhoneInputSelectorType.BOTTOM_SHEET,
                useBottomSheetSafeArea: true,
              ),
              ignoreBlank: false,
              autoValidateMode: AutovalidateMode.onUserInteraction,
              selectorTextStyle: const TextStyle(color: Colors.black),
              initialValue: num,
              textFieldController: phoneController,
              formatInput: true,
              keyboardType: const TextInputType.numberWithOptions(
                  signed: true, decimal: true),
              inputBorder: const OutlineInputBorder(),
            ),
          ),
          SizedBox(
            width: 5.h,
          ),
          CustomElevatedButton(
              leftIcon: Container(
                margin: EdgeInsets.only(right: 1.w),
                child: CustomImageView(
                    imagePath: AssetUrl.imgPlus, height: 15.h, width: 15.w),
              ),
              onPressed: () {
                if (formKey2.currentState!.validate()) {
                  String phoneNumber = invitePhone;

                  // Phone phone =
                  //     Phone(phoneNumber, normalizedNumber: phoneNumber);

                  // dynamic contact = dynamic(
                  //   phones: [phone],
                  // );
                  // selectContact(contact, context);
                  // ToastUtils.showSuccessToast(context, "Number added", "",
                  //     toastPosition: Position.center);
                  setState(() {
                    phoneController.clear();
                  });
                  // } else {
                  //   ToastUtils.showErrorToast(
                  //       context, "Enter a number to continue", "Error");
                }
              },
              height: 30.h,
              width: 70.w,
              text: "Add",
              buttonStyle: CustomButtonStyles.fillIndigR,
              buttonTextStyle: CustomTextStyles.titleSmallWhiteA700),
        ],
      ),
    );
  }

  Widget _buildAddNumber(BuildContext context) {
    return CustomElevatedButton(
        width: 250.w,
        height: 40.h,
        text: "Select from contacts",
        leftIcon: Container(
            margin: EdgeInsets.only(right: 8.w),
            child: CustomImageView(
                imagePath: AssetUrl.imgPlus, height: 18.h, width: 18.w)),
        buttonStyle: CustomButtonStyles.fillPrimary,
        onPressed: () {
          Get.toNamed(NavRoutes.selectcontacts, arguments: isInvite);
        });
  }

  Widget buildInviteContacts(BuildContext context) {
    final selectedContacts = ref
        .watch(selectContactControllerProvider.notifier)
        .getSelectedContacts();
    startTimer();
    // Check if selected contacts list is empty
    if (selectedContacts.isEmpty) {
      return CustomImageView(
        imagePath: AssetUrl.imgGroup13,
        height: 150.h,
        width: 254.w,
      );
    } else {
      return Container(
        height: 400.h,
        margin: EdgeInsets.only(left: 2.h),
        padding: EdgeInsets.symmetric(horizontal: 2.h, vertical: 17.h),
        decoration: AppDecoration.outlineGray
            .copyWith(borderRadius: BorderRadiusStyle.roundedBorder8),
        child: Column(
          children: [
            Expanded(
              child: ListView.builder(
                itemCount: ref
                    .watch(selectContactControllerProvider.notifier)
                    .getSelectedContacts()
                    .length,
                itemBuilder: (context, index) {
                  final selectedContact = ref
                      .watch(selectContactControllerProvider.notifier)
                      .getSelectedContacts()[index];

                  Role roleStatus = roleStatusMap[
                          selectedContact.phones.first.normalizedNumber] ??
                      Role.member;

                  return Container(
                    decoration: BoxDecoration(
                        borderRadius: BorderRadiusStyle.roundedBorder8),
                    child: Column(
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CustomImageView(
                              imagePath: AssetUrl.dotSix,
                              height: 25.h,
                              width: 25.w,
                              margin: EdgeInsets.only(right: 3.h),
                            ),
                            Opacity(
                              opacity: 0.5,
                              child: Padding(
                                padding: EdgeInsets.only(top: 6.h, bottom: 8.h),
                                child: Text(
                                  "${index + 1}",
                                  style: theme.textTheme.titleSmall!.copyWith(
                                    color:
                                        appTheme.blueGray700.withOpacity(0.53),
                                  ),
                                ),
                              ),
                            ),
                            CustomImageView(
                              imagePath: AssetUrl.imgPerson,
                              height: 25.h,
                              width: 25.w,
                              margin: EdgeInsets.only(left: 3.h),
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Padding(
                                  padding: EdgeInsets.only(
                                      left: 6.h, top: 1.h, bottom: 1.h),
                                  child: Text(
                                    index == 0
                                        ? _userController
                                            .getLocalUser()!
                                            .firstName!
                                        : "${selectedContact.name.first} ${selectedContact.name.last}",
                                    overflow: TextOverflow.ellipsis,
                                    style: CustomTextStyles.titleSmallGray90001
                                        .copyWith(color: appTheme.gray90001),
                                  ),
                                ),
                                Padding(
                                  padding: EdgeInsets.only(
                                      left: 6.h, top: 1.h, bottom: 1.h),
                                  child: Text(
                                    selectedContact.phones.first.number,
                                    overflow: TextOverflow.ellipsis,
                                    style: CustomTextStyles.titleSmallGray90001
                                        .copyWith(color: appTheme.gray90001),
                                  ),
                                ),
                                selectedContact.name.prefix == "CHAIRPERSON"
                                    ? CustomImageView(
                                        imagePath: AssetUrl.crownsv,
                                        height: 18.h,
                                        width: 18.w,
                                        margin:
                                            EdgeInsets.symmetric(vertical: 9.h),
                                      )
                                    : const SizedBox.shrink(),
                                index == 0
                                    ? Text(
                                        "CHAIRPERSON",
                                        style: TextStyle(
                                            fontStyle: FontStyle.italic,
                                            color: getRoleColors("CHAIRPERSON"),
                                            fontWeight: FontWeight.bold),
                                      )
                                    : Text(
                                        selectedContact.name.prefix,
                                        style: context.dividerTextLarge
                                            ?.copyWith(
                                                fontWeight: FontWeight.bold,
                                                color: getRoleColors(
                                                    selectedContact
                                                        .name.prefix)),
                                      ),
                              ],
                            ),
                            const Spacer(),
                            InkWell(
                              onTap: () {
                                ref
                                    .read(selectContactControllerProvider
                                        .notifier)
                                    .removeSelectedContact(selectedContact);
                                setState(() {
                                  roleStatusMap.remove(selectedContact
                                      .phones.first.normalizedNumber);
                                });
                              },
                              child: index == 0
                                  ? const SizedBox()
                                  : CustomImageView(
                                      imagePath: AssetUrl.imgIconoirCancel,
                                      height: 18.h,
                                      width: 18.w,
                                      margin: EdgeInsets.symmetric(
                                          vertical: 9.h, horizontal: 5.h),
                                    ),
                            ),
                            index == 0
                                ? const SizedBox.shrink()
                                : IconButton(
                                    icon: const Icon(
                                      Icons.edit,
                                      color: AppColors.blueButtonColor,
                                    ),
                                    padding:
                                        EdgeInsets.symmetric(vertical: 10.h),
                                    onPressed: () {
                                      _chamaOptionsDialog(context, roleStatus,
                                          index, selectedContact);
                                    },
                                  )
                          ],
                        ),
                        SizedBox(height: 2.h),
                        const Divider()
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      );
    }
  }

  void _chamaOptionsDialog(BuildContext context, Role roleStatus, int index,
      dynamic selectedContact) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        // Local state for the selected status within the dialog
        Role selectedStatus = roleStatus;

        firstNameController.text = selectedContact.name.first;
        lastNameController.text = selectedContact.name.last;
        selectedRole = selectedContact.name.prefix;

        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return Align(
              alignment: Alignment.centerRight,
              child: AlertDialog(
                title: const Text(
                  "Update Member Details",
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                content: SizedBox(
                  height: 200.h,
                  child: Column(
                    children: [
                      CustomTextField(
                        controller: firstNameController,
                        labelText: "Enter First Name",
                      ),
                      CustomTextField(
                        controller: lastNameController,
                        labelText: "Enter Last Name",
                      ),
                      SizedBox(
                        height: 8.h,
                      ),
                      DropdownButtonFormField<String>(
                        decoration: const InputDecoration(
                          labelText: "Select Role",
                          // fillColor: Colors.blueAccent.withOpacity(0.1),
                        ),
                        isExpanded: true,
                        items: roleItems
                            .map(
                              (String item) => DropdownMenuItem<String>(
                                value: item,
                                child: Text(
                                  item,
                                  style: const TextStyle(
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                            )
                            .toList(),
                        value: selectedRole,
                        onChanged: (String? value) {
                          setState(() {
                            selectedRole = value!;
                          });
                        },
                      ),
                    ],
                  ),
                ),
                actions: [
                  CustomElevatedButton(
                    text: "Save",
                    onPressed: () {
                      try {
                        updateContact(
                            selectedContact.phones.first.normalizedNumber,
                            firstNameController.text,
                            lastNameController.text,
                            selectedRole);
                      } catch (e) {}

                      Navigator.of(context).pop(); // Close the dialog
                    },
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  void updateContact(
    String id,
    String newFirstName,
    String newLastName,
    String role,
  ) {
    setState(() {
      final selectedContact = ref
          .watch(selectContactControllerProvider.notifier)
          .getSelectedContacts();

      //access and update the contact list
      final contact = selectedContact
          .firstWhere((contact) => contact.phones.first.normalizedNumber == id);
      contact.name.first = newFirstName;
      contact.name.last = newLastName;
      contact.name.prefix = role;
    });
  }
}
