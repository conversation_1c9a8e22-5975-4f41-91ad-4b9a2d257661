import 'dart:async';
import 'package:animations/animations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/admin_screens/events/alltransactions_admin.dart';
import 'package:onekitty/admin_screens/events/block_event_page.dart';
import 'package:onekitty/controllers/events/controllers.dart';
import 'package:onekitty/controllers/events/edit_event_controller.dart';
import 'package:onekitty/controllers/events/view_single_event.dart';
import 'package:onekitty/controllers/events/vieweventcontroller.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/models/events/events_model.dart';
import 'package:onekitty/models/events/media_models.dart';
import 'package:onekitty/models/transac_kitt_model.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/see_all_transactions_screen/see_all_transactions_screen.dart';
import 'package:onekitty/screens/dashboard/pages/events/edit_event.dart';
import 'package:onekitty/screens/dashboard/pages/events/edit_ticket_page.dart';
import 'package:onekitty/screens/dashboard/pages/events/invite_users.dart';
import 'package:onekitty/screens/dashboard/pages/events/manage_delegates.dart';
import 'package:onekitty/utils/asset_urls.dart';
import 'package:onekitty/utils/date_formatter.dart';
import 'package:onekitty/utils/formatted_currency.dart';
import 'package:onekitty/utils/image_popup.dart';
import 'package:onekitty/utils/my_button.dart';
import 'package:onekitty/utils/my_quill_editor.dart';
import 'package:onekitty/utils/responsive_size.dart';
import 'package:onekitty/utils/search_widget.dart';
import 'package:onekitty/utils/show_cached_network_image.dart';
import 'package:onekitty/utils/timeSince.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import '../../../../helpers/colors.dart';
import '../../../../models/events/tickets_model.dart';
import '/utils/glassmorphic.dart';
import 'map_page.dart';
import 'signatory_transactions.dart';
import 'package:carousel_slider/carousel_slider.dart' as q;
import 'transfers_page.dart';
import 'verify_ticket.dart';
import 'view_single_event_viewer.dart' as ve;

class ViewSingleEventOrganizer extends StatefulWidget {
  final MyEventsModel eventmodel;
  const ViewSingleEventOrganizer({super.key, required this.eventmodel});

  @override
  State<ViewSingleEventOrganizer> createState() =>
      _ViewSingleEventOrganizerState();
}

class _ViewSingleEventOrganizerState extends State<ViewSingleEventOrganizer> {
  final ViewSingleEventController controller =
      Get.put(ViewSingleEventController());
  final getEventController = Get.put(EditEventController());
  final RefreshController _refreshController =
      RefreshController(initialRefresh: true);
  void _onRefresh() async {
    try {
      await controller.fetchTransactions(widget.eventmodel.event.id);
      getEventController.fetchEventDetail(widget.eventmodel.event.id,
          isOrganizer: true);
      _refreshController.refreshCompleted();
    } catch (e) {
      _refreshController.refreshCompleted();
      Logger().e(e);
    }
  }

  final carouselController = q.CarouselSliderController();
  @override
  void initState() {
    super.initState();
    _onRefresh();
  }

  final activeIndex = 0.obs;
  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
        designSize: const Size(392.72727272727275, 803.6363636363636),
        builder: (context, _) {
          return Scaffold(
              body: Obx(
            () => SmartRefresher(
              onRefresh: _onRefresh,
              controller: _refreshController,
              child: CustomScrollView(slivers: [
                SliverAppBar(
                    // floating: true,
                    leadingWidth: 88,
                    pinned: true,
                    backgroundColor: Theme.of(context).scaffoldBackgroundColor,
                    bottom: PreferredSize(
                        preferredSize: const Size(0, 0),
                        child: Container(
                            height: ResponsiveSize.height(context,45),
                            alignment: Alignment.center,
                            width: ResponsiveSize.width(context,280),
                            padding: const EdgeInsets.symmetric(
                                vertical: 1, horizontal: 2),
                            decoration: BoxDecoration(
                                color:
                                    Theme.of(context).scaffoldBackgroundColor,
                                boxShadow: [
                                  const BoxShadow(
                                      color: Colors.black,
                                      offset:  Offset(0, 2),
                                      blurRadius: 12,
                                      spreadRadius: -4)
                                ],
                                borderRadius: BorderRadius.circular(30.r)),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                AttendeesWidget(
                                  count: widget.eventmodel.count,
                                  size: 18.spMin,
                                  textSize: 15.spMin,
                                ),
                                Material(
                                  borderRadius: BorderRadius.circular(25),
                                  color: primaryColor,
                                  child: SizedBox(
                                    height: ResponsiveSize.height(context,30),
                                    width: ResponsiveSize.width(context,70),
                                    child: MaterialButton(
                                        onPressed: () {
                                          Get.dialog(InviteUser(
                                              eventId:
                                                  controller.event.value.id,
                                              ticket: widget.eventmodel.event
                                                      .tickets ??
                                                  []));
                                        },
                                        child: Text(
                                          'Invite',
                                          style: TextStyle(
                                              color: Colors.white,
                                              fontWeight: FontWeight.w400,
                                              fontSize: 12.spMin),
                                        )),
                                  ),
                                )
                              ],
                            ))),
                    leading: GlassmorphicContainer(
                      onTap: () => Navigator.pop(context),
                      color: Colors.black,
                      blurRadius: 20,
                      cornerRadius: 24,
                      child: const Row(
                        children: [
                          Icon(
                            Icons.arrow_back,
                            color: Colors.white,
                            size: 18,
                          ),
                          SizedBox(width: 8),
                          Text('Back', style: TextStyle(color: Colors.white))
                        ],
                      ),
                    ),
                    expandedHeight: ResponsiveSize.height(context,255),
                    flexibleSpace: FlexibleSpaceBar(
                        background:
                            Stack(alignment: Alignment.topCenter, children: [
                      SizedBox(
                        height: ResponsiveSize.height(context,260),
                        child: Stack(
                          children: [
                            q.CarouselSlider.builder(
                              itemCount:
                                  controller.event.value.eventMedia?.length ??
                                      0,
                              itemBuilder: (context, index, realIndex) {
                                return Hero(
                                  tag: realIndex == 0
                                      ? 'image:${controller.event.value.id}o'
                                      : 'image:${controller.event.value.id}o$realIndex',
                                  child: InkWell(
                                    onTap: () {
                                      Get.to(() => ImagePopup(
                                          pos: realIndex,
                                          title: controller.event.value.title,
                                          imageUrl: controller
                                                  .event.value.eventMedia
                                                  ?.map((e) => e.url)
                                                  .toList() ??
                                              <String>[]));
                                    },
                                    child: ShowCachedNetworkImage(
                                      fit: BoxFit.cover,
                                      width: MediaQuery.sizeOf(context).width,
                                      imageurl: controller.event.value
                                              .eventMedia?[index].url ??
                                          AssetUrl.onekittyBannnerUrl,
                                    ),
                                  ),
                                );
                              },
                              options: q.CarouselOptions(
                                height: ResponsiveSize.height(context,260),
                                viewportFraction: 1.0,
                                enableInfiniteScroll: false,
                                onPageChanged: (index, reason) {
                                  activeIndex(index);
                                  // carouselController.jumpToPage(index);
                                },
                              ),
                            ),
                            (controller.event.value.eventMedia ??
                                        <EventMedia>[])
                                    .isEmpty
                                ? const SizedBox()
                                : Positioned(
                                    bottom: ResponsiveSize.height(context,38),
                                    left: 0,
                                    right: 0,
                                    child: Obx(
                                      () => Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: List.generate(
                                          controller.event.value.eventMedia
                                                  ?.length ??
                                              0,
                                          (index) => Container(
                                            margin: const EdgeInsets.symmetric(
                                                horizontal: 4),
                                            width: 8,
                                            height: 8,
                                            decoration: BoxDecoration(
                                              shape: BoxShape.circle,
                                              color: activeIndex.value == index
                                                  ? Colors.white70
                                                  : Colors.white24,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                          ],
                        ),
                      ),
                    ]))),
                SliverToBoxAdapter(
                  child: Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: 20.0, vertical: ResponsiveSize.height(context,8)),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // if (kDebugMode) Text('${controller.event.value.id}'),
                              Hero(
                                tag: 'text:${controller.event.value.id}o',
                                child: Material(
                                  color: Colors.transparent,
                                  child: Text(controller.event.value.title,
                                      style: const TextStyle(
                                          // color: Colors.black,
                                          fontWeight: FontWeight.w700,
                                          fontSize: 22)),
                                ),
                              ),
                              Container(
                                  margin:
                                      const EdgeInsets.only(left: 2, top: 4),
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 4, horizontal: 8),
                                  decoration: BoxDecoration(
                                      border: Border.all(color: Colors.grey),
                                      borderRadius: BorderRadius.circular(24)),
                                  child: Text(
                                    controller.event.value.status?.name ?? '',
                                    style: TextStyle(
                                      color: getEventStatusColor(
                                          controller.event.value.status?.name ??
                                              ""),
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ))
                            ],
                          ),
                        ),
                        Hero(
                          tag: 'collected${controller.event.value.id}o',
                          child: Material(
                            color: Colors.transparent,
                            child: Text.rich(
                                textAlign: TextAlign.end,
                                TextSpan(children: [
                                  TextSpan(
                                      text:
                                          '${FormattedCurrency().getFormattedCurrency(widget.eventmodel.event.balance)}\n',
                                      style: const TextStyle(
                                          color: Color(0xff4355b6),
                                          fontSize: 30,
                                          fontWeight: FontWeight.w600)),
                                  const TextSpan(text: 'Collected')
                                ])),
                          ),
                        )
                      ],
                    ),
                  ),
                ),
                SliverToBoxAdapter(
                  child: Column(
                    children: [
                      ListTile(
                        onTap: () => Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => MapScreen(
                              viewOnly: true,
                              longitude: controller.event.value.longitude,
                              latitude: controller.event.value.latitude,
                            ),
                          ),
                        ),
                        leading: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              color: primaryColor.withOpacity(0.15),
                            ),
                            child: Hero(
                                tag: "ilocation${controller.event.value.id}o",
                                child: Image.asset(
                                    'assets/images/icons/location.png',
                                    height: 30,
                                    width: 30,
                                    color: primaryColor)

                                //  const Icon(Icons.location_on,
                                //     color: primaryColor)

                                )),
                        title: Hero(
                          tag: "tlocation${controller.event.value.id}o",
                          child: Material(
                            color: Colors.transparent,
                            child: Text(
                              controller.event.value.venue,
                              style:
                                  const TextStyle(fontWeight: FontWeight.w600),
                            ),
                          ),
                        ),
                        subtitle: Text(controller.event.value.locationTip,
                            style: const TextStyle(
                                fontSize: 12, color: Colors.grey)),
                      ),
                      ListTile(
                        onTap: () {
                          showDialog(
                            context: context,
                            builder: (context) => Dialog(
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    CalendarDatePicker(
                                      initialDate:
                                          controller.event.value.startDate!,
                                      firstDate: DateTime(2000),
                                      lastDate: DateTime(2100),
                                      onDateChanged: (_) {},
                                      selectableDayPredicate: (DateTime date) {
                                        return date.year ==
                                                controller.event.value
                                                    .startDate!.year &&
                                            date.month ==
                                                controller.event.value
                                                    .startDate!.month &&
                                            date.day ==
                                                controller
                                                    .event.value.startDate!.day;
                                      },
                                    ),
                                    Align(
                                      alignment: Alignment.centerRight,
                                      child: TextButton(
                                        onPressed: () => Navigator.pop(context),
                                        child: const Text('Close'),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                        leading: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              color: primaryColor.withOpacity(0.15),
                            ),
                            child: Hero(
                              tag: "idate${controller.event.value.id}o",
                              child: Image.asset(
                                  'assets/images/icons/calendar.png',
                                  height: 30,
                                  width: 30,
                                  color: primaryColor),
                              //  const Icon(Icons.calendar_month_rounded,
                              //     color: primaryColor)
                            )),
                        title: Hero(
                          tag: "tdate${controller.event.value.id}o",
                          child: Material(
                            color: Colors.transparent,
                            child: Text(
                              formatDate(
                                  "${controller.event.value.startDate?.toLocal()}"),
                              style:
                                  const TextStyle(fontWeight: FontWeight.w600),
                            ),
                          ),
                        ),
                        subtitle: Text(
                            controller.event.value.startDate == null ||
                                    controller.event.value.endDate == null
                                ? ''
                                : '${DateFormat('EEEE').format(controller.event.value.startDate?.toLocal() ?? DateTime.now())}, ${formatTime("${controller.event.value.startDate?.toLocal()}")} - ${formatDate("${controller.event.value.startDate?.toLocal()}") == formatDate("${controller.event.value.endDate?.toLocal()}") ? formatTime("${controller.event.value.endDate?.toLocal()}") : "${DateFormat('EEEE').format(controller.event.value.endDate?.toLocal() ?? DateTime.now())}, ${formatTime("${controller.event.value.endDate?.toLocal()}")}"}\n'
                                    '${highPrecisiontimeSince(controller.event.value.endDate?.toLocal() ?? DateTime.now(), preffixFutureDate: '', suffixFutureDate: 'left', preffixPastDate: 'ended')}',
                            style: const TextStyle(
                                fontSize: 12, color: Colors.grey)),
                      ),
                    ],
                  ),
                ),
                SliverToBoxAdapter(
                  child: Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: ResponsiveSize.width(context,12.0), vertical: ResponsiveSize.height(context,2)),
                    child: Text(
                      'About Event',
                      style: TextStyle(
                        fontSize: 16.spMin,
                        fontWeight: FontWeight.w500,
                        // color: Colors.black
                      ),
                    ),
                  ),
                ),
                SliverToBoxAdapter(
                    child: Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: ResponsiveSize.width(context,12.0), vertical: ResponsiveSize.height(context,4)),
                  child: Hero(
                    tag: 'desc:${controller.event.value.id}o',
                    child: QuillEditorWidget(
                        text: controller.event.value.description),
                  ),
                )),
                SliverToBoxAdapter(
                    child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: ResponsiveSize.width(context,2)),
                  child: Builder(builder: (context) {
                    final ValueNotifier<int> selectedIndex = ValueNotifier(0);
                    return ValueListenableBuilder(
                        valueListenable: selectedIndex,
                        builder: (context, index, child) {
                          return Column(children: [
                            Container(
                                padding: EdgeInsets.symmetric(
                                    vertical: ResponsiveSize.height(context,6), horizontal: ResponsiveSize.width(context,8)),
                                margin: EdgeInsets.all(8.spMin),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(15),
                                  color: primaryColor.withOpacity(0.15),
                                ),
                                child: Row(
                                  children: [
                                    Expanded(
                                      child: GestureDetector(
                                        onTap: () => selectedIndex.value = 0,
                                        child: Container(
                                          padding: const EdgeInsets.all(8),
                                          alignment: Alignment.center,
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(7.5),
                                            color: index == 0
                                                ? Colors.white
                                                : null,
                                          ),
                                          child: Text('Services',
                                              style: TextStyle(
                                                fontWeight: FontWeight.w600,
                                                fontSize: 14.spMin,
                                                color: index == 0
                                                    ? primaryColor
                                                    : null,
                                              )),
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                      child: GestureDetector(
                                        onTap: () => selectedIndex.value = 1,
                                        child: Container(
                                          alignment: Alignment.center,
                                          padding: const EdgeInsets.all(8),
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(7.5),
                                            color: index == 1
                                                ? Colors.white
                                                : null,
                                          ),
                                          child: Text('My Transactions',
                                              style: TextStyle(
                                                fontWeight: FontWeight.w600,
                                                fontSize: 14.spMin,
                                                color: index == 1
                                                    ? primaryColor
                                                    : null,
                                              )),
                                        ),
                                      ),
                                    )
                                  ],
                                )),
                            if (index == 0)
                              Wrap(children: [
                                ServicesWidget(
                                    page: EditTickectPage(
                                      eventId: controller.event.value.id,
                                    ),
                                    label: 'Edit ticket',
                                    image: 'assets/images/icons/ticketlogo.png',
                                    icon: Icons.airplane_ticket_outlined),
                                ServicesWidget(
                                    page: EditEventPage(
                                      myevent: widget.eventmodel,
                                      id: controller.event.value.id,
                                    ),
                                    refreshController: _refreshController,
                                    // onTap: () async {
                                    //   var res = await Navigator.push(
                                    //       context,
                                    //       MaterialPageRoute(
                                    //           builder: (context) =>
                                    //               ));
                                    //   if (res ?? false) {
                                    //     _refreshController.requestRefresh();
                                    //   }
                                    // },
                                    label: 'Edit event',
                                    icon: Icons.edit_calendar_rounded),
                                ServicesWidget(
                                    // onTap: () async {
                                    //   var res = await Navigator.push(
                                    //       context,
                                    //       MaterialPageRoute(
                                    //           builder: (context) => ));
                                    //   if (res ?? false) {
                                    //     _refreshController.requestRefresh();
                                    //   }
                                    // },
                                    page: InvitePage(
                                      eventname: controller.event.value.title,
                                      kittyId: widget.eventmodel.event.kittyId!,
                                    ),
                                    label: 'Manage Delegates',
                                    icon: Icons.group_add_outlined),
                                ServicesWidget(
                                    page: ve.ViewSingleEventViewer(
                                      event: controller.event.value,
                                    ),
                                    label: 'Purchase Ticket',
                                    icon: Icons.monetization_on_outlined),
                                ServicesWidget(
                                    refreshController: _refreshController,
                                    flash: false,
                                    page: SignatoryTransactions(
                                        kittyId:
                                            controller.event.value.kittyId!),
                                    label: 'Signatory Transactions',
                                    icon: Icons.verified_outlined),
                                ServicesWidget(
                                    flash: false,
                                    page: VerifyTicket(
                                      eventId: controller.event.value.id,
                                    ),
                                    label: 'Verify Tickets',
                                    icon: Icons.document_scanner_outlined),
                                ServicesWidget(
                                  page: TransferScreen(
                                    kittyId: controller.event.value.kittyId!,
                                  ),
                                  label: 'Transfer funds',
                                  icon: Icons.currency_exchange,
                                  flash: false,
                                ),
                                 ServicesWidget(
                                  page: BlockEventPage(
                                    eventId: controller.event.value.id,
                                  ),
                                  label: 'Block Event',
                                  icon: Icons.block,
                                  flash: false,
                                ),
                              ]),
                            if (index == 1)
                              Padding(
                                padding: EdgeInsets.only(top: ResponsiveSize.height(context,20)),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    const Text(
                                      "Transactions",
                                    ),
                                    IconButton(
                                        icon: const Icon(
                                            Icons.table_view_outlined),
                                        onPressed: () {
                                          Get.to(() => EventsTransactionTable(
                                              eventId:
                                                  controller.event.value.id));
                                        }),
                                    TextButton(
                                      onPressed: () {
                                        final transController =
                                            Get.find<KittyController>();
                                        transController.getKittyContributions(
                                            kittyId:
                                                controller.event.value.kittyId!,
                                            eventId: controller.event.value.id);
                                        Get.to(() => AllTransactionsScreen(
                                              kittyId: controller
                                                  .event.value.kittyId,
                                              eventId:
                                                  controller.event.value.id,
                                            ));
                                      },
                                      child: const Text('See All'),
                                    ),
                                  ],
                                ),
                              ),
                            if (index == 1)
                              Obx(() {
                                return Container(
                                    margin: const EdgeInsets.all(15),
                                    padding: const EdgeInsets.all(2),
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(15),
                                        border: Border.all(
                                            color: Colors.grey.shade300),
                                        color: Colors.white10),
                                    child: controller.transactions.isEmpty
                                        ? SizedBox(
                                            height: ResponsiveSize.height(context,200),
                                            child: const Center(
                                                child: Text(
                                                    'No Transactions Found')))

                                        // Text('${controller.transactions}')
                                        : GroupedTransactionsList(
                                            // ignore: invalid_use_of_protected_member
                                            transactions:
                                                controller.transactions,
                                          ));
                              })
                          ]);
                        });
                  }),
                )),
                SliverToBoxAdapter(child: SizedBox(height: ResponsiveSize.height(context,300))),
              ]),
            ),
          ));
        });
  }
}

class ServicesWidget extends StatelessWidget {
  final String label;
  final IconData icon;
  final Widget page;
  final String? image;
  final bool? flash;
  final RefreshController? refreshController;

  const ServicesWidget(
      {super.key,
      required this.label,
      required this.icon,
      required this.page,
      this.image,
      this.flash,
      this.refreshController});

  @override
  Widget build(BuildContext context) {
    final flashColor = primaryColor.obs;
    if (flash ?? false) {
      Timer.periodic(const Duration(milliseconds: 800), (t) {
        flashColor.value = t.tick.isEven ? primaryColor : Colors.red;
      });
    }
    return OpenContainer(
      closedElevation: 0,
      closedColor: Colors.transparent,
      openBuilder: (context, action) => page,
      closedBuilder: (context, action) => InkWell(
          onTap: () => {
                action.call(),
                if (refreshController != null)
                  {refreshController!.requestRefresh()}
              },
          child: Obx(() {
            return InkWell(
              child: Container(
                height: ResponsiveSize.height(context,90),
                width: ResponsiveSize.width(context,110),
                padding: EdgeInsets.all(8.spMin),
                margin: EdgeInsets.all(4.spMin),
                decoration: BoxDecoration(
                  border: Border.all(color: flashColor.value, width: 0.5.spMin),
                  borderRadius: BorderRadius.circular(8.r),
                  // color: flashColor.value,
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    image != null
                        ? Image.asset(
                            image!,
                            height: ResponsiveSize.height(context,24),
                            width: ResponsiveSize.width(context,24),
                            color: primaryColor,
                          )
                        : Icon(icon, color: flashColor.value),
                    Center(
                        child: Text(label,
                            textAlign: TextAlign.center,
                            style: TextStyle(color: flashColor.value)))
                  ],
                ),
              ),
            );
          })),
    );
  }
}

class GroupedTransactionsList extends StatelessWidget {
  final List<TransactionModel> transactions;

  const GroupedTransactionsList({super.key, required this.transactions});

  @override
  Widget build(BuildContext context) {
    // Sort transactions by date
    final sortedTransactions = List<TransactionModel>.from(transactions)
      ..sort((a, b) => b.createdAt!.compareTo(a.createdAt!));

    // Group transactions by date
    final groupedTransactions = <String, List<TransactionModel>>{};
    for (var transaction in sortedTransactions) {
      final dateStr = DateFormat('dd MMMM yyyy').format(transaction.createdAt!);
      groupedTransactions.putIfAbsent(dateStr, () => []).add(transaction);
    }

    return ListView.builder(
      itemCount: groupedTransactions.length,
      shrinkWrap: true,
      padding: const EdgeInsets.all(0),
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        final date = groupedTransactions.keys.elementAt(index);
        final transactionsForDate = groupedTransactions[date]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 16.0, top: 8.0, right: 16.0),
              child: Text(
                date,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            ListView.builder(
              itemCount: transactionsForDate.length,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemBuilder: (context, index) {
                final transaction = transactionsForDate[index];
                return Column(
                  children: [
                    ListTile(
                      onTap: () {
                        showDialog(
                          context: context,
                          builder: (context) => AlertDialog(
                            contentPadding: EdgeInsets.zero,
                            content: SingleChildScrollView(
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      color: primaryColor.withOpacity(0.1),
                                      borderRadius: const BorderRadius.only(
                                        topLeft: Radius.circular(8),
                                        topRight: Radius.circular(8),
                                      ),
                                    ),
                                    child: Row(
                                      children: [
                                        CircleAvatar(
                                          backgroundColor: primaryColor,
                                          child: Text(
                                            transaction.firstName?[0] ?? '',
                                            style: const TextStyle(
                                                color: Colors.white),
                                          ),
                                        ),
                                        const SizedBox(width: 12),
                                        Expanded(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                '${transaction.firstName ?? ""} ${transaction.secondName ?? ""}',
                                                style: const TextStyle(
                                                  fontSize: 16,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                              Text(
                                                transaction.phoneNumber ?? '',
                                                style: TextStyle(
                                                  color: Colors.grey.shade700,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.all(16),
                                    child: Builder(builder: (context) {
                                      Widget _buildDetailRow(
                                          String label, String value) {
                                        return Padding(
                                          padding: const EdgeInsets.symmetric(
                                              vertical: 8.0),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                label,
                                                style: TextStyle(
                                                  color: Colors.grey.shade600,
                                                ),
                                              ),
                                              Text(
                                                value,
                                                style: const TextStyle(
                                                  fontWeight: FontWeight.w500,
                                                ),
                                              ),
                                            ],
                                          ),
                                        );
                                      }

                                      return Column(
                                        children: [
                                          _buildDetailRow(
                                              'Transaction ID',
                                              transaction.transactionCode ??
                                                  ''),
                                          _buildDetailRow(
                                              'Amount',
                                              FormattedCurrency()
                                                  .getFormattedCurrency(
                                                      transaction.amount)),
                                          _buildDetailRow(
                                              'Date',
                                              DateFormat('dd MMM yyyy, hh:mm a')
                                                  .format(transaction.createdAt!
                                                      .toLocal())),
                                          _buildDetailRow('Status',
                                              transaction.status ?? ''),
                                          if (transaction.channelCode != null)
                                            _buildDetailRow(
                                                'Payment Method',
                                                transaction.channelCode != null
                                                    ? Get.find<
                                                            GlobalControllers>()
                                                        .paymentChannels
                                                        .where((e) =>
                                                            e.channelCode ==
                                                            int.tryParse(
                                                                transaction
                                                                    .channelCode
                                                                    .toString()))
                                                        .first
                                                        .name
                                                    : ""),
                                        ],
                                      );
                                    }),
                                  ),
                                ],
                              ),
                            ),
                            actions: [
                              TextButton(
                                onPressed: () => Navigator.pop(context),
                                child: const Text('Close'),
                              ),
                            ],
                          ),
                        );
                      },
                      title: Text(
                        (transaction.firstName ?? "") +
                            (transaction.secondName ?? ''),
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      subtitle: Text(
                        '${transaction.transactionCode ?? transaction.phoneNumber}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade700,
                        ),
                      ),
                      trailing: Text.rich(
                        textAlign: TextAlign.end,
                        TextSpan(
                          children: [
                            TextSpan(
                              text:
                                  '${FormattedCurrency().getFormattedCurrency(transaction.amount)}\n',
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 16,
                                color: Colors.green,
                              ),
                            ),
                            TextSpan(
                              text: DateFormat.jm()
                                  .format(transaction.createdAt!.toLocal()),
                            ),
                          ],
                        ),
                      ),
                      leading: CircleAvatar(
                        backgroundColor: primaryColor,
                        child: Text(
                          transaction.firstName?[0] ?? '',
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 15.0),
                      child: Divider(
                        height: 1,
                        color: Colors.grey.shade300,
                      ),
                    ),
                  ],
                );
              },
            ),
          ],
        );
      },
    );
  }
}

class InviteUser extends StatelessWidget {
  final List<Ticket> ticket;
  final int eventId;
  const InviteUser({super.key, required this.ticket, required this.eventId});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(ViewEventController());
    final cont = false.obs;

    return Dialog(
      child: Builder(
        builder: (context) {
          return Obx(
            () => AnimatedContainer(
              duration: const Duration(milliseconds: 500),
              height: ticket.isEmpty
                  ? ResponsiveSize.height(context,250)
                  : cont.value
                      ? ResponsiveSize.height(context,340)
                      : ResponsiveSize.height(context,440),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  Text(cont.value ? 'enter user details' : 'Pick a Ticket:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 20.spMin,
                      )),
                  if (ticket.isEmpty)
                    SizedBox(
                      height: ResponsiveSize.height(context,290),
                      child: const Center(
                        child: Text('No Tickets Available at the moment'),
                      ),
                    ),
                  if (ticket.isNotEmpty)
                    Expanded(
                      child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: ticket.length,
                        itemBuilder: (context, index) {
                          final Ticket tickets = ticket[index];
                          if (tickets.ticketType != "RESERVE") {
                            return const SizedBox();
                          }
                          return Obx(
                            () => GestureDetector(
                              onTap: () {
                                controller.selectedTicket.value = index;
                              },
                              child: Container(
                                padding: const EdgeInsets.all(8),
                                margin: EdgeInsets.all(8.spMin),
                                decoration: BoxDecoration(
                                  border: Border.all(
                                      color: controller.selectedTicket.value ==
                                              index
                                          ? AppColors.primary
                                          : Colors.grey,
                                      width: 1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(tickets.title ?? '',
                                        style: const TextStyle(
                                            fontWeight: FontWeight.bold)),
                                    Text(tickets.ticketType ?? ''),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  const SizedBox(height: 10),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Obx(
                      () => MyButton(
                        showLoading: controller.isInviting.value,
                        icon: cont.value ? null : Icons.navigate_next,
                        label: cont.value ? 'Invite' : 'Continue',
                        onClick: () {
                          Get.to(() => InviteUsersPage(
                                ticketId:
                                    ticket[controller.selectedTicket.value].id!,
                                eventId: eventId,
                              ));
                        },
                      ),
                    ),
                  )
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

Color getEventStatusColor(String status) {
  switch (status.toLowerCase()) {
    case "active":
      return const Color(0xFF56AF57);
    case "ended":
      return AppColors.greyTextColor;
    case "pending review":
      return Colors.amber;
    case "blocked":
      return const Color(0xFFEE5B60);

    default:
      return const Color(0xFFEE5B60);
  }
}
