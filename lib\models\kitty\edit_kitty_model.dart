// class EditKittyModel {
//     Kitty kitty;
//     String url;

//     EditKittyModel({
//         required this.kitty,
//         required this.url,
//     });

// }

// class Kitty {
//     int id;
//     DateTime createdAt;
//     DateTime updatedAt;
//     dynamic deletedAt;
//     String title;
//     String description;
//     String beneficiaryAccount;
//     String beneficiaryChannel;
//     String beneficiaryPhoneNumber;
//     DateTime endDate;
//     int limit;
//     dynamic refererMerchantCode;
//     String phoneNumber;

//     Kitty({
//         required this.id,
//         required this.createdAt,
//         required this.updatedAt,
//         required this.deletedAt,
//         required this.title,
//         required this.description,
//         required this.beneficiaryAccount,
//         required this.beneficiaryChannel,
//         required this.beneficiaryPhoneNumber,
//         required this.endDate,
//         required this.limit,
//         required this.refererMerchantCode,
//         required this.phoneNumber,
//     });

// }



class EditKittyModel {
  Kitty? kitty;
  String? url;

  EditKittyModel({this.kitty, this.url});

  EditKittyModel.fromJson(Map<String, dynamic> json) {
    kitty = json['kitty'] != null ? Kitty.fromJson(json['kitty']) : null;
    url = json['url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (kitty != null) {
      data['kitty'] = kitty!.toJson();
    }
    data['url'] = url;
    return data;
  }
}

class Kitty {
  int? iD;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? title;
  String? description;
  String? beneficiaryAccount;
  String? beneficiaryChannel;
  String? beneficiaryPhoneNumber;
  String? endDate;
  int? limit;
  String? refererMerchantCode;
  String? phoneNumber;

  Kitty(
      {this.iD,
      this.createdAt,
      this.updatedAt,
      this.deletedAt,
      this.title,
      this.description,
      this.beneficiaryAccount,
      this.beneficiaryChannel,
      this.beneficiaryPhoneNumber,
      this.endDate,
      this.limit,
      this.refererMerchantCode,
      this.phoneNumber});

  Kitty.fromJson(Map<String, dynamic> json) {
    iD = json['ID'];
    createdAt = json['CreatedAt'];
    updatedAt = json['UpdatedAt'];
    deletedAt = json['DeletedAt'];
    title = json['title'];
    description = json['description'];
    beneficiaryAccount = json['beneficiary_account'];
    beneficiaryChannel = json['beneficiary_channel'];
    beneficiaryPhoneNumber = json['beneficiary_phone_number'];
    endDate = json['end_date'];
    limit = json['limit'];
    refererMerchantCode = json['referer_merchant_code'];
    phoneNumber = json['phone_number'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['ID'] = iD;
    data['CreatedAt'] = createdAt;
    data['UpdatedAt'] = updatedAt;
    data['DeletedAt'] = deletedAt;
    data['title'] = title;
    data['description'] = description;
    data['beneficiary_account'] = beneficiaryAccount;
    data['beneficiary_channel'] = beneficiaryChannel;
    data['beneficiary_phone_number'] = beneficiaryPhoneNumber;
    data['end_date'] = endDate;
    data['limit'] = limit;
    data['referer_merchant_code'] = refererMerchantCode;
    data['phone_number'] = phoneNumber;
    return data;
  }
}

