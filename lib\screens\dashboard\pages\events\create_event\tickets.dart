import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/events/controllers.dart';
import 'package:onekitty/controllers/events/create_event_controller.dart';
import 'package:onekitty/utils/my_button.dart';
import 'package:onekitty/utils/my_text_field.dart';
import 'package:onekitty/utils/show_snackbar.dart';

class TicketsModel {
  final String ticketType, description, slotType;
  final DateTime purchaseDate, purchaseEndDate;
  final int price;
  final int? groupSize;
  final int? slotLength;
  const TicketsModel(
      {required this.ticketType,
      required this.description,
      required this.slotType,
      required this.purchaseDate,
      required this.purchaseEndDate,
      required this.price,
      this.slotLength,
      this.groupSize});
}

class Tickets extends StatelessWidget {
  const Tickets({super.key});

  @override
  Widget build(BuildContext context) {
    final GlobalKey<FormState> formKey = GlobalKey();
    final group = false.obs;
    final TextEditingController ticketType = TextEditingController(),
        ticketDescription = TextEditingController(),
        price = TextEditingController(),
        purchaseStartDate = TextEditingController(),
        purchaseEndDate = TextEditingController(),
        slotsAvailable = TextEditingController(),
        groupSizeController = TextEditingController();

    return GetX<CreateEventController>(builder: (controller) {
      return ListView.builder(
          shrinkWrap: true,
          itemCount: controller.tickets.length + 1,
          itemBuilder: (context, index) {
            if (index == controller.tickets.length) {
              return Column(
                children: [
                  SizedBox(height: 8.h),
                  if (controller.tickets.isNotEmpty) const Divider(),
                  SizedBox(height: 20.h),
                  Form(
                    key: formKey,
                    child: SingleChildScrollView(
                      child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: MyDropdownMenu(
                                    titleStyle: TextStyle(
                                        fontSize: 14.spMin,
                                        fontWeight: FontWeight.w500),
                                    title: 'Ticket Type',
                                    onSelected: (val) {
                                      if (val == "GROUP") {
                                        group(true);
                                      } else {
                                        group(false);
                                      }
                                    },
                                    lists: Get.put(GlobalControllers())
                                        .enums
                                        .value
                                        .ticketType,
                                    controller: ticketType,
                                  ),
                                ),
                                Obx(() => group.value
                                    ? Padding(
                                        padding: const EdgeInsets.all(8.0),
                                        child: Column(
                                          children: [
                                            Text(
                                              'Group Size',
                                              style: TextStyle(
                                                  fontSize: 14.spMin,
                                                  fontWeight: FontWeight.w600),
                                            ),
                                            SizedBox(
                                              width: 120,
                                              child: Expanded(
                                                child: TextField(
                                                  keyboardType:
                                                      TextInputType.number,
                                                  controller:
                                                      groupSizeController,
                                                  decoration: const InputDecoration(
                                                      label: Text('Size'),
                                                      border:
                                                          OutlineInputBorder()),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      )
                                    : const SizedBox())
                              ],
                            ),
                            SizedBox(height: 8.h),
                            MyTextFieldwValidator(
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Ticket description is required';
                                  }
                                  return null;
                                },
                                controller: ticketDescription,
                                titleStyle: TextStyle(
                                    fontSize: 14.spMin,
                                    fontWeight: FontWeight.w500),
                                title: 'Ticket description',
                                hint: 'e.g. For Club member'),
                            SizedBox(height: 8.h),
                            Text(
                              'slots available',
                              style: TextStyle(
                                  fontSize: 14.spMin,
                                  fontWeight: FontWeight.w600),
                            ),
                            Obx(() => Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Expanded(
                                      child: Column(
                                        children: [
                                          TextButton.icon(
                                              onPressed: () {
                                                controller.slotType.value =
                                                    0; // Update the value directly
                                              },
                                              label:
                                                  const Text('Limited slots'),
                                              icon: Icon(controller
                                                          .slotType.value ==
                                                      0
                                                  ? Icons.radio_button_checked
                                                  : Icons.radio_button_off)),
                                          if (controller.slotType.value == 0)
                                            MyTextFieldwValidator(
                                                keyboardType:
                                                    TextInputType.number,
                                                validator: (value) {
                                                  if (value == null ||
                                                      value.isEmpty) {
                                                    return 'Slots is required';
                                                  }
                                                  return null;
                                                },
                                                controller: slotsAvailable,
                                                titleStyle: const TextStyle(
                                                    fontSize: 14,
                                                    fontWeight:
                                                        FontWeight.w500),
                                                title: 'Slots Available',
                                                hint: 'eg. 100')
                                        ],
                                      ),
                                    ),
                                    Expanded(
                                      child: TextButton.icon(
                                          onPressed: () {
                                            controller.slotType.value =
                                                1; // Update the value directly
                                          },
                                          label: const Text('Unlimited slots'),
                                          icon: Icon(
                                              controller.slotType.value == 1
                                                  ? Icons.radio_button_checked
                                                  : Icons.radio_button_off)),
                                    )
                                  ],
                                )),
                            MyTextFieldwValidator(
                                keyboardType: TextInputType.number,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Price is required';
                                  }
                                  return null;
                                },
                                controller: price,
                                titleStyle: const TextStyle(
                                    fontSize: 14, fontWeight: FontWeight.w500),
                                title: 'Price',
                                hint: '500'),
                            SizedBox(height: 8.h),
                            MyTextFieldwValidator(
                                readOnly: true,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Purchase Start Date and Time is required';
                                  }
                                  return null;
                                },
                                controller: purchaseStartDate,
                                titleStyle: TextStyle(
                                    fontSize: 14.spMin,
                                    fontWeight: FontWeight.w500),
                                iconSuffix: IconButton(
                                  icon: const Icon(Icons.calendar_month),
                                  onPressed: () async {
                                    DateTime? pickedDateTime =
                                        await showDatePicker(
                                      context: context,
                                      initialDate: DateTime.now(),
                                      firstDate: DateTime.now(),
                                      lastDate: DateTime.now()
                                          .add(const Duration(days: 365)),
                                    );

                                    if (pickedDateTime != null) {
                                      TimeOfDay? pickedTime =
                                          await showTimePicker(
                                        context: context,
                                        initialTime: TimeOfDay.now(),
                                      );

                                      if (pickedTime != null) {
                                        DateTime finalDateTime = DateTime(
                                          pickedDateTime.year,
                                          pickedDateTime.month,
                                          pickedDateTime.day,
                                          pickedTime.hour,
                                          pickedTime.minute,
                                        );

                                        String formattedDateTime =
                                            DateFormat('dd/MM/yyyy HH:mm')
                                                .format(finalDateTime);
                                        purchaseStartDate.text =
                                            formattedDateTime;
                                      }
                                    }
                                  },
                                ),
                                title: 'Purchase Start Date and Time',
                                hint: '13/2/2024 14:00'),
                            SizedBox(width: 18.w),
                            MyTextFieldwValidator(
                              readOnly: true,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Purchase End Date is required';
                                }
                                return null;
                              },
                              controller: purchaseEndDate,
                              titleStyle: TextStyle(
                                  fontSize: 14.spMin,
                                  fontWeight: FontWeight.w500),
                              iconSuffix: IconButton(
                                icon: const Icon(Icons.calendar_month),
                                onPressed: () async {
                                  DateTime? pickedDateTime =
                                      await showDatePicker(
                                    context: context,
                                    initialDate: DateTime.now(),
                                    firstDate: DateTime.now(),
                                    lastDate: DateTime.now()
                                        .add(const Duration(days: 365)),
                                  );

                                  if (pickedDateTime != null) {
                                    TimeOfDay? pickedTime =
                                        await showTimePicker(
                                      context: context,
                                      initialTime: TimeOfDay.now(),
                                    );

                                    if (pickedTime != null) {
                                      DateTime finalDateTime = DateTime(
                                        pickedDateTime.year,
                                        pickedDateTime.month,
                                        pickedDateTime.day,
                                        pickedTime.hour,
                                        pickedTime.minute,
                                      );

                                      String formattedDateTime =
                                          DateFormat('dd/MM/yyyy HH:mm')
                                              .format(finalDateTime);
                                      purchaseEndDate.text = formattedDateTime;
                                    }
                                  }
                                },
                              ),
                              hint: '13/2/2024 14:00',
                              title: 'Purchase End Date and Time',
                            ),
                            SizedBox(height: 18.h),
                            Align(
                              alignment: Alignment.center,
                              child: MyButton(
                                icon: Icons.add,
                                onClick: () {
                                  if (ticketType.text == "") {
                                    showSnackbar(
                                        context: context,
                                        label:
                                            'Must Pick a Ticket type to proceed');
                                    return;
                                  }
                                  if (formKey.currentState?.validate() ??
                                      false) {
                                    controller.tickets.add(
                                      TicketsModel(
                                          groupSize: group.value
                                              ? int.tryParse(
                                                  groupSizeController.text)
                                              : null,
                                          ticketType: ticketType.text,
                                          description: ticketDescription.text,
                                          slotType:
                                              controller.slotType.toInt() == 0
                                                  ? "Limited"
                                                  : "Unlimited",
                                          purchaseDate: DateFormat('dd/MM/yyyy')
                                              .parse(purchaseStartDate.text),
                                          purchaseEndDate:
                                              DateFormat('dd/MM/yyyy')
                                                  .parse(purchaseEndDate.text),
                                          slotLength: int.parse(
                                              slotsAvailable.text == ""
                                                  ? "0"
                                                  : slotsAvailable.text),
                                          price: int.parse(price.text)),
                                    );
                                    ticketType.clear();
                                    ticketDescription.clear();
                                    controller.slotType.value = 1;
                                    price.clear();
                                    purchaseStartDate.clear();
                                    slotsAvailable.clear();
                                    purchaseEndDate.clear();
                                  }
                                },
                                label: 'Add Ticket',
                              ),
                            )
                          ]),
                    ),
                  ),
                ],
              );
            }

            return Card(
                child: ExpansionTile(
              onExpansionChanged: (expanded) {
                if (expanded) {
                  ticketType.text = controller.tickets[index].ticketType;
                  ticketDescription.text =
                      controller.tickets[index].description;
                  price.text = controller.tickets[index].price.toString();
                  purchaseStartDate.text = DateFormat('dd/MM/yyyy')
                      .format(controller.tickets[index].purchaseDate);
                  purchaseEndDate.text = DateFormat('dd/MM/yyyy')
                      .format(controller.tickets[index].purchaseEndDate);
                  slotsAvailable.text =
                      controller.tickets[index].slotLength.toString();
                  groupSizeController.text =
                      controller.tickets[index].groupSize?.toString() ?? "0";
                  controller.slotType.value =
                      controller.tickets[index].slotType == "Limited" ? 0 : 1;
                }
              },
              leading: CircleAvatar(
                backgroundColor: primaryColor,
                radius: 15,
                child: Text('${index + 1}',
                    style: const TextStyle(color: Colors.white)),
              ),
              title: Text(controller.tickets[index].ticketType),
              subtitle: Text(controller.tickets[index].description),
              trailing: IconButton(
                  icon: const Icon(
                    Icons.delete,
                    color: Colors.red,
                  ),
                  onPressed: () {
                    showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return AlertDialog(
                          title: const Text('Confirm Deletion'),
                          content: const Text(
                              'Are you sure you want to delete this ticket?'),
                          actions: [
                            TextButton(
                              child: const Text('Cancel'),
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                            ),
                            TextButton(
                              child: const Text('Delete',
                                  style: TextStyle(color: Colors.red)),
                              onPressed: () {
                                controller.tickets.removeAt(index);
                                Navigator.of(context).pop();
                              },
                            ),
                          ],
                        );
                      },
                    );
                  }),
              children: [
                Row(
                  children: [
                    Expanded(
                      child: MyDropdownMenu(
                        title: 'Ticket Type',
                        onSelected: (val) {
                          if (val == "GROUP") {
                            group(true);
                          } else {
                            group(false);
                          }
                        },
                        lists:
                            Get.put(GlobalControllers()).enums.value.ticketType,
                        controller: ticketType,
                      ),
                    ),
                    Obx(() => group.value
                        ? Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Column(
                              children: [
                                Text(
                                  'Group Size',
                                  style: TextStyle(
                                      fontSize: 14.spMin,
                                      fontWeight: FontWeight.w600),
                                ),
                                SizedBox(
                                  width: 120,
                                  child: Expanded(
                                    child: TextField(
                                      keyboardType: TextInputType.number,
                                      controller: groupSizeController,
                                      decoration: const InputDecoration(
                                          label: Text('Size'),
                                          border: OutlineInputBorder()),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          )
                        : const SizedBox())
                  ],
                ),
                SizedBox(height: 8.h),
                MyTextFieldwValidator(
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Ticket description is required';
                      }
                      return null;
                    },
                    controller: ticketDescription,
                    titleStyle: TextStyle(
                        fontSize: 14.spMin, fontWeight: FontWeight.w500),
                    title: 'Ticket description',
                    hint: 'e.g. For Club member'),
                SizedBox(height: 8.h),
                Text(
                  'slots available',
                  style: TextStyle(
                      fontSize: 14.spMin, fontWeight: FontWeight.w600),
                ),
                Obx(() => Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Column(
                            children: [
                              TextButton.icon(
                                  onPressed: () {
                                    controller.slotType.value =
                                        0; // Update the value directly
                                  },
                                  label: const Text('Limited slots'),
                                  icon: Icon(controller.slotType.value == 0
                                      ? Icons.radio_button_checked
                                      : Icons.radio_button_off)),
                              if (controller.slotType.value == 0)
                                MyTextFieldwValidator(
                                    keyboardType: TextInputType.number,
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Slots is required';
                                      }
                                      return null;
                                    },
                                    controller: slotsAvailable,
                                    titleStyle: const TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500),
                                    title: 'Slots Available',
                                    hint: 'eg. 100')
                            ],
                          ),
                        ),
                        Expanded(
                          child: TextButton.icon(
                              onPressed: () {
                                controller.slotType.value =
                                    1; // Update the value directly
                              },
                              label: const Text('Unlimited slots'),
                              icon: Icon(controller.slotType.value == 1
                                  ? Icons.radio_button_checked
                                  : Icons.radio_button_off)),
                        )
                      ],
                    )),
                MyTextFieldwValidator(
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Price is required';
                      }
                      return null;
                    },
                    controller: price,
                    titleStyle: const TextStyle(
                        fontSize: 14, fontWeight: FontWeight.w500),
                    title: 'Price',
                    hint: '500'),
                SizedBox(height: 8.h),
                Row(
                  children: [
                    Expanded(
                        child: MyTextFieldwValidator(
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Purchase Start Date is required';
                              }
                              return null;
                            },
                            onTap: () async {
                              DateTime? pickedDate = await showDatePicker(
                                context: context,
                                initialDate: DateTime.now(),
                                firstDate: DateTime.now(),
                                lastDate: DateTime.now()
                                    .add(const Duration(days: 365)),
                              );

                              if (pickedDate != null) {
                                String formattedDate =
                                    DateFormat('dd/MM/yyyy').format(pickedDate);
                                purchaseStartDate.text = formattedDate;
                              }
                            },
                            controller: purchaseStartDate,
                            titleStyle: TextStyle(
                                fontSize: 14.spMin,
                                fontWeight: FontWeight.w500),
                            iconSuffix: IconButton(
                              icon: const Icon(Icons.calendar_month),
                              onPressed: () async {
                                DateTime? pickedDate = await showDatePicker(
                                  context: context,
                                  initialDate: DateTime.now(),
                                  firstDate: DateTime.now(),
                                  lastDate: DateTime.now()
                                      .add(const Duration(days: 365)),
                                );

                                if (pickedDate != null) {
                                  String formattedDate =
                                      DateFormat('dd/MM/yyyy')
                                          .format(pickedDate);
                                  purchaseStartDate.text = formattedDate;
                                }
                              },
                            ),
                            title: 'Purchase Start Date',
                            hint: '13/2/2024')),
                    SizedBox(width: 18.w),
                    Expanded(
                        child: MyTextFieldwValidator(
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Purchase End Date is required';
                        }
                        return null;
                      },
                      controller: purchaseEndDate,
                      onTap: () async {
                        DateTime? pickedDate = await showDatePicker(
                          context: context,
                          initialDate: DateTime.now(),
                          firstDate: DateTime.now(),
                          lastDate:
                              DateTime.now().add(const Duration(days: 365)),
                        );

                        if (pickedDate != null) {
                          String formattedDate =
                              DateFormat('dd/MM/yyyy').format(pickedDate);
                          purchaseEndDate.text = formattedDate;
                        }
                      },
                      titleStyle: TextStyle(
                          fontSize: 14.spMin, fontWeight: FontWeight.w500),
                      iconSuffix: IconButton(
                        icon: const Icon(Icons.calendar_month),
                        onPressed: () async {
                          DateTime? pickedDate = await showDatePicker(
                            context: context,
                            initialDate: DateTime.now(),
                            firstDate: DateTime.now(),
                            lastDate:
                                DateTime.now().add(const Duration(days: 365)),
                          );

                          if (pickedDate != null) {
                            String formattedDate =
                                DateFormat('dd/MM/yyyy').format(pickedDate);
                            purchaseEndDate.text = formattedDate;
                          }
                        },
                      ),
                      hint: '13/2/2024',
                      title: 'Purchase End Date',
                    )),
                  ],
                ),
                SizedBox(height: 18.h),
                Align(
                  alignment: Alignment.center,
                  child: OutlinedButton.icon(
                    icon: const Icon(Icons.edit),
                    onPressed: () {
                      if (ticketType.text == "") {
                        showSnackbar(
                            context: context,
                            label: 'Must Pick a Ticket type to proceed');
                        return;
                      }
                      if (formKey.currentState?.validate() ?? false) {
                        controller.tickets[index] = TicketsModel(
                            ticketType: ticketType.text,
                            description: ticketDescription.text,
                            slotType: controller.slotType.toInt() == 0
                                ? "Limited"
                                : "Unlimited",
                            purchaseDate: DateFormat('dd/MM/yyyy')
                                .parse(purchaseStartDate.text),
                            purchaseEndDate: DateFormat('dd/MM/yyyy')
                                .parse(purchaseEndDate.text),
                            slotLength: int.parse(slotsAvailable.text == ""
                                ? "0"
                                : slotsAvailable.text),
                            price: int.parse(price.text));
                      }
                    },
                    label: const Text('Edit Ticket'),
                  ),
                )
              ],
            ));
          });
    });
  }
}
