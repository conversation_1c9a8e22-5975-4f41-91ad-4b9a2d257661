import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty/admin_screens/chama/widgets/table_footer.dart';
import 'package:onekitty/admin_screens/events/events_table.dart';
import 'package:onekitty/admin_screens/settings/pages/getAllEvents.dart';
import 'package:onekitty/controllers/admin/events/events_admin_controller.dart';
import 'widgets/events_filter_widget.dart';

class EventsAdmin extends StatefulWidget {
  const EventsAdmin({super.key});

  @override
  State<EventsAdmin> createState() => _EventsAdminState();
}

class _EventsAdminState extends State<EventsAdmin> {
  int? _sortColumnIndex;
  bool _sortAscending = true;
  final controller = Get.put(EventsAdminController());

  @override
  void initState() {
    super.initState();
    controller.getEvents(0);
  }

  // void _sort<T>(Comparable<T> Function(Event event) getField, int columnIndex,
  //     bool ascending) {
  //   setState(() {
  //     _sortColumnIndex = columnIndex;
  //     _sortAscending = ascending;

  //     controller.events.sort((a, b) {
  //       final aValue = getField(a);
  //       final bValue = getField(b);
  //       return ascending
  //           ? Comparable.compare(aValue, bValue)
  //           : Comparable.compare(bValue, aValue);
  //     });
  //   });
  // }

  void _onRowsPerPageChanged(int value) {
    controller.size.value = value;

    controller.getEvents(0);
  }

  void _onRefresh() {
    controller.events.clear();
    controller.getEvents(0);
  }

  void _onPreviousPage() {
    if (!controller.isFirst.value) {
      controller.getEvents(controller.currentPage.value - 1);
    }
  }

  void _onNextPage() {
    if (!controller.isLast.value) {
      controller.getEvents(controller.currentPage.value + 1);
    }
  }

  final showAll = false.obs;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const EventsFilterWidget(),
         Align( 
          alignment: Alignment.centerRight,
           child: Obx(
             ()=> showAll.value ?  IconButton(onPressed: (){
                    showAll.value = !showAll.value;
                  }, icon: const Icon(Icons.table_chart_outlined)) : IconButton(onPressed: (){
                    showAll.value = !showAll.value;
                  }, icon: const Icon(Icons.list_alt_outlined)),
           ),
         ),
           
        Expanded(
            child: Obx(
          () => showAll.value
              ? const AllEvents()
              : Obx(
                  () => controller.isLoading.value
                      ? const Center(child: CircularProgressIndicator())
                      : const EventTable(),
                ),
        )),
        Obx(
          () => TablePaginationFooter(
            currentPage: controller.currentPage.value,
            totalPages: controller.totalPages.value,
            rowsPerPage: controller.size.value,
            availableRowsPerPage: const [15, 30, 50, 100],
            onRowsPerPageChanged: _onRowsPerPageChanged,
            onRefresh: _onRefresh,
            onPreviousPage: _onPreviousPage,
            onNextPage: _onNextPage,
          ),
        ),
      ],
    );
  }
}
