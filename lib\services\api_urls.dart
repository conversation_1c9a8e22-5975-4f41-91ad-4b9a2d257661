/// ignore_for_file: non_constant_identifier_names

class ApiUrls {
  static  String BASE_URL_LOCAL = "https://b945-41-209-60-66.ngrok-free.app/";
  static  String BASE_URL_DEV = "https://devsalticon.onekitty.co.ke/";
  static  String BASE_URL_LIVE = "https://apisalticon.onekitty.co.ke/";

  /*#########################~ADMIN API URLS~###########################*/
  static final String getAllChamasAdmin = 'chama/admin/chamas/';
  static final String addGeneralPenalty = 'chama/admin/add-general-penalty/';
  static final String getInvoices = '/chama/chama/invoices/';
  static final String sendfundstobeneficiary =
      '/chama/admin/send-funds-beneficiaries/';
  static final String getChamaOccurence = 'chama/chama/chama-occurrence/';
  static final String addBeneficiaryAccount = 'chama/chama/add-beneficiary-account/';
  static final String editEventAdmin = "/tickets/event/edit-event/";
  static final String blockEvent = "/tickets/admin/event-status/";
  static final String getAllKitties = "admin/get-kitties/";
  static final String getReconciliationRecords ="/admin/reconciliations/";
  /*###################################################################*/

  static final String create_kitty = "kitty/create_kitty/";
  static final String create_payment_kitty = "pay/create_payment/";
  static final String pay_payment_kitty = "pay/pay_kitty/";
  static final String charges = "pay/pay_charges/";

  static final String update_kitty = "kitty/update_kitty/";
  
  static String deactivateKitty = 'kitty/update-kitty-status/';
  static final String updateEndDate = "kitty/update_end_date/";
  static final String join_group = "kitty/join_whatsapp/";
  static final String contribute_kitty = "kitty/contribute_kitty/";
  static final String get_kitty = "kitty/get_kitty_all/";
  static final String contrProcess = "kitty/process_contribute_kitty/";
  static final String contribsTransactions = "kitty/transactions/";
  static final String filterContribs = "kitty/filter-kitty-transactions/";
  static final String confirmPay = "kitty/check-transaction-status/";
  static final String notifications = "kitty/get-notification-accounts/?kitty_id=";
  static final String toggleWhatsapp = "kitty/update-whatsapp-status/";
  static final String getKittyText = "kitty/get-transactions-text/";
  static final String fetchBalance = "kitty/charges/";
  static final String fetchBeneficiaries = 'kitty/beneficiaries/';
  static final String beneficiary = 'kitty/beneficiary/';
  static final String create_kitty_media = "kitty/media/";
  static final String kitty_settings = 'kitty/get-kitty-settings/';
  static final String update_kitty_settings = 'kitty/update-kitty-settings/';
  static final String remove_kitty_whatsapp = 'kitty/notification/';
  /*
   thank you message ?=> mimi natumiwa messages , thank you for contributing
  group by account -> 
  max - no one sends more than 1000, 
  min - 
  goal - amount 
  delete - a kitty
  search 
  delete bot 
  outlines events 
  */
  //AUTH
  static final String register = "auth/register/";
  static final String otpConfirm = "auth/confirm_user_otp/";
  static final String setPin = "auth/user_set_pin/";
  static final String forgotPass = "auth/forgot_password/";
  static final String login = "admin/login/";
  static final String getPaymentChannels = "auth/payment-channels/";

  //USER
  static final String getUserStats = "user/user_stats/";
  static final String getUserKitties = "user/user_kitties/";
  static final String getUserTransactions = "kitty/get_kitty_contribs/";
  static final String getUserAllTransactions = "user/user-transactions/";
  static final String searchTransactionQr = "admin/search-verify-transaction/";
  static final String verifyTransactionConfirm = "admin/verify-transaction-confirm/";
  static final String topUp = "user/top-up/";
  static final String getUser = "user/get-user-phone/";

  static final String withdrawRequest = "user/withdraw_request/";
  static final String withdrawConfirm = "user/withdraw_confirm/";
  static final String config = "user/get-configs-keys/";
  static final String updateProfile = "user/update-user/";
  static final String getReferKitties = "merchant/kitties/";
  static final String getMerTransac = "merchant/transactions/";
  static final String getCode = "merchant/self-register/";

  //bulk_sms
  static final String sendSmS = "user/send-bulk-sms/";
  static final String confirmSmS = "user/send-bulk-sms/confirm/";
  static final String getMsgs = "user/get-sms/";
  static final String getMsg = "user/get-filter-sms/";

  //Chama
  static final String getChamaMembers = "chama/chama/members/";
  static final String getUserChamas = "chama/chama/user-chama/";
  static final String getAllChamaDetails = "chama/chama/chama-details/";
  static final String editChamaSettings = "chama/chama/settings/";
  static final String getChamaSettings = "chama/chama/settings/";

  //Penalty
  static final String addPenalty = "chama/penalty/add-chama-penalty/";
  static final String getChamaPenalties = "chama/penalty/chama-penalties/";
  static final String updatePenalty = "chama/penalty/chama-penalty/";
  static final String penalizeMember = "chama/penalty/member-penalty/";
  static final String penalizeMultiple = "chama/penalty/member-penalty-multiple/";
  static final String getGeneralPenalties = "chama/penalty/get-general-penalties/";
  static final String deletePenalty = "chama/penalty/chama-penalty/";

  //SignatoryTransactions
  static final String transferReq = "chama/chama/transfer/";
  static final String transferConfirm = "chama/chama/transfer/confirm/";
  static final String getSigTra = "chama/chama/signatory-transactions/";
  static final String sigApproval = "chama/chama/process-signatory-transaction/";

  //Chama Transctions
  static final String getChamaTransactions = "chama/chama/transactions/";
  static final String postTransactionText = "chama/chama/contribution-message/";

  //signatories
  static final String addSignatory = "chama/chama/signatory/";
  static final String getSignatories = "chama/chama/signatory/";

  //meetings
  static final String addMeeting = "chama/chama/event/";
  static final String getMeeting = "chama/chama/event/";
  static final String updateMeeting = "chama/chama/event/";

  //chama Contributions
  static final String chamaContribute = "chama/chama/pay/";
  static final String chamaContrConfirm = "chama/chama/pay/confirm/";
  static final String checkMemberPenalties = "chama/chama/member-penalties/";

  //chama
  static final String getFreq = "chama/chama/get-enums-configs/";
  static final String create_chama = "chama/chama/create-chama/";
  static final String add_members = "chama/member/add-members/";
  static final String set_order = "chama/chama/set-receiving-order/";
  static final String add_resource = "chama/chama/resource/";
  static final String getResources = "chama/chama/resource/";
  static final String update_chama = "chama/chama/edit-chama/";
  static final String get_all = "chama/chama/chama-details/";
  static final String add_group = "chama/chama/notification/";
  static final String removeM = "chama/member/update-member-status/";
  static final String getbeneficiaries = "chama/chama/chama-details/";
  static final String update_benf_acc = "chama/member/update-beneficiary-account/";
  static final String update_resource = "chama/chama/resource/";
  static final String rm_resource = "chama/chama/resource/";
  static final String update_role = "chama/member/update-member-role/";
  static final String update_member = "chama/member/update-member-details/";
  static final String rm_whatsApp = "chama/chama/notification/";
  static final String toggle_whatsApp = "chama/chama/notification/";

  //events
  static final String GETCATEGORIES = "/tickets/admin/get-categories/";
  static final String GETALLEVENTS = "tickets/event/get-events/";
  static final String GETUSEREVENTS = "/tickets/event/get-events-user/";
  static final String CREATEEVENT = "/tickets/event/create-event/";
  static final String ADDSOCIALMEDIA = "/tickets/event/upload-images-social/";
  static final String ADDTICKETS = "/tickets/event/create-tickets/";
  static final String EDITTICKET = "/tickets/event/edit-ticket/";
  static final String GETEVENTBYID = "/tickets/event/get-event-id/";
  static final String EDITEVENT = "/tickets/event/edit-event/";
  static final String UPLOADFILE = "/tickets/media/upload-media/";
  static final String PURCHASETICKETS = "/tickets/event/purchase-tickets/";
  static final String GETEVENTSMEDIA = "/media/media/";
  static final String EVENTTRANSACTIONS = "/tickets/event/transactions/";
  static final String GETDELEGATES = "/kitty/delegates/";
  static final String ADDDELEGATES = "/kitty/delegate/";
  static final String VERIFYTICKET = '/tickets/event/verify-ticket/';
  static final String VERIFYTICKETCONFIRM = "tickets/event/verify-ticket/confirm/";
  static final String GETENUMS = '/tickets/event/enums/';
  static final String TRANSFERREQUEST = '/kitty/transfer/';
  static final String TRANSFERCONFIRM = '/kitty/transfer/confirm/';
  static final String SIGNATORYTRANSACTIONS = 'kitty/signatory-transactions/';
  static final String DELETEMEDIA = 'tickets/media/media/';
  static final String PROCESSSIGNATORYTRANSACTIONS =
      'kitty/process-signatory-transaction/';
  static final String RESERVETICKET = 'tickets/event/reserve-ticket/';
  static final String INVITEUSERS = 'tickets/event/invite-reserve-tickets-users/';
  static final String FETCHINVITEDUSERS = 'tickets/event/invited-reserve-users/';
}

enum Environmentcurrent { Live, development }

Environmentcurrent? environmentMode;
