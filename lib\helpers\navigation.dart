import 'package:get/get.dart';

class AppThemeGuard extends GetMiddleware {
//   AuthController authService = Get.find<AuthController>();
//   UserController userService = Get.find<UserController>();
// //   The default is 0 but you can update it to any number. Please ensure you match the priority based
// //   on the number of guards you have.
//   @override
//   int? get priority => 2;

//   @override
//   GetPage? onPageCalled(GetPage? page) {
//     getUser();
//     return super.onPageCalled(page);
//   }

//   getUser() async {
//     await userService.getUserProfile();
//   }

//   @override
//   RouteSettings? redirect(String? route) {
//     String accountStatus = authService.getUserModel().admin_validated;

//     int clientStatus = authService.getUserModel().clientStatus;
//     // Navigate to login if client is not authenticated other wise continue

//     if (kDebugMode) {
//       print("I am here $clientStatus + Account Status $accountStatus");
//     }

//     // if (clientStatus == 300) {
//     //   return const RouteSettings(
//     //     name: AppRoutes.HOME_SCREEN,
//     //   );
//     // } else {
//     //   return const RouteSettings(
//     //     name: AppRoutes.SUSPENDED_SCREEN,
//     //   );
//     // }
  // }
}
