# Events Admin Documentation

## Overview
The Events Admin module provides comprehensive administrative functionality for managing events in the OneKitty application. This documentation covers data fetching, table display, filtering, event management, blocking/unblocking, and detailed event views.

## 1. Events Admin Screen Structure

### Main Components
- **EventsAdmin** (`lib/admin_screens/events/events_admin.dart`)
- **EventsAdminController** (`lib/controllers/admin/events/events_admin_controller.dart`)
- **EventTable** (`lib/admin_screens/events/events_table.dart`)
- **EventsFilterWidget** (`lib/admin_screens/events/widgets/events_filter_widget.dart`)
- **BlockEventController** (`lib/controllers/admin/events/block_event_controller.dart`)

## 2. Data Fetching

### API Endpoints
- **Main Events**: `tickets/event/get-events/`
- **Event Transactions**: `tickets/event/transactions/`
- **Block Event**: `tickets/admin/event-status/`
- **Edit Event**: `tickets/event/edit-event/`

### Request Parameters
```
?page={page}&size={size}&search={search}&phone_number={phoneNumber}&start_date={startDate}&end_date={endDate}&kitty_id={kittyId}&frequency={frequency}
```

| Parameter | Type | Description |
|-----------|------|-------------|
| `page` | int | Page number for pagination (default: 0) |
| `size` | int | Number of items per page (default: 15) |
| `search` | string | Search term for event names |
| `phone_number` | string | Filter by organizer phone number |
| `kitty_id` | string | Filter by associated kitty ID |
| `frequency` | string | Filter by frequency |
| `start_date` | string | Filter by start date (ISO format) |
| `end_date` | string | Filter by end date (ISO format) |

### Controller Implementation
```dart
Future getEvents(int page) async {
  try {
    isLoading(true);
    var response = await apiProvider.request(
      method: Method.GET,
      url: "${ApiUrls.GETALLEVENTS}?page=$page&size=${size.value}&search=${search.value}&phone_number=${phoneNumber.value}&start_date=${startDate.value}&end_date=${endDate.value}&kitty_id=${kittyId.value}&frequency=${frequency.value}",
    );
    
    if (response.data['status'] ?? false) {
      events((response.data['data']['items'] as List)
          .map((e) => Event.fromJson(e))
          .toList());
      
      currentPage.value = response.data['data']['page'];
      maxPage.value = response.data['data']['total_pages'];
      totalPages.value = response.data['data']['total_pages'];
      isLast.value = response.data['data']['last'];
      isFirst.value = response.data['data']['first'];
    }
  } catch (e) {
    logger.e('Error fetching events: $e');
  } finally {
    isLoading(false);
  }
}
```

## 3. Table Display Values

### Data Grid Columns
The events table displays the following columns:

| Column | Data Source | Format | Description |
|--------|-------------|--------|-------------|
| **ID** | `event.id` | String | Event unique identifier |
| **Created At** | `event.createdAt` | `dd/MM/yy HH:mm` | Event creation timestamp |
| **Title** | `event.title` | String | Event name/title |
| **Username** | `event.username` | String | Event organizer username |
| **Kitty ID** | `event.kittyId` | String | Associated kitty identifier |
| **Email** | `event.email` | String | Organizer email address |
| **Phone Number** | `event.phoneNumber` | String | Organizer phone number |
| **Venue** | `event.venue` | String | Event location/venue |
| **Start Date** | `event.startDate` | `dd/MM/yy HH:mm` | Event start timestamp |
| **End Date** | `event.endDate` | `dd/MM/yy HH:mm` | Event end timestamp |
| **Status** | `event.status` | Color-coded | Event status with color coding |

### Data Source Implementation
```dart
class EventDataSource extends DataGridSource {
  EventDataSource({required List<Event> eventData}) {
    dataGridRows = eventData.map<DataGridRow>((event) => DataGridRow(cells: [
      DataGridCell<String>(columnName: 'id', value: event.id.toString()),
      DataGridCell<String>(columnName: 'created_at', 
          value: DateFormat('dd/MM/yy HH:mm').format(event.createdAt ?? DateTime.now())),
      DataGridCell<String>(columnName: 'title', value: event.title),
      DataGridCell<String>(columnName: 'username', value: event.username),
      DataGridCell<String>(columnName: 'kittyId', value: event.kittyId.toString()),
      DataGridCell<String>(columnName: 'email', value: event.email),
      DataGridCell<String>(columnName: 'phoneNumber', value: event.phoneNumber),
      DataGridCell<String>(columnName: 'venue', value: event.venue),
      DataGridCell<String>(columnName: 'startDate', 
          value: DateFormat('dd/MM/yy HH:mm').format(event.startDate ?? DateTime.now())),
      DataGridCell<String>(columnName: 'endDate', 
          value: DateFormat('dd/MM/yy HH:mm').format(event.endDate ?? DateTime.now())),
      DataGridCell<String>(columnName: 'status', value: event.status?.name.toString()),
    ])).toList();
  }
}
```

## 4. Filtering Functionality

### Filter Widget Components
The `EventsFilterWidget` provides the following filters:

#### Date Range Filters
- **Start Date**: Date picker for filtering from a specific date
- **End Date**: Date picker for filtering to a specific date
- Both dates are stored in ISO format

#### Search Filters
- **Kitty ID Filter**: Text input with 1-second debounce
- **Phone Number Filter**: Text input with 1-second debounce  
- **Search by Name**: Text input with 1-second debounce

### Filter Implementation
```dart
// Date selection
Future<void> _selectDate(BuildContext context, {required bool isStartDate}) async {
  final DateTime? picked = await showDatePicker(
    context: context,
    initialDate: DateTime.now(),
    firstDate: DateTime(2000),
    lastDate: isStartDate ? DateTime.now() : DateTime.now().add(const Duration(days: 365)),
  );

  if (picked != null) {
    if (isStartDate) {
      controller.startDate.value = picked.toUtc().toIso8601String();
    } else {
      controller.endDate.value = picked.toUtc().toIso8601String();
    }
    controller.getEvents(0);
  }
}
```

## 5. Event Click Navigation & Actions

### On Cell Tap Event
When a user clicks on any cell in the events table:

```dart
onCellTap: (details) {
  Get.put(ViewSingleEventController())
      .event(controller.events[details.rowColumnIndex.rowIndex - 1]);
  Get.to(
      () => ViewSingleEventOrganizer(
            eventmodel: MyEventsModel(
                event: controller.events[details.rowColumnIndex.rowIndex - 1]),
          ),
      transition: Transition.rightToLeft);
}
```

### Navigation Flow
1. **Click Detection**: Any cell click triggers the `onCellTap` callback
2. **Data Setting**: Selected event is stored in `ViewSingleEventController.event`
3. **Navigation**: User is navigated to `ViewSingleEventOrganizer`
4. **Transition**: Right-to-left slide transition

## 6. Admin Functions & Actions

### 6.1 View Toggle
**Location**: Top-right of events admin screen
- **Table View Icon** (`Icons.table_chart_outlined`): Shows paginated data grid
- **List View Icon** (`Icons.list_alt_outlined`): Shows all events without pagination

### 6.2 Pagination Controls
**Location**: Bottom of events admin screen
- **Rows Per Page**: Dropdown (15, 30, 50, 100 options)
- **Previous/Next**: Navigation buttons
- **Refresh**: Reload current page
- **Page Info**: Current page / Total pages display

### 6.3 Excel Export
**Location**: All Events view (`getAllEvents.dart`)
- **Function**: `generateExcel()`
- **Features**:
  - Exports all events to Excel format
  - Includes: ID, Name, Description, Start Date, End Date, Location, Status
  - File picker for save location
  - Progress indicator during generation

```dart
Future generateExcel() async {
  isGeneratingExcel(true);
  try {
    final excel = Excel.createExcel();
    final sheet = excel['Events'];
    
    // Add headers
    final headers = ['ID', 'Name', 'Description', 'Start Date', 'End Date', 'Location', 'Status'];
    sheet.appendRow(headers.map((header) => TextCellValue(header)).toList());
    
    // Add data
    for (var event in allEvents) {
      final row = [
        TextCellValue(event.id.toString()),
        TextCellValue(event.title),
        TextCellValue(event.description),
        TextCellValue(event.startDate.toString()),
        TextCellValue(event.endDate.toString()),
        TextCellValue(event.longitude.toString()),
        TextCellValue(event.status.toString())
      ];
      sheet.appendRow(row);
    }
    
    // Save file
    final fileBytes = excel.encode();
    // File picker and save logic...
  } finally {
    isGeneratingExcel(false);
  }
}
```

## 7. Single Event View Page

### Screen: `ViewSingleEventOrganizer`
**Location**: `lib/screens/dashboard/pages/events/view_single_event_organizer.dart`

### Key Features Displayed

#### Header Section
- **Event Title**: Large, prominent display
- **Balance**: Formatted currency collected
- **Status Chip**: Color-coded status indicator
- **Attendees Count**: Number of participants
- **Invite Button**: Quick invite functionality

#### Media Section
- **Image Carousel**: Event photos with navigation dots
- **Hero Animations**: Smooth transitions between views
- **Image Popup**: Full-screen image viewing

#### Details Section
- **Location**: Venue with map integration
- **Date/Time**: Start and end dates with relative time
- **Description**: Rich text editor display

#### Action Tabs
1. **Services Tab**:
   - Edit Ticket
   - Edit Event
   - Manage Delegates
   - Purchase Ticket
   - Signatory Transactions
   - Verify Tickets
   - Transfer Funds
   - **Block Event** (Admin function)

2. **Transactions Tab**:
   - Transaction history
   - Grouped by date
   - Detailed transaction info
   - Export to table view

### Services Widget Actions

#### Edit Ticket
- **Page**: `EditTickectPage`
- **Function**: Modify ticket types and pricing
- **Icon**: `Icons.airplane_ticket_outlined`

#### Edit Event
- **Page**: `EditEventPage`
- **Function**: Modify event details
- **Icon**: `Icons.edit_calendar_rounded`
- **Refresh**: Triggers page refresh on completion

#### Manage Delegates
- **Page**: `InvitePage`
- **Function**: Add/remove event delegates
- **Icon**: `Icons.group_add_outlined`

#### Purchase Ticket
- **Page**: `ViewSingleEventViewer`
- **Function**: Buy tickets for the event
- **Icon**: `Icons.monetization_on_outlined`

#### Signatory Transactions
- **Page**: `SignatoryTransactions`
- **Function**: Manage financial approvals
- **Icon**: `Icons.verified_outlined`

#### Verify Tickets
- **Page**: `VerifyTicket`
- **Function**: QR code ticket verification
- **Icon**: `Icons.document_scanner_outlined`

#### Transfer Funds
- **Page**: `TransferScreen`
- **Function**: Move funds between accounts
- **Icon**: `Icons.currency_exchange`

#### Block Event (Admin Function)
- **Page**: `BlockEventPage`
- **Function**: Block/unblock events
- **Icon**: `Icons.block`

## 8. Block Event Functionality

### Block Event Page
**Location**: `lib/admin_screens/events/block_event_page.dart`

#### Features
- **Reason Input**: Multi-line text field for blocking reason
- **Photo Upload**: Add evidence photos
- **Image Preview**: Grid view of uploaded images
- **Submit Report**: Send blocking request

#### Block Event Controller Functions

##### Image Upload
```dart
Future pickImages() async {
  isUploadingImage(true);
  try {
    FilePickerResult? image = await FilePicker.platform
        .pickFiles(type: FileType.image, allowMultiple: false);
    
    if (image != null && image.files.isNotEmpty) {
      final file = image.files.first;
      final url = await uploadFileWeb(bytes: file.bytes!, fileName: fileName);
      images.add({"path": file.bytes, "url": url, "type": "Image"});
    }
  } finally {
    isUploadingImage(false);
  }
}
```

##### Submit Block
```dart
void submitBlock(int eventId) async {
  try {
    isLoading(true);
    if (reasonController.text.isEmpty) {
      Get.snackbar('Error', 'Please provide a reason');
      return;
    }

    final mediaList = images.map((image) => {"url": image["url"], "type": "Image"}).toList();

    var response = await apiProvider.request(
      method: Method.POST,
      url: ApiUrls.blockEvent,
      params: {
        "event_status": "BLOCKED",
        "event_id": eventId,
        "media": mediaList,
        "description": reasonController.text
      },
    );

    if (response.data['status'] ?? false) {
      Get.find<EventsAdminController>().events.removeWhere((event) => event.id == eventId);
      Get.back();
      Get.snackbar('Success', 'Event blocked successfully');
    }
  } finally {
    isLoading(false);
  }
}
```

##### Unblock Event
```dart
Future<void> unblockEvent(int eventId) async {
  try {
    isLoading.value = true;
    var response = await apiProvider.request(
      method: Method.POST,
      url: ApiUrls.blockEvent,
      params: {
        "event_status": "ACTIVE",
        "event_id": eventId,
      },
    );
    
    if (response.data['status'] ?? false) {
      Get.back();
      Get.snackbar('Success', 'Event unblocked successfully');
    }
  } finally {
    isLoading.value = false;
  }
}
```

## 9. Event Status Management

### Status Color Coding
```dart
Color getEventStatusColor(String status) {
  switch (status.toLowerCase()) {
    case "active": return const Color(0xFF56AF57);
    case "ended": return AppColors.greyTextColor;
    case "pending review": return Colors.amber;
    case "blocked": return const Color(0xFFEE5B60);
    default: return const Color(0xFFEE5B60);
  }
}
```

### Status Types
- **ACTIVE**: Green - Event is live and accepting registrations
- **ENDED**: Grey - Event has concluded
- **PENDING REVIEW**: Amber - Event awaiting admin approval
- **BLOCKED**: Red - Event has been blocked by admin

## 10. Data Models

### Event Model Structure
```dart
class Event {
  final int id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String title;
  final String username;
  final String description;
  final String email;
  final String phoneNumber;
  final dynamic balance;
  final String locationTip;
  final String venue;
  final double latitude;
  final double longitude;
  final Status? status;
  final int? categoryId;
  final int? kittyId;
  final List<EventMedia>? eventMedia;
  final DateTime? startDate;
  final DateTime? endDate;
  final List<Ticket>? tickets;
  final List<EventsModelSocialAccount>? socialAccounts;
}
```

### MyEventsModel Wrapper
```dart
class MyEventsModel {
  final Event event;
  final int count;
  final bool hasSignatoryTransactions;
}
```

## 11. Advanced Features

### 11.1 All Events View
**Location**: `lib/admin_screens/settings/pages/getAllEvents.dart`

#### Features
- **Reorderable List**: Drag and drop to change event order
- **PDF Generation**: Create PDF reports for events
- **Excel Export**: Export all events to Excel
- **Dual Pane**: List view with PDF preview (desktop)

#### PDF Generation
```dart
Future<Uint8List> generateEventPdf(Event event) async {
  final pdf = pw.Document();
  final font = await PdfGoogleFonts.notoSansRegular();
  
  pdf.addPage(pw.Page(
    pageFormat: PdfPageFormat.a4,
    build: (pw.Context context) {
      return pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Center(child: pw.Text(event.title, style: pw.TextStyle(
            font: font, fontSize: 24, fontWeight: pw.FontWeight.bold))),
          pw.Text('Description: ${event.description}'),
          pw.Text('Start Date: ${event.startDate?.toLocal() ?? 'N/A'}'),
          pw.Text('End Date: ${event.endDate?.toLocal() ?? 'N/A'}'),
          pw.Text('Location: ${event.latitude},${event.longitude}'),
          pw.Text('Status: ${event.status.toString()}'),
        ]
      );
    }
  ));
  
  return pdf.save();
}
```

### 11.2 Transaction Management
- **Event Transactions**: View all payments for an event
- **Grouped Display**: Transactions grouped by date
- **Detailed View**: Full transaction information in dialogs
- **Export Options**: Table view and Excel export

## 12. Key Features Summary

### Admin Table Features
- ✅ Sortable columns
- ✅ Resizable columns
- ✅ Frozen first column
- ✅ Cell-based navigation
- ✅ Loading states
- ✅ Error handling
- ✅ Pagination controls
- ✅ View toggle (table/list)

### Filtering Features
- ✅ Date range filtering
- ✅ Text search with debounce
- ✅ Multiple filter criteria
- ✅ Real-time filtering
- ✅ Phone number search
- ✅ Kitty ID filtering

### Event Management Features
- ✅ Comprehensive event details
- ✅ Media management
- ✅ Status management
- ✅ Block/unblock functionality
- ✅ Transaction tracking
- ✅ PDF generation
- ✅ Excel export
- ✅ Delegate management
- ✅ Ticket verification

### Block Event Features
- ✅ Reason documentation
- ✅ Evidence photo upload
- ✅ Image preview and management
- ✅ Status change tracking
- ✅ Unblock functionality
- ✅ Admin notifications

## 13. Technical Implementation Notes

### State Management
- Uses **GetX** for reactive state management
- Controllers handle API calls and data state
- Observable variables for real-time UI updates

### UI Components
- **SfDataGrid** for table display
- **CustomScrollView** with slivers for single event view
- **ReorderableListView** for event ordering
- **SmartRefresher** for pull-to-refresh functionality
- **OpenContainer** for smooth page transitions

### Performance Optimizations
- Debounced search inputs (1-second delay)
- Lazy loading with pagination
- Efficient data grid rendering
- Image caching for media content
- Hero animations for smooth transitions

### File Operations
- **Excel generation** with custom formatting
- **PDF creation** with custom layouts
- **Image upload** with progress tracking
- **File picker** integration for exports