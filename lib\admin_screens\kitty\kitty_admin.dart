import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/admin_screens/chama/widgets/filter_widget.dart';
import 'package:onekitty/admin_screens/common/color_codes.dart';
import 'package:onekitty/controllers/admin/kitty/kitty_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/models/user_kitties_model.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';

class KittyAdmin extends StatefulWidget {
  const KittyAdmin({super.key});

  @override
  State<KittyAdmin> createState() => _KittyAdminState();
}

class _KittyAdminState extends State<KittyAdmin> {
  final controller = Get.put(KittyAdminController());
  @override
  void initState() {
    controller.fetchKitties(0);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ChamaFilterWidget(isKitty: true, controller: controller),
        Obx(
          () => controller.isLoading.value
              ? const Center(child: CircularProgressIndicator())
              :  const KittyTable(),
        ),
      ],
    );
  }
}

class KittyDataSource extends DataGridSource {
  KittyDataSource({required List<Kitty> kitties}) {
    dataGridRows = kitties
        .map<DataGridRow>((kitty) => DataGridRow(cells: [
              DataGridCell<String>(
                  columnName: 'id', value: kitty.id?.toString() ?? '-'),
               DataGridCell<String>(
                  columnName: 'created_at',
                  value: DateFormat('dd/MM/yy HH:mm')
                      .format(kitty.createdAt ?? DateTime.now())
                      .toString()),
             
              DataGridCell<String>(
                  columnName: 'title', value: kitty.title ?? '-'),
              DataGridCell<String>(
                  columnName: 'description', value: kitty.description ?? '-'),
              DataGridCell<String>(
                  columnName: 'beneficiary_account',
                  value: kitty.beneficiaryAccount ?? '-'),
              DataGridCell<String>(
                  columnName: 'beneficiary_channel',
                  value: kitty.beneficiaryChannel ?? '-'),
              DataGridCell<String>(
                  columnName: 'beneficiary_phone',
                  value: kitty.beneficiaryPhoneNumber ?? '-'),
              DataGridCell<String>(
                  columnName: 'end_date',
                  value: kitty.endDate != null
                      ? DateFormat('dd/MM/yy HH:mm').format(kitty.endDate!)
                      : '-'),
              DataGridCell<String>(
                  columnName: 'balance',
                  value: kitty.balance != null
                      ? NumberFormat.currency(symbol: 'KES ')
                          .format(kitty.balance)
                      : '-'),
              DataGridCell<String>(
                  columnName: 'phone_number', value: kitty.phoneNumber ?? '-'),
            ]))
        .toList();
  }

  List<DataGridRow> dataGridRows = [];

  @override
  List<DataGridRow> get rows => dataGridRows;

  @override
  DataGridRowAdapter? buildRow(DataGridRow row) {
    return DataGridRowAdapter(
        cells: row.getCells().map<Widget>((dataGridCell) {
      if (dataGridCell.columnName == 'status') {
        return Text(
          dataGridCell.value.toString(),
          style:
              TextStyle(color: getStatusColor(dataGridCell.value.toString())),
        );
      }
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        alignment: Alignment.centerLeft,
        child: Text(dataGridCell.value.toString()),
      );
    }).toList());
  }
}

class KittyTable extends StatefulWidget {
  const KittyTable({super.key});

  @override
  State<KittyTable> createState() => _KittyTableState();
}

class _KittyTableState extends State<KittyTable> {
  @override
  Widget build(BuildContext context) {
    final controller = Get.put(KittyAdminController());
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Obx(
        () => SfDataGrid(
          source: KittyDataSource(kitties: controller.kitties),
          allowColumnsResizing: true,
          columnResizeMode: ColumnResizeMode.onResize,
          gridLinesVisibility: GridLinesVisibility.both,
          headerGridLinesVisibility: GridLinesVisibility.both,
          isScrollbarAlwaysShown: false,
          // shrinkWrapColumns: true,
          // shrinkWrapRows: true,

          frozenColumnsCount: 1,
          navigationMode: GridNavigationMode.cell,
          // verticalScrollPhysics: const NeverScrollableScrollPhysics(),
          columnWidthMode: ColumnWidthMode.fitByCellValue,
          onCellTap: (details) {
            var dataController = Get.put(DataController());
            dataController.kitty.value = UserKitty(
                kitty: controller.kitties[details.rowColumnIndex.rowIndex - 1]);

            Navigator.pushNamed(
              context,
              NavRoutes.viewingsinglekittyScreen,
            );
          },
          onColumnResizeUpdate: (ColumnResizeUpdateDetails details) {
            if (details.width < 60) {
              return false;
            }
            return true;
          },

          columns: _buildColumns(),
        ),
      ),
    );
  }

  List<GridColumn> _buildColumns() {
    return [
      GridColumn(
        columnName: 'id',
        width: 80,
        label: _buildHeaderCell('ID'),
      ),
      GridColumn(
        columnName: 'created_at',
        width: 120,
        label: _buildHeaderCell('Created At'),
      ),
      GridColumn(
        columnName: 'title',
        width: 150,
        label: _buildHeaderCell('Title'),
      ),
      GridColumn(
        columnName: 'description',
        width: 200,
        label: _buildHeaderCell('Description'),
      ),
      GridColumn(
        columnName: 'beneficiary_account',
        width: 150,
        label: _buildHeaderCell('Beneficiary Account'),
      ),
      GridColumn(
        columnName: 'beneficiary_channel',
        width: 150,
        label: _buildHeaderCell('Channel'),
      ),
      GridColumn(
        columnName: 'beneficiary_phone',
        width: 130,
        label: _buildHeaderCell('Phone'),
      ),
      GridColumn(
        columnName: 'end_date',
        width: 120,
        label: _buildHeaderCell('End Date'),
      ),
      GridColumn(
        columnName: 'balance',
        width: 120,
        label: _buildHeaderCell('Balance'),
      ),
      GridColumn(
        columnName: 'phone_number',
        width: 130,
        label: _buildHeaderCell('Phone Number'),
      ),
    ];
  }

  Widget _buildHeaderCell(String text) {
    return Container(
      padding: const EdgeInsets.all(8),
      alignment: Alignment.center,
      child: Text(
        text,
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
    );
  }
}
