import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:onekitty/admin_screens/chama/chama_occurence.dart';
import 'package:onekitty/admin_screens/chama/invoices.dart';
import 'package:onekitty/admin_screens/chama/penalties.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/models/chama/chama_model.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import '../../../../../utils/utils_exports.dart';

class ChamaServicesWidget extends StatefulWidget {
  final Chama chama;
  const ChamaServicesWidget({super.key, required this.chama});

  @override
  State<ChamaServicesWidget> createState() => _ChamaServicesWidgetState();
}

class _ChamaServicesWidgetState extends State<ChamaServicesWidget> {
  final ChamaDataController chamaDataController =
      Get.put(ChamaDataController());
  final ChamaController chamaController = Get.put(ChamaController());
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 0.w),
      child: Column(
        children: [
          SizedBox(height: 8.h),
          Wrap(
            spacing: 5.w,
            runSpacing: 5.h,
            children: [
              _buildCard(
                context,
                edit: AssetUrl.members,
                //color: AppColors.blueButtonColor,
                resources: "Members",
                onTap: () {
                  Get.toNamed(NavRoutes.members);
                },
              ),
              if (chamaDataController.chama.value.member?.role == "CHAIRPERSON")
                _buildCard(
                  context,
                  edit: AssetUrl.imgEdit,
                  resources: "Edit Chama",
                  onTap: () {
                    Get.toNamed(NavRoutes.updateChama);
                  },
                ),
              _buildCard(
                context,
                edit: AssetUrl.settings,
                resources: "Settings",
                color: AppColors.blueButtonColor,
                onTap: () {
                  Get.toNamed(NavRoutes.settings);
                },
              ),
              _buildCard(
                context,
                edit: AssetUrl.handCoins,
                color: AppColors.blueButtonColor,
                resources: "Contribute",
                onTap: () {
                  Get.toNamed(NavRoutes.chamaContribute);
                },
              ),
              if (chamaDataController.chama.value.member?.role == "CHAIRPERSON")
                _buildCard(
                  context,
                  edit: AssetUrl.transfer,
                  color: AppColors.blueButtonColor,
                  resources: "Transfer",
                  onTap: () {
                    Get.toNamed(NavRoutes.mainTransactions);
                  },
                ),
              Obx(() {
                if (chamaController.isSignatory.isTrue) {
                  Widget card = _buildCard(
                    context,
                    edit: AssetUrl.approveTransactions,
                    color: AppColors.blueButtonColor,
                    resources: "Signatory\nTransactions",
                    onTap: () {
                      Get.toNamed(NavRoutes.signatoryTransactiions);
                    },
                  );

                  if (chamaController.hasSigTrans.isTrue) {
                    card = card
                        .animate(
                          onComplete: (controller) =>
                              controller.repeat(reverse: true),
                        )
                        .shimmer(
                            duration: const Duration(milliseconds: 500),
                            curve: Curves.bounceInOut,
                            color: Colors.red)
                        .tint(
                            duration: const Duration(milliseconds: 500),
                            color: Colors.red);
                  }

                  return card;
                } else {
                  return const SizedBox.shrink();
                }
              }),
              _buildCard(
                context,
                edit: AssetUrl.meetings,
                resources: "Meetings",
                onTap: () {
                  Get.toNamed(NavRoutes.meetings);
                },
              ),
              _buildCard(
                context,
                edit: AssetUrl.resources,
                resources: "Documents",
                onTap: () {
                  Get.toNamed(NavRoutes.resourcesView);
                },
              ),
              _buildCard(
                context,
                edit: AssetUrl.penalty,
                resources: "Penalties",
                onTap: () {
                  Get.toNamed(NavRoutes.penalties);
                },
              ),
              _buildCard(
                context,
                edit: AssetUrl.signstories,
                resources: "Signatories",
                onTap: () {
                  Get.toNamed(NavRoutes.signatories);
                },
              ),
              _buildCard(
                context,
                icon: Icons.calendar_month,
                resources: "Occurence",
                onTap: () {
                   Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          ChamaOccurence(chama: widget.chama,)));
                          
                },
              ),
              _buildCard(
                context,
                icon: Icons.receipt_long,
                resources: "Invoices",
                onTap: () {
                   Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          InvoicesPage(chama: widget.chama)));
                          },
              ),
                _buildCard(
                context,
                icon: Icons.receipt_long,
                resources: "General Penalty",
                onTap: () {
                    Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => Penalties(
                                          
                                          chama: widget.chama, )));
      }    ),
              
            ],
          ),
        ],
      ),
    );
  }

  /// Common widget
  Widget _buildCard(
    BuildContext context, {
    String? edit,
    IconData? icon,
    required String resources,
    required VoidCallback onTap,
    Color? color,
  }) {
    if(icon == null && edit == null) {
      throw Exception("Icon or edit must be provided");
    }
    return InkWell(
      onTap: onTap,
      child: Container(
        width: 102.w,
        height: 60.h,
        margin: const EdgeInsets.symmetric(horizontal: 2, vertical: 3),
        padding: EdgeInsets.symmetric(
          horizontal: 15.w,
          vertical: 4.h,
        ),
        decoration: AppDecoration.outlineIndigo.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder8,
        ),
        child: FittedBox(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (icon != null)
                Icon(
                  icon,
                  size: 30.h,
                  color: color ?? AppColors.blueButtonColor,
                ),
                if(edit != null)
              SvgPicture.asset(
                edit,
                height: 30.h,
                width: 30.w,
                colorFilter: ColorFilter.mode(
                    color ?? AppColors.blueButtonColor, BlendMode.srcIn),
              ),
              SizedBox(height: 2.h),
              Center(
                child: Text(
                  resources,
                  style: theme.textTheme.labelLarge!.copyWith(
                    color: appTheme.indigo500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
