import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../services/api_urls.dart';
import '../../services/http_service.dart';

class ViewEventController extends GetxController implements GetxService {
  final isReserving = false.obs;
  final isInviting = false.obs;
  final RxInt selectedTicket = 0.obs;

  final HttpService apiProvider = Get.find();
  Future reserve(
      {required String eventId,
      required String phoneNumber,
      required String firstname,
      required String email,
      required List tickets}) async {
    try {
      isReserving(true);

      var response = await apiProvider
          .request(url: ApiUrls.RESERVETICKET, method: Method.POST, params: {
        "event_id": eventId,
        "phone_number": phoneNumber,
        "longitude": 1.33,
        "latitude": 3.5,
        "first_name": firstname,
        "second_name": '',
        "email": email,
        "tickets": tickets
      });
      if (response.data['status'] ?? false) {
        Get.snackbar('Success', 'Successfully reserved ticket');
      } else {
        throw response.data['message'] ?? 'error';
      }
    } catch (e) {
      Get.snackbar('error', '$e', backgroundColor: Colors.red);
    } finally {
      isReserving(false);
    }
  }

  Future invite(
      {required int ticketId,
      required String number,
      required String fname,
      required String lname,
      required String email}) async {
    try {
      isInviting(true);
      var response = await apiProvider
          .request(url: ApiUrls.INVITEUSERS, method: Method.POST, params: {
        "ticket_id": ticketId,
        "users": [
          {
            "phone_number": number,
            "first_name": fname,
            "second_name": lname,
            "email": email
          }
        ]
      });

      if (response.data['status'] ?? false) {
        Get.snackbar('Success', 'invite sent', backgroundColor: Colors.green);
      } else {
        throw response.data['message'] ?? 'error';
      }
    } catch (e) {
      Get.snackbar('Error', '$e', backgroundColor: Colors.red);
    } finally {
      isInviting(false);
    }
  }
}
