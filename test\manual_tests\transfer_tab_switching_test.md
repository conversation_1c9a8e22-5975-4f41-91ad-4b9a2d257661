# Transfer Page Tab Switching Manual Test

## Overview
This manual test verifies that the transfer page correctly handles tab switching and prevents data contamination between different transfer modes.

## Test Environment
- Device: Any Android/iOS device or emulator
- App: OneKitty Transfer Page
- Transfer Types: Event, Chama, Penalty

## Test Cases

### Test Case 1: Basic Tab Switching
**Objective**: Verify that switching tabs clears previous tab data

**Steps**:
1. Open Transfer Page (any transfer type)
2. Stay on Mobile Money tab (first tab)
3. Enter phone number: `+************`
4. Switch to Paybill tab
5. Verify phone number field is cleared
6. Enter Paybill number: `123456`
7. Enter Account number: `ACC123`
8. Switch to Till tab
9. Verify Paybill and Account fields are cleared
10. Enter Till number: `789012`
11. Switch to Bank tab
12. Verify Till number field is cleared
13. Select a bank from dropdown
14. Enter bank account: `BANK123456`
15. Switch back to Mobile Money tab
16. Verify bank selection and account are cleared

**Expected Result**: Each tab switch should clear the previous tab's data completely.

### Test Case 2: Transfer with Correct Tab Data
**Objective**: Verify that transfer uses only current tab data

**Steps**:
1. Open Transfer Page
2. Enter amount: `1000`
3. Enter reason: `Test transfer`
4. Go to Mobile Money tab
5. Enter phone: `+************`
6. Switch to Paybill tab
7. Enter Paybill: `123456`
8. Enter Account: `ACC123`
9. Stay on Paybill tab
10. Click Transfer button
11. Verify confirmation dialog shows Paybill details only
12. Cancel the transfer
13. Switch to Mobile Money tab
14. Click Transfer button
15. Verify confirmation dialog shows Mobile Money details only

**Expected Result**: Transfer should only use data from the currently active tab.

### Test Case 3: Data Persistence Prevention
**Objective**: Verify no data persists across tab switches

**Steps**:
1. Open Transfer Page
2. Fill all tabs with data:
   - Mobile: `+************`
   - Paybill: `123456` / `ACC123`
   - Till: `789012`
   - Bank: Select bank + `BANK123`
3. Switch between tabs multiple times
4. Go to Mobile Money tab
5. Click Transfer (with amount filled)
6. Check if any paybill/till/bank data appears in request

**Expected Result**: Only mobile money data should be in the transfer request.

### Test Case 4: Form Reset After Transfer
**Objective**: Verify form resets after successful transfer

**Steps**:
1. Open Transfer Page
2. Fill amount: `1000`
3. Fill reason: `Test transfer`
4. Fill mobile money details
5. Complete transfer successfully
6. Return to transfer page
7. Verify all fields are empty
8. Verify tab is reset to Mobile Money (first tab)

**Expected Result**: All form fields should be cleared and tab reset to first position.

### Test Case 5: Validation Per Tab
**Objective**: Verify validation works correctly for each tab

**Steps**:
1. Open Transfer Page
2. Enter amount: `1000`
3. For each tab:
   - Switch to tab
   - Leave required fields empty
   - Click Transfer
   - Verify appropriate validation message
   - Fill required fields
   - Verify validation passes

**Expected Result**: Each tab should validate only its own required fields.

### Test Case 6: Multiple Transfer Types
**Objective**: Verify behavior across different transfer types

**Steps**:
1. Test with Event Transfer:
   - Follow Test Cases 1-5
2. Test with Chama Transfer:
   - Follow Test Cases 1-5
3. Test with Penalty Transfer:
   - Follow Test Cases 1-5

**Expected Result**: All transfer types should behave consistently.

## Bug Scenarios to Watch For

### Scenario A: Data Contamination
- Fill Mobile Money tab with phone number
- Switch to Paybill tab
- Fill paybill details
- Switch back to Mobile Money
- Click Transfer
- **Bug**: If paybill data appears in mobile money transfer

### Scenario B: Validation Confusion
- Fill Paybill tab completely
- Switch to Mobile Money tab (leave phone empty)
- Click Transfer
- **Bug**: If validation passes or shows wrong error message

### Scenario C: State Persistence
- Complete a transfer successfully
- Start a new transfer
- **Bug**: If previous transfer data is still visible

### Scenario D: Tab Index Mismatch
- Switch tabs rapidly
- Click Transfer
- **Bug**: If wrong transfer mode is used (e.g., on Till tab but sends as Mobile Money)

## Success Criteria

✅ **Pass**: All test cases complete without bugs
✅ **Pass**: No data contamination between tabs
✅ **Pass**: Correct validation for each tab
✅ **Pass**: Proper form reset after transfer
✅ **Pass**: Consistent behavior across transfer types

❌ **Fail**: Any data persists when switching tabs
❌ **Fail**: Wrong validation messages or validation bypass
❌ **Fail**: Transfer uses wrong mode or wrong data
❌ **Fail**: Form doesn't reset after successful transfer

## Notes
- Test on both Android and iOS if possible
- Test with different screen sizes
- Test with slow network conditions
- Test rapid tab switching
- Test interrupting transfers mid-process
