import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/admin/chama/chama_admin_controller.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'edit_chama.dart';

class ChamaSettingsScreen extends StatefulWidget {
  final int id;
  // final Function(ChamaSetting) onEdit;

  const ChamaSettingsScreen({
    super.key,
    required this.id,
  });

  @override
  State<ChamaSettingsScreen> createState() => _ChamaSettingsScreenState();
}

class _ChamaSettingsScreenState extends State<ChamaSettingsScreen> {
  final controller = Get.find<ChamaAdminController>();
  @override
  void initState() {
    controller.getChamaSettings(chamaId: widget.id);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Chama Settings')),
      body: Obx(
        () => controller.isGetAllChamaDetailsLoading.value
            ? const Center(
                child: CircularProgressIndicator(),
              )
            : Obx(
                () => Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildInfoTile('Beneficiaries Per Cycle',
                          '${controller.chamaSettings.value.beneficiariesPerCycle}'),
                      _buildInfoTile('Beneficiary Percentage',
                          '${(controller.chamaSettings.value.beneficiaryPercentage ?? 0) * 100}%'),
                      _buildInfoTile('Signature Threshold',
                          '${controller.chamaSettings.value.signatureThreshold}'),
                      const SizedBox(height: 20),
                      ElevatedButton(
                        onPressed: () {
                          final ChamaController chamaController =
                              Get.put(ChamaController());
                          final ChamaDataController chamaDataController =
                              Get.put(ChamaDataController());

                          chamaController.benefPerCycle(controller
                              .chamaSettings.value.beneficiariesPerCycle);
                          chamaController.signatureThreshold(controller
                              .chamaSettings.value.signatureThreshold);
                          chamaController.benefPercentage(controller
                              .chamaSettings.value.beneficiaryPercentage);

                          chamaController
                              .chamaSettings(controller.chamaSettings.value);
                          chamaController.benefPerCycle(controller
                              .chamaSettings.value.beneficiariesPerCycle);

                          chamaController
                              .settingId(controller.chamaSettings.value.id);
                          Get.to(() => EditChamaSettingsAdmin(
                              chamaSetting: controller.chamaSettings.value));
                        },
                        child: const Text('Edit Settings'),
                      ),
                    ],
                  ),
                ),
              ),
      ),
    );
  }

  Widget _buildInfoTile(String title, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
          Text(value),
        ],
      ),
    );
  }

  // void _showEditDialog(BuildContext context) {
  //   final ChamaSetting settings = controller.chamaSettings.value;
  //   showDialog(
  //     context: context,
  //     builder: (context) => AlertDialog(
  //       title: const Text('Edit Chama Settings'),
  //       content: SingleChildScrollView(
  //         child: Column(
  //           mainAxisSize: MainAxisSize.min,
  //           children: [
  //             TextField(
  //               decoration:
  //                   const InputDecoration(labelText: 'Beneficiaries Per Cycle'),
  //               keyboardType: TextInputType.number,
  //               controller: TextEditingController(
  //                   text: settings.beneficiariesPerCycle.toString()),
  //               onChanged: (value) =>
  //                   settings.beneficiariesPerCycle = int.tryParse(value),
  //             ),
  //             TextField(
  //               decoration:
  //                   const InputDecoration(labelText: 'Beneficiary Percentage'),
  //               keyboardType:
  //                   const TextInputType.numberWithOptions(decimal: true),
  //               controller: TextEditingController(
  //                   text: settings.beneficiaryPercentage.toString()),
  //               onChanged: (value) =>
  //                   settings.beneficiaryPercentage = double.tryParse(value),
  //             ),
  //             TextField(
  //               decoration:
  //                   const InputDecoration(labelText: 'Signature Threshold'),
  //               keyboardType: TextInputType.number,
  //               controller: TextEditingController(
  //                   text: settings.signatureThreshold.toString()),
  //               onChanged: (value) =>
  //                   settings.signatureThreshold = int.tryParse(value),
  //             ),
  //           ],
  //         ),
  //       ),
  //       actions: [
  //         TextButton(
  //           onPressed: () => Navigator.pop(context),
  //           child: const Text('Cancel'),
  //         ),
  //         ElevatedButton(
  //           onPressed: () {
  //             //todo Update chama settings
  //             Navigator.pop(context);
  //           },
  //           child: const Text('Save'),
  //         ),
  //       ],
  //     ),
  //   );
  // }
}
