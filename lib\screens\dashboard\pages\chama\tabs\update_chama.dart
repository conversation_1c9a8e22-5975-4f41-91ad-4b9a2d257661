import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_quill/flutter_quill.dart' as q;
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';
import 'package:onekitty/models/chama/chamaDto.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/widgets/date_picker.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';

import '../../../../../models/chama/chama_model.dart';
import '../../../../../utils/utils_exports.dart';

class UpdateChama extends StatefulWidget {
  const UpdateChama({super.key});

  @override
  State<UpdateChama> createState() => _MyWidgetState();
}

class _MyWidgetState extends State<UpdateChama> {
  final ChamaController _chamaController = Get.find<ChamaController>();
  final ChamaDataController _dataController = Get.put(ChamaDataController());
  TextEditingController chamaNameController = TextEditingController();
  TextEditingController descrController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController dateController = TextEditingController();
  TextEditingController timeController = TextEditingController();
  DateTime combinedDateTime = DateTime.now();

  TextEditingController amountController = TextEditingController();
  q.QuillController _controller = q.QuillController.basic();
  q.QuillController controller = q.QuillController.basic();
  String? selectedvalue;
  String? description;

  List<String> dropdownItems = [];
  TextEditingController freqcyController = TextEditingController();

  @override
  void initState() {
    setState(() {
      dropdownItems = _chamaController.frequencies
          .map((frequency) => frequency.frequency)
          .toList();
    });
    setvalues();
    super.initState();
  }

  void setvalues() {
    selectedvalue = _dataController.singleChamaDts.value.frequency;
    chamaNameController.text = _dataController.singleChamaDts.value.title ?? "";
    emailController.text = _dataController.singleChamaDts.value.email ?? "";
    amountController.text =
        _dataController.singleChamaDts.value.amount.toString();
    dateController.text = DateFormat.yMd().format(
        _dataController.singleChamaDts.value.nextOccurrence ?? DateTime.now());
    timeController.text =
        _dataController.singleChamaDts.value.nextOccurrence == null
            ? ""
            : DateFormat().add_jm().format(
                _dataController.singleChamaDts.value.nextOccurrence!.toLocal());

    final String initialContent =
        _dataController.singleChamaDts.value.description ??
            ""; // Assuming content is a string
    var myJSON = jsonDecode(initialContent);
    _controller = q.QuillController(
      document: q.Document.fromJson(myJSON),
      selection: const TextSelection.collapsed(offset: 0),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        //appBar: buildAppBar(context),
        body: SingleChildScrollView(
          child: Column(
            children: [
              const RowAppBar(),
              Text(
                "Update Chama Details",
                style: Theme.of(context)
                    .textTheme
                    .titleLarge
                    ?.copyWith(fontWeight: FontWeight.bold, fontSize: 22),
              ),
              buildTextField(context),
              SizedBox(
                height: 20.h,
              ),
              buildSaveChanges(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildTextField(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(15.0),
      child: Form(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Update Chama Name",
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15),
            ),
            SizedBox(
              height: 5.h,
            ),
            CustomTextField(
              controller: chamaNameController,
              hintText: "e.g Wedding Contribution",
              labelText: "",
              validator: (p0) {
                if (p0!.isEmpty) {
                  return "Filed cannot be empty";
                } else if (p0.length < 5) {
                  return "Chama Name must be between 5 and 300";
                }
                return p0;
              },
            ),
            Text("Chama Description",
                style: Theme.of(context)
                    .textTheme
                    .titleLarge
                    ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15)),
            Container(
              decoration: BoxDecoration(
                color: Colors.blueAccent.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.0),
                border: Border.all(
                  color: AppColors.blueButtonColor,
                  width: 1.0,
                ),
              ),
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 8),
                  q.QuillToolbar.simple(
                    configurations: q.QuillSimpleToolbarConfigurations(
                      multiRowsDisplay: false,
                      sharedConfigurations: const q.QuillSharedConfigurations(
                        locale: Locale('en'),
                      ),
                      controller: _controller,
                    ),
                  ),
                  const SizedBox(height: 15),
                  q.QuillEditor.basic(
                    configurations: q.QuillEditorConfigurations(
                      placeholder: "e.g purpose of chama",
                      controller: _controller,
                      // readOnly: false,
                      autoFocus: false,
                      enableInteractiveSelection:
                          true, // Enable interactive selection to allow text editing

                      sharedConfigurations: const q.QuillSharedConfigurations(
                        locale: Locale('en'),
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                ],
              ),
            ),
            SizedBox(
              height: 10.h,
            ),
            Text(
              "Chama Email",
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15),
            ),
            SizedBox(
              height: 5.h,
            ),
            CustomTextField(
              controller: emailController,
              hintText: "e.g <EMAIL>",
              labelText: "Email",
              validator: (p0) {
                if (p0!.isEmpty) {
                  return "Email cannot be empty";
                } else if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                    .hasMatch(p0)) {
                  return "Enter a valid email address";
                }
                return null;
              },
            ),
            Text(
              "Update Frequency",
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15),
            ),
            SizedBox(
              height: 5.h,
            ),
            DropdownButtonFormField<String>(
              decoration: InputDecoration(
                labelText: "Frequency",
                // fillColor: Colors.blueAccent.withOpacity(0.1),
              ),
              isExpanded: true,
              items: dropdownItems
                  .map(
                    (String item) => DropdownMenuItem<String>(
                      value: item,
                      child: Text(
                        item,
                        style: const TextStyle(
                          fontSize: 14,
                        ),
                      ),
                    ),
                  )
                  .toList(),
              value: selectedvalue,
              onChanged: (String? value) {
                setState(() {
                  selectedvalue = value;
                  freqcyController.text = value!;
                });
              },
            ),
            Text(
              description ?? "",
              style: const TextStyle(fontStyle: FontStyle.italic),
            ),
            SizedBox(
              height: 5.h,
            ),
            Text(
              "Contribution Amount",
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15),
            ),
            SizedBox(
              height: 5.h,
            ),
            CustomTextField(
              inputFormatters: <TextInputFormatter>[
                FilteringTextInputFormatter.allow(RegExp("[0-9]")),
              ],
              controller: amountController,
              hintText: "1000",
              labelText: "Amount",
              validator: (p0) {
                if (p0!.isEmpty) {
                  return "Amount is required";
                }
                return null;
              },
            ),
            SizedBox(
              height: 8.h,
            ),
            Text(
              "Update Chama Deadline",
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15),
            ),
            SizedBox(
              height: 5.h,
            ),
            DatePick(
              date: dateController,
              time: timeController,
              // onDateTimeSelected: (DateTime) => _updateCombinedDateTime,
              combinedDateTime: combinedDateTime,
            ),
          ],
        ),
      ),
    );
  }

  Widget buildSaveChanges(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(right: 14.w),
      child: Align(
        alignment: Alignment.centerRight,
        child: Obx(() => CustomKtButton(
            isLoading: _chamaController.isUpdating.isTrue,
            onPress: () async {
              DateTime date = DateFormat.yMd().parse(dateController.text);

              TimeOfDay time = TimeOfDay.fromDateTime(
                  DateFormat.Hm().parse(timeController.text));

              DateTime combinedDateTime = DateTime(
                date.year,
                date.month,
                date.day,
                time.hour,
                time.minute,
              );

              final descr = jsonEncode(
                _controller.document.toDelta().toJson(),
              );
              final updateDto = UpdateDto(
                id: _dataController.singleChamaDts.value.id ?? 1,
                title: chamaNameController.text,
                description: descr.toString(),
                email: emailController.text,
                frequency: selectedvalue ?? "",
                amount: double.parse(amountController.text),
                nextOccurrence: combinedDateTime.toUtc(),
              );
              final res =
                  await _chamaController.UpdateChama(updateDto: updateDto);

              if (res) {
                var resp = await _chamaController.getChamaDetails(
                    chamaId: _dataController.singleChamaDts.value.id ?? 1);

                if (resp) {
                  var chamaDataController = Get.put(ChamaDataController());

                  chamaDataController.singleChamaDts.value = Chama();

                  chamaDataController.singleChamaDts.value =
                      _chamaController.chamaDetails.value;
                }
                Get.offNamed(NavRoutes.singleChama);
                Snack.show(res, _chamaController.apiMessage.string);
              } else {
                Snack.show(res, _chamaController.apiMessage.string);
              }
            },
            width: 130,
            height: 40,
            btnText: "Save Changes")),
      ),
    );
  }
}
