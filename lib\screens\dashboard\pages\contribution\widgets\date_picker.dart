import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/helpers/show_toast.dart';

class DatePicker extends StatefulWidget {
  final TextEditingController date;
  final TextEditingController time;
  final bool isAllow;

  const DatePicker(
      {super.key,
      required this.date,
      required this.time,
      required this.isAllow});

  @override
  State<DatePicker> createState() => _DatePickerState();
}

class _DatePickerState extends State<DatePicker> {
  late DateTime selectedDate;
  late TimeOfDay selectedTime;

  // setDate() {
  //   String? date = DateFormat().add_yMd().format(DateTime.now());
  //   String? time = DateFormat().add_jm().format(DateTime.now());
  // }

  @override
  void initState() {
    super.initState();
    selectedDate = DateTime.now();
    selectedTime = TimeOfDay.now();
    //setDate();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
      selectableDayPredicate: widget.isAllow
          ? (day) {
              return day
                  .isAfter(DateTime.now().subtract(const Duration(days: 1)));
            }
          : null,
    );
    if (picked != null && picked != selectedDate) {
      setState(() {
        selectedDate = picked;
        widget.date.text = DateFormat('yyyy-MM-dd').format(selectedDate);
      });
    } else {
      if (mounted) {
        ToastUtils.showErrorToast(context, "You didn't pick a date", "Oops!");
      }
    }
  }

  Future<void> _selectTime(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: selectedTime,
    );
    if (picked != null && picked != selectedTime) {
      setState(() {
        selectedTime = picked;
        widget.time.text = selectedTime.format(context);
      });
    } else {
      if (mounted) {
        // setState(() {
        //   widget.time.text = DateFormat().add_jm().format(DateTime.now());
        // });
        ToastUtils.showErrorToast(context, "You didn't pick any time", "Oops!");
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: TextFormField(
            onTap: () => _selectDate(context),
            readOnly: true,
            controller: widget.date,
            decoration: InputDecoration(
              labelText: DateFormat().add_yMd().format(DateTime.now()),
              suffixIcon: IconButton(
                onPressed: () => _selectDate(context),
                icon: const Icon(Icons.calendar_month),
              ),
            ),
            validator: (value) {
              if (value!.isEmpty) {
                return "Date can't be empty";
              }
              return null;
            },
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: TextFormField(
            onTap: () => _selectTime(context),
            readOnly: true,
            controller: widget.time,
            decoration: InputDecoration(
              labelText: DateFormat().add_jm().format(DateTime.now()),
              suffixIcon: IconButton(
                onPressed: () => _selectTime(context),
                icon: const Icon(Icons.access_time),
              ),
            ),
            validator: (value) {
              if (value!.isEmpty) {
                return "Time can't be empty";
              }
              return null;
            },
          ),
        ),
      ],
    );
  }
}

class DateOnlyPicker extends StatefulWidget {
  final TextEditingController dateController;
  final bool isAllow;
  const DateOnlyPicker(
      {super.key, required this.dateController, required this.isAllow});

  @override
  State<DateOnlyPicker> createState() => _DateOnlyPickerState();
}

class _DateOnlyPickerState extends State<DateOnlyPicker> {
  Future<void> selectDate(
    BuildContext context,
  ) async {
    final DateTime initialDate = DateTime.now();
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: DateTime(1920),
      lastDate: DateTime(2100),
      selectableDayPredicate: widget.isAllow
          ? (day) {
              return day
                  .isAfter(DateTime.now().subtract(const Duration(days: 1)));
            }
          : null,
    );

    if (picked != null) {
      widget.dateController.text = DateFormat('yyyy-MM-dd').format(picked);
    } else {
      if (context.mounted) {
        // Display toast or handle error
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text("You didn't pick a date")),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      onTap: () => selectDate(context),
      readOnly: true,
      controller: widget.dateController,
      decoration: InputDecoration(
        labelText: DateFormat('yyyy-MM-dd').format(DateTime.now()),
        suffixIcon: IconButton(
          onPressed: () => selectDate(context),
          icon: const Icon(Icons.calendar_month),
        ),
      ),
      validator: (value) {
        if (value!.isEmpty) {
          return "Date can't be empty";
        }
        return null;
      },
    );
  }
}

class TimePicker extends StatefulWidget {
  final TextEditingController time;

  const TimePicker({super.key, required this.time});

  @override
  State<TimePicker> createState() => _TimePickerState();
}

class _TimePickerState extends State<TimePicker> {
  late TimeOfDay selectedTime;

  @override
  void initState() {
    super.initState();
    selectedTime = TimeOfDay.now();
  }

  Future<void> _selectTime(BuildContext context) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: selectedTime,
    );
    if (picked != null && picked != selectedTime) {
      setState(() {
        selectedTime = picked;
        widget.time.text = selectedTime.format(context);
      });
    } else {
      if (mounted) {
        ToastUtils.showErrorToast(context, "You didn't pick any time", "Oops!");
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: InkWell(
            onTap: () {
              _selectTime;
            },
            child: TextFormField(
              readOnly: true,
              controller: widget.time,
              decoration: InputDecoration(
                labelText: "Choose Time",
                suffixIcon: IconButton(
                  onPressed: () => _selectTime(context),
                  icon: const Icon(Icons.access_time),
                ),
              ),
              validator: (value) {
                if (value!.isEmpty) {
                  return "Time can't be empty";
                }
                return null;
              },
            ),
          ),
        ),
      ],
    );
  }
}

// ignore: must_be_immutable
class DatePick extends StatefulWidget {
  final TextEditingController date;
  final TextEditingController time;
  DateTime combinedDateTime;
  DatePick({
    super.key,
    required this.date,
    required this.time,
    required this.combinedDateTime, // Pass the callback function
  });

  @override
  _DatePickState createState() => _DatePickState();
}

class _DatePickState extends State<DatePick> {
  DateTime selectedDate = DateTime.now();
  TimeOfDay selectedTime = TimeOfDay.now();

  Future<void> _selectDateAndTime(BuildContext context) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: selectedDate,
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );

    if (pickedDate != null && pickedDate != selectedDate) {
      final TimeOfDay? pickedTime = await showTimePicker(
        context: context,
        initialTime: selectedTime,
      );

      if (pickedTime != null) {
        setState(() {
          selectedDate = pickedDate;
          selectedTime = pickedTime;
          widget.combinedDateTime = DateTime(
            selectedDate.year,
            selectedDate.month,
            selectedDate.day,
            selectedTime.hour,
            selectedTime.minute,
          );

          // Invoke callback function with combined date and time
          print("From function ${widget.combinedDateTime}");
        });
        widget.date.text = DateFormat.yMd().format(selectedDate);
        widget.time.text = selectedTime.format(context);

        // Calculate combined date and time
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: TextFormField(
            onTap: () {
              _selectDateAndTime(context);
            },
            readOnly: true,
            controller: widget.date,
            decoration: InputDecoration(
              // fillColor: Colors.blueAccent.withOpacity(0.1),
              labelText: "Date",
              suffixIcon: IconButton(
                onPressed: () => _selectDateAndTime(context),
                icon: const Icon(Icons.calendar_month),
              ),
            ),
            validator: (value) {
              if (value!.isEmpty) {
                return "Date can't be empty";
              }
              return null;
            },
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: TextFormField(
            readOnly: true,
            controller: widget.time,
            onTap: () async {
              final TimeOfDay? pickedTime = await showTimePicker(
                context: context,
                initialTime: selectedTime,
              );

              if (pickedTime != null) {
                setState(() {
                  selectedTime = pickedTime;
                  widget.combinedDateTime = DateTime(
                    selectedDate.year,
                    selectedDate.month,
                    selectedDate.day,
                    selectedTime.hour,
                    selectedTime.minute,
                  );

                  // Invoke callback function with combined date and time
                  print("From function ${widget.combinedDateTime}");
                });
                widget.date.text = DateFormat.yMd().format(selectedDate);
                widget.time.text = selectedTime.format(context);

                // Calculate combined date and time
              }
            },
            decoration: InputDecoration(
              // fillColor: Colors.blueAccent.withOpacity(0.1),
              suffixIcon: IconButton(
                onPressed: () {},
                icon: const Icon(Icons.timer_outlined),
              ),
              labelText: "Time",
            ),
            validator: (value) {
              if (value!.isEmpty) {
                return "Time can't be empty";
              }
              return null;
            },
          ),
        ),
      ],
    );
  }
}
