import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty/admin_screens/chama/chama_admin.dart';
import 'package:onekitty/admin_screens/events/events_admin.dart';
import 'package:onekitty/admin_screens/settings/settings.dart'; 
import 'package:onekitty/screens/onboarding/passwd_req_screen.dart';
import 'package:onekitty/services/auth_manager.dart';

import '../../admin_screens/kitty/kitty_admin.dart';

class DashboardController extends GetxController {
  final _authenticationManager = Get.put(AuthenticationManager());

  void logOutUser() async {
    if (_authenticationManager.isLogged.isTrue) {
      var isAuthenticationSuccesfull = await Get.to(
        () => const AuthPasswdScreen(),
        arguments: [false],
      );
      if (isAuthenticationSuccesfull == true) {}
    }
  }

  final pageNo = 0.obs;
  Widget AdminPages(int pageNo) {
    switch (pageNo) {
      case 0:
        return const ChamaAdmin();
      case 1:
        return const EventsAdmin();
      case 2:
        return const SettingsPage();
      case 3:
        return const KittyAdmin();
      default:
        return const ChamaAdmin();
    }
  }
}
