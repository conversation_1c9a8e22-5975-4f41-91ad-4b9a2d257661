// To parse this JSON data, do
//
//     final operatorsModel = operatorsModelFromJson(jsonString);

import 'dart:convert';

List<OperatorsModel> operatorsModelFromJson(String str) =>
    List<OperatorsModel>.from(
        json.decode(str).map((x) => OperatorsModel.fromJson(x)));

String operatorsModelToJson(List<OperatorsModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class OperatorsModel {
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final dynamic userId;
  final String firstName;
  final String lastName;
  final String role;
  final bool isSignatory;
  final int kittyId;
  final String status;
  final String phoneNumber;
  final String whatsappNumber;
  final String email;

  OperatorsModel({
    this.id = 0,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.userId,
    this.firstName = '',
    this.lastName = '',
    this.role = '',
    this.isSignatory = false,
    this.kittyId = 0,
    this.status = '',
    this.phoneNumber = '',
    this.whatsappNumber = '',
    this.email = '',
  });

  factory OperatorsModel.fromJson(Map<String, dynamic> json) => OperatorsModel(
        id: json["ID"] ?? 0,
        createdAt: json["CreatedAt"] != null
            ? DateTime.parse(json["CreatedAt"])
            : null,
        updatedAt: json["UpdatedAt"] != null
            ? DateTime.parse(json["UpdatedAt"])
            : null,
        deletedAt: json["DeletedAt"],
        userId: json["user_id"],
        firstName: json["first_name"] ?? '',
        lastName: json["last_name"] ?? '',
        role: json["role"] ?? '',
        isSignatory: json["is_signatory"] ?? false,
        kittyId: json["kitty_id"] ?? 0,
        status: json["status"] ?? '',
        phoneNumber: json["phone_number"] ?? '',
        whatsappNumber: json["whatsapp_number"] ?? '',
        email: json["email"] ?? '',
      );

  Map<String, dynamic> toJson() => {
        "ID": id ?? 0,
        "CreatedAt": createdAt?.toIso8601String() ?? '',
        "UpdatedAt": updatedAt?.toIso8601String() ?? '',
        "DeletedAt": deletedAt,
        "user_id": userId,
        "first_name": firstName,
        "last_name": lastName,
        "role": role,
        "is_signatory": isSignatory,
        "kitty_id": kittyId,
        "status": status,
        "phone_number": phoneNumber,
        "whatsapp_number": whatsappNumber,
        "email": email,
      };
}
