# Admin Widgets Library Documentation

## Reusable Widget Components

### 1. Filter Widget
**File**: `lib/admin_screens/chama/widgets/filter_widget.dart`

```dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ChamaFilterWidget extends StatelessWidget {
  final dynamic controller;
  final bool isKitty;

  const ChamaFilterWidget({
    super.key,
    required this.controller,
    this.isKitty = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: TextField(
                    decoration: const InputDecoration(
                      labelText: 'Search',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      controller.search(value);
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextField(
                    decoration: const InputDecoration(
                      labelText: 'Start Date',
                      prefixIcon: Icon(Icons.calendar_today),
                      border: OutlineInputBorder(),
                    ),
                    readOnly: true,
                    onTap: () => _selectDate(context, true),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextField(
                    decoration: const InputDecoration(
                      labelText: 'End Date',
                      prefixIcon: Icon(Icons.calendar_today),
                      border: OutlineInputBorder(),
                    ),
                    readOnly: true,
                    onTap: () => _selectDate(context, false),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                ElevatedButton.icon(
                  onPressed: () => controller.fetchData(0),
                  icon: const Icon(Icons.search),
                  label: const Text('Apply Filters'),
                ),
                const SizedBox(width: 16),
                OutlinedButton.icon(
                  onPressed: () => controller.clearFilters(),
                  icon: const Icon(Icons.clear),
                  label: const Text('Clear Filters'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    
    if (picked != null) {
      final formattedDate = picked.toIso8601String().split('T')[0];
      if (isStartDate) {
        controller.startDate(formattedDate);
      } else {
        controller.endDate(formattedDate);
      }
    }
  }
}
```

### 2. Table Footer with Pagination
**File**: `lib/admin_screens/chama/widgets/table_footer.dart`

```dart
import 'package:flutter/material.dart';

class TablePaginationFooter extends StatelessWidget {
  final int currentPage;
  final int totalPages;
  final int rowsPerPage;
  final List<int> availableRowsPerPage;
  final Function(int) onRowsPerPageChanged;
  final VoidCallback onRefresh;
  final VoidCallback onPreviousPage;
  final VoidCallback onNextPage;

  const TablePaginationFooter({
    super.key,
    required this.currentPage,
    required this.totalPages,
    required this.rowsPerPage,
    required this.availableRowsPerPage,
    required this.onRowsPerPageChanged,
    required this.onRefresh,
    required this.onPreviousPage,
    required this.onNextPage,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(top: BorderSide(color: Colors.grey.shade300)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              const Text('Rows per page:'),
              const SizedBox(width: 8),
              DropdownButton<int>(
                value: rowsPerPage,
                items: availableRowsPerPage
                    .map((size) => DropdownMenuItem(
                          value: size,
                          child: Text(size.toString()),
                        ))
                    .toList(),
                onChanged: (value) {
                  if (value != null) {
                    onRowsPerPageChanged(value);
                  }
                },
              ),
              const SizedBox(width: 16),
              IconButton(
                onPressed: onRefresh,
                icon: const Icon(Icons.refresh),
                tooltip: 'Refresh',
              ),
            ],
          ),
          Row(
            children: [
              Text('Page ${currentPage + 1} of $totalPages'),
              const SizedBox(width: 16),
              IconButton(
                onPressed: currentPage > 0 ? onPreviousPage : null,
                icon: const Icon(Icons.chevron_left),
                tooltip: 'Previous Page',
              ),
              IconButton(
                onPressed: currentPage < totalPages - 1 ? onNextPage : null,
                icon: const Icon(Icons.chevron_right),
                tooltip: 'Next Page',
              ),
            ],
          ),
        ],
      ),
    );
  }
}
```

### 3. Custom List Tile
**File**: `lib/admin_screens/utils/my_list_tile.dart`

```dart
import 'package:flutter/material.dart';

class MyListTile extends StatelessWidget {
  final String title;
  final String? subtitle;
  final Widget? leading;
  final Widget? trailing;
  final VoidCallback? onTap;
  final Color? backgroundColor;

  const MyListTile({
    super.key,
    required this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.onTap,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      color: backgroundColor,
      child: ListTile(
        title: Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: subtitle != null ? Text(subtitle!) : null,
        leading: leading,
        trailing: trailing,
        onTap: onTap,
      ),
    );
  }
}
```

### 4. Loading Widget
**File**: `lib/admin_screens/utils/loading_widget.dart`

```dart
import 'package:flutter/material.dart';

class LoadingWidget extends StatelessWidget {
  final String? message;
  final double? size;

  const LoadingWidget({
    super.key,
    this.message,
    this.size,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: size ?? 50,
            height: size ?? 50,
            child: const CircularProgressIndicator(),
          ),
          if (message != null) ...[
            const SizedBox(height: 16),
            Text(
              message!,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ],
      ),
    );
  }
}
```

### 5. Empty State Widget
**File**: `lib/admin_screens/utils/empty_state_widget.dart`

```dart
import 'package:flutter/material.dart';

class EmptyStateWidget extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData? icon;
  final Widget? action;

  const EmptyStateWidget({
    super.key,
    required this.title,
    this.subtitle,
    this.icon,
    this.action,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (icon != null)
              Icon(
                icon,
                size: 64,
                color: Colors.grey.shade400,
              ),
            const SizedBox(height: 16),
            Text(
              title,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.grey.shade600,
                  ),
              textAlign: TextAlign.center,
            ),
            if (subtitle != null) ...[
              const SizedBox(height: 8),
              Text(
                subtitle!,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey.shade500,
                    ),
                textAlign: TextAlign.center,
              ),
            ],
            if (action != null) ...[
              const SizedBox(height: 24),
              action!,
            ],
          ],
        ),
      ),
    );
  }
}
```

### 6. Status Badge Widget
**File**: `lib/admin_screens/utils/status_badge.dart`

```dart
import 'package:flutter/material.dart';

class StatusBadge extends StatelessWidget {
  final String status;
  final Map<String, Color>? colorMap;

  const StatusBadge({
    super.key,
    required this.status,
    this.colorMap,
  });

  static const Map<String, Color> defaultColorMap = {
    'active': Colors.green,
    'inactive': Colors.red,
    'pending': Colors.orange,
    'completed': Colors.blue,
    'cancelled': Colors.grey,
  };

  @override
  Widget build(BuildContext context) {
    final colors = colorMap ?? defaultColorMap;
    final color = colors[status.toLowerCase()] ?? Colors.grey;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        status.toUpperCase(),
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}
```

### 7. Action Button Group
**File**: `lib/admin_screens/utils/action_button_group.dart`

```dart
import 'package:flutter/material.dart';

class ActionButtonGroup extends StatelessWidget {
  final List<ActionButtonData> buttons;
  final MainAxisAlignment alignment;

  const ActionButtonGroup({
    super.key,
    required this.buttons,
    this.alignment = MainAxisAlignment.start,
  });

  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      alignment: WrapAlignment.start,
      children: buttons
          .map((button) => ActionChip(
                avatar: button.icon != null ? Icon(button.icon, size: 18) : null,
                label: Text(button.label),
                onPressed: button.onPressed,
                backgroundColor: button.backgroundColor,
                labelStyle: TextStyle(color: button.textColor),
              ))
          .toList(),
    );
  }
}

class ActionButtonData {
  final String label;
  final IconData? icon;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? textColor;

  ActionButtonData({
    required this.label,
    this.icon,
    this.onPressed,
    this.backgroundColor,
    this.textColor,
  });
}
```

### 8. Responsive Container
**File**: `lib/admin_screens/utils/responsive_container.dart`

```dart
import 'package:flutter/material.dart';

class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final double? maxWidth;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.maxWidth,
    this.padding,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: margin,
      child: Center(
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: maxWidth ?? 1200,
          ),
          child: Container(
            padding: padding ?? const EdgeInsets.all(16),
            child: child,
          ),
        ),
      ),
    );
  }
}
```

## Usage Examples

### Using Filter Widget
```dart
ChamaFilterWidget(
  controller: Get.find<ChamaAdminController>(),
  isKitty: false,
)
```

### Using Table Footer
```dart
TablePaginationFooter(
  currentPage: controller.currentPage.value,
  totalPages: controller.totalPages.value,
  rowsPerPage: controller.size.value,
  availableRowsPerPage: const [15, 30, 50, 100],
  onRowsPerPageChanged: (size) => controller.size(size),
  onRefresh: () => controller.fetchData(0),
  onPreviousPage: () => controller.onPreviousPage(),
  onNextPage: () => controller.onNextPage(),
)
```

### Using Status Badge
```dart
StatusBadge(
  status: 'active',
  colorMap: {
    'active': Colors.green,
    'inactive': Colors.red,
  },
)
```

This widget library provides all the reusable components needed for building consistent admin interfaces across different modules.
