class EventMedia {
  final int id;
  final DateTime createdAt;
  final DateTime updatedAt;
  final dynamic deletedAt;
  final int eventId;
  final String url;
  final String title;
  final String description;
  final String type;
  final String category;

  EventMedia({
    int? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.deletedAt,
    int? eventId,
    String? url,
    String? title,
    String? description,
    String? type,
    String? category,
  })  : id = id ?? 0,
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now(),
        eventId = eventId ?? 0,
        url = url ?? "",
        title = title ?? "",
        description = description ?? "",
        type = type ?? "",
        category = category ?? "";

  factory EventMedia.fromJson(Map<String, dynamic> json) => EventMedia(
        id: json["ID"] ?? 0,
        createdAt: json["CreatedAt"] != null
            ? DateTime.parse(json["CreatedAt"])
            : DateTime.now(),
        updatedAt: json["UpdatedAt"] != null
            ? DateTime.parse(json["UpdatedAt"])
            : DateTime.now(),
        deletedAt: json["DeletedAt"],
        eventId: json["event_id"] ?? 0,
        url: json["url"] ?? "",
        title: json["title"] ?? "",
        description: json["description"] ?? "",
        type: json["type"] ?? "",
        category: json["category"] ?? "",
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt.toIso8601String(),
        "UpdatedAt": updatedAt.toIso8601String(),
        "DeletedAt": deletedAt,
        "event_id": eventId,
        "url": url,
        "title": title,
        "description": description,
        "type": type,
        "category": category,
      };
}
