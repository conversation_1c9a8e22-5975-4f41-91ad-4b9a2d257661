import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

/// Country model class
class Country {
  final String name;
  final String code;
  final String dialCode;
  final String flagUrl;

  Country({
    required this.name,
    required this.code,
    required this.dialCode,
    required this.flagUrl,
  });
}

/// A static list of countries.
/// The flagUrl is built using flagcdn.com – change if needed.
final List<Country> countriesList = [
  Country(name: 'Afghanistan', code: 'AF', dialCode: '+93', flagUrl: 'https://flagcdn.com/af.svg'),
  Country(name: 'Albania', code: 'AL', dialCode: '+355', flagUrl: 'https://flagcdn.com/al.svg'),
  Country(name: 'Algeria', code: 'DZ', dialCode: '+213', flagUrl: 'https://flagcdn.com/dz.svg'),
  Country(name: 'Andorra', code: 'AD', dialCode: '+376', flagUrl: 'https://flagcdn.com/ad.svg'),
  Country(name: 'Angola', code: 'AO', dialCode: '+244', flagUrl: 'https://flagcdn.com/ao.svg'),
  Country(name: 'Antigua and Barbuda', code: 'AG', dialCode: '+1-268', flagUrl: 'https://flagcdn.com/ag.svg'),
  Country(name: 'Argentina', code: 'AR', dialCode: '+54', flagUrl: 'https://flagcdn.com/ar.svg'),
  Country(name: 'Armenia', code: 'AM', dialCode: '+374', flagUrl: 'https://flagcdn.com/am.svg'),
  Country(name: 'Australia', code: 'AU', dialCode: '+61', flagUrl: 'https://flagcdn.com/au.svg'),
  Country(name: 'Austria', code: 'AT', dialCode: '+43', flagUrl: 'https://flagcdn.com/at.svg'),
  Country(name: 'Azerbaijan', code: 'AZ', dialCode: '+994', flagUrl: 'https://flagcdn.com/az.svg'),
  Country(name: 'Bahamas', code: 'BS', dialCode: '+1-242', flagUrl: 'https://flagcdn.com/bs.svg'),
  Country(name: 'Bahrain', code: 'BH', dialCode: '+973', flagUrl: 'https://flagcdn.com/bh.svg'),
  Country(name: 'Bangladesh', code: 'BD', dialCode: '+880', flagUrl: 'https://flagcdn.com/bd.svg'),
  Country(name: 'Barbados', code: 'BB', dialCode: '+1-246', flagUrl: 'https://flagcdn.com/bb.svg'),
  Country(name: 'Belarus', code: 'BY', dialCode: '+375', flagUrl: 'https://flagcdn.com/by.svg'),
  Country(name: 'Belgium', code: 'BE', dialCode: '+32', flagUrl: 'https://flagcdn.com/be.svg'),
  Country(name: 'Belize', code: 'BZ', dialCode: '+501', flagUrl: 'https://flagcdn.com/bz.svg'),
  Country(name: 'Benin', code: 'BJ', dialCode: '+229', flagUrl: 'https://flagcdn.com/bj.svg'),
  Country(name: 'Bhutan', code: 'BT', dialCode: '+975', flagUrl: 'https://flagcdn.com/bt.svg'),
  Country(name: 'Bolivia', code: 'BO', dialCode: '+591', flagUrl: 'https://flagcdn.com/bo.svg'),
  Country(name: 'Bosnia and Herzegovina', code: 'BA', dialCode: '+387', flagUrl: 'https://flagcdn.com/ba.svg'),
  Country(name: 'Botswana', code: 'BW', dialCode: '+267', flagUrl: 'https://flagcdn.com/bw.svg'),
  Country(name: 'Brazil', code: 'BR', dialCode: '+55', flagUrl: 'https://flagcdn.com/br.svg'),
  Country(name: 'Brunei', code: 'BN', dialCode: '+673', flagUrl: 'https://flagcdn.com/bn.svg'),
  Country(name: 'Bulgaria', code: 'BG', dialCode: '+359', flagUrl: 'https://flagcdn.com/bg.svg'),
  Country(name: 'Burkina Faso', code: 'BF', dialCode: '+226', flagUrl: 'https://flagcdn.com/bf.svg'),
  Country(name: 'Burundi', code: 'BI', dialCode: '+257', flagUrl: 'https://flagcdn.com/bi.svg'),
  Country(name: 'Cambodia', code: 'KH', dialCode: '+855', flagUrl: 'https://flagcdn.com/kh.svg'),
  Country(name: 'Cameroon', code: 'CM', dialCode: '+237', flagUrl: 'https://flagcdn.com/cm.svg'),
  Country(name: 'Canada', code: 'CA', dialCode: '+1', flagUrl: 'https://flagcdn.com/ca.svg'),
  Country(name: 'Cape Verde', code: 'CV', dialCode: '+238', flagUrl: 'https://flagcdn.com/cv.svg'),
  Country(name: 'Central African Republic', code: 'CF', dialCode: '+236', flagUrl: 'https://flagcdn.com/cf.svg'),
  Country(name: 'Chad', code: 'TD', dialCode: '+235', flagUrl: 'https://flagcdn.com/td.svg'),
  Country(name: 'Chile', code: 'CL', dialCode: '+56', flagUrl: 'https://flagcdn.com/cl.svg'),
  Country(name: 'China', code: 'CN', dialCode: '+86', flagUrl: 'https://flagcdn.com/cn.svg'),
  Country(name: 'Colombia', code: 'CO', dialCode: '+57', flagUrl: 'https://flagcdn.com/co.svg'),
  Country(name: 'Comoros', code: 'KM', dialCode: '+269', flagUrl: 'https://flagcdn.com/km.svg'),
  Country(name: 'Congo (Brazzaville)', code: 'CG', dialCode: '+242', flagUrl: 'https://flagcdn.com/cg.svg'),
  Country(name: 'Congo (Kinshasa)', code: 'CD', dialCode: '+243', flagUrl: 'https://flagcdn.com/cd.svg'),
  Country(name: 'Costa Rica', code: 'CR', dialCode: '+506', flagUrl: 'https://flagcdn.com/cr.svg'),
  Country(name: 'Côte d\'Ivoire', code: 'CI', dialCode: '+225', flagUrl: 'https://flagcdn.com/ci.svg'),
  Country(name: 'Croatia', code: 'HR', dialCode: '+385', flagUrl: 'https://flagcdn.com/hr.svg'),
  Country(name: 'Cuba', code: 'CU', dialCode: '+53', flagUrl: 'https://flagcdn.com/cu.svg'),
  Country(name: 'Cyprus', code: 'CY', dialCode: '+357', flagUrl: 'https://flagcdn.com/cy.svg'),
  Country(name: 'Czech Republic', code: 'CZ', dialCode: '+420', flagUrl: 'https://flagcdn.com/cz.svg'),
  Country(name: 'Denmark', code: 'DK', dialCode: '+45', flagUrl: 'https://flagcdn.com/dk.svg'),
  Country(name: 'Djibouti', code: 'DJ', dialCode: '+253', flagUrl: 'https://flagcdn.com/dj.svg'),
  Country(name: 'Dominica', code: 'DM', dialCode: '+1-767', flagUrl: 'https://flagcdn.com/dm.svg'),
  Country(name: 'Dominican Republic', code: 'DO', dialCode: '+1-809', flagUrl: 'https://flagcdn.com/do.svg'),
  Country(name: 'East Timor', code: 'TL', dialCode: '+670', flagUrl: 'https://flagcdn.com/tl.svg'),
  Country(name: 'Ecuador', code: 'EC', dialCode: '+593', flagUrl: 'https://flagcdn.com/ec.svg'),
  Country(name: 'Egypt', code: 'EG', dialCode: '+20', flagUrl: 'https://flagcdn.com/eg.svg'),
  Country(name: 'El Salvador', code: 'SV', dialCode: '+503', flagUrl: 'https://flagcdn.com/sv.svg'),
  Country(name: 'Equatorial Guinea', code: 'GQ', dialCode: '+240', flagUrl: 'https://flagcdn.com/gq.svg'),
  Country(name: 'Eritrea', code: 'ER', dialCode: '+291', flagUrl: 'https://flagcdn.com/er.svg'),
  Country(name: 'Estonia', code: 'EE', dialCode: '+372', flagUrl: 'https://flagcdn.com/ee.svg'),
  Country(name: 'Eswatini', code: 'SZ', dialCode: '+268', flagUrl: 'https://flagcdn.com/sz.svg'),
  Country(name: 'Ethiopia', code: 'ET', dialCode: '+251', flagUrl: 'https://flagcdn.com/et.svg'),
  Country(name: 'Fiji', code: 'FJ', dialCode: '+679', flagUrl: 'https://flagcdn.com/fj.svg'),
  Country(name: 'Finland', code: 'FI', dialCode: '+358', flagUrl: 'https://flagcdn.com/fi.svg'),
  Country(name: 'France', code: 'FR', dialCode: '+33', flagUrl: 'https://flagcdn.com/fr.svg'),
  Country(name: 'Gabon', code: 'GA', dialCode: '+241', flagUrl: 'https://flagcdn.com/ga.svg'),
  Country(name: 'Gambia', code: 'GM', dialCode: '+220', flagUrl: 'https://flagcdn.com/gm.svg'),
  Country(name: 'Georgia', code: 'GE', dialCode: '+995', flagUrl: 'https://flagcdn.com/ge.svg'),
  Country(name: 'Germany', code: 'DE', dialCode: '+49', flagUrl: 'https://flagcdn.com/de.svg'),
  Country(name: 'Ghana', code: 'GH', dialCode: '+233', flagUrl: 'https://flagcdn.com/gh.svg'),
  Country(name: 'Greece', code: 'GR', dialCode: '+30', flagUrl: 'https://flagcdn.com/gr.svg'),
  Country(name: 'Grenada', code: 'GD', dialCode: '+1-473', flagUrl: 'https://flagcdn.com/gd.svg'),
  Country(name: 'Guatemala', code: 'GT', dialCode: '+502', flagUrl: 'https://flagcdn.com/gt.svg'),
  Country(name: 'Guinea', code: 'GN', dialCode: '+224', flagUrl: 'https://flagcdn.com/gn.svg'),
  Country(name: 'Guinea-Bissau', code: 'GW', dialCode: '+245', flagUrl: 'https://flagcdn.com/gw.svg'),
  Country(name: 'Guyana', code: 'GY', dialCode: '+592', flagUrl: 'https://flagcdn.com/gy.svg'),
  Country(name: 'Haiti', code: 'HT', dialCode: '+509', flagUrl: 'https://flagcdn.com/ht.svg'),
  Country(name: 'Honduras', code: 'HN', dialCode: '+504', flagUrl: 'https://flagcdn.com/hn.svg'),
  Country(name: 'Hungary', code: 'HU', dialCode: '+36', flagUrl: 'https://flagcdn.com/hu.svg'),
  Country(name: 'Iceland', code: 'IS', dialCode: '+354', flagUrl: 'https://flagcdn.com/is.svg'),
  Country(name: 'India', code: 'IN', dialCode: '+91', flagUrl: 'https://flagcdn.com/in.svg'),
  Country(name: 'Indonesia', code: 'ID', dialCode: '+62', flagUrl: 'https://flagcdn.com/id.svg'),
  Country(name: 'Iran', code: 'IR', dialCode: '+98', flagUrl: 'https://flagcdn.com/ir.svg'),
  Country(name: 'Iraq', code: 'IQ', dialCode: '+964', flagUrl: 'https://flagcdn.com/iq.svg'),
  Country(name: 'Ireland', code: 'IE', dialCode: '+353', flagUrl: 'https://flagcdn.com/ie.svg'),
  Country(name: 'Israel', code: 'IL', dialCode: '+972', flagUrl: 'https://flagcdn.com/il.svg'),
  Country(name: 'Italy', code: 'IT', dialCode: '+39', flagUrl: 'https://flagcdn.com/it.svg'),
  Country(name: 'Jamaica', code: 'JM', dialCode: '+1-876', flagUrl: 'https://flagcdn.com/jm.svg'),
  Country(name: 'Japan', code: 'JP', dialCode: '+81', flagUrl: 'https://flagcdn.com/jp.svg'),
  Country(name: 'Jordan', code: 'JO', dialCode: '+962', flagUrl: 'https://flagcdn.com/jo.svg'),
  Country(name: 'Kazakhstan', code: 'KZ', dialCode: '+7', flagUrl: 'https://flagcdn.com/kz.svg'),
  Country(name: 'Kenya', code: 'KE', dialCode: '+254', flagUrl: 'https://flagcdn.com/ke.svg'),
  Country(name: 'Kiribati', code: 'KI', dialCode: '+686', flagUrl: 'https://flagcdn.com/ki.svg'),
  Country(name: 'Kosovo', code: 'XK', dialCode: '+383', flagUrl: 'https://flagcdn.com/xk.svg'),
  Country(name: 'Kuwait', code: 'KW', dialCode: '+965', flagUrl: 'https://flagcdn.com/kw.svg'),
  Country(name: 'Kyrgyzstan', code: 'KG', dialCode: '+996', flagUrl: 'https://flagcdn.com/kg.svg'),
  Country(name: 'Laos', code: 'LA', dialCode: '+856', flagUrl: 'https://flagcdn.com/la.svg'),
  Country(name: 'Latvia', code: 'LV', dialCode: '+371', flagUrl: 'https://flagcdn.com/lv.svg'),
  Country(name: 'Lebanon', code: 'LB', dialCode: '+961', flagUrl: 'https://flagcdn.com/lb.svg'),
  Country(name: 'Lesotho', code: 'LS', dialCode: '+266', flagUrl: 'https://flagcdn.com/ls.svg'),
  Country(name: 'Liberia', code: 'LR', dialCode: '+231', flagUrl: 'https://flagcdn.com/lr.svg'),
  Country(name: 'Libya', code: 'LY', dialCode: '+218', flagUrl: 'https://flagcdn.com/ly.svg'),
  Country(name: 'Liechtenstein', code: 'LI', dialCode: '+423', flagUrl: 'https://flagcdn.com/li.svg'),
  Country(name: 'Lithuania', code: 'LT', dialCode: '+370', flagUrl: 'https://flagcdn.com/lt.svg'),
  Country(name: 'Luxembourg', code: 'LU', dialCode: '+352', flagUrl: 'https://flagcdn.com/lu.svg'),
  Country(name: 'Madagascar', code: 'MG', dialCode: '+261', flagUrl: 'https://flagcdn.com/mg.svg'),
  Country(name: 'Malawi', code: 'MW', dialCode: '+265', flagUrl: 'https://flagcdn.com/mw.svg'),
  Country(name: 'Malaysia', code: 'MY', dialCode: '+60', flagUrl: 'https://flagcdn.com/my.svg'),
  Country(name: 'Maldives', code: 'MV', dialCode: '+960', flagUrl: 'https://flagcdn.com/mv.svg'),
  Country(name: 'Mali', code: 'ML', dialCode: '+223', flagUrl: 'https://flagcdn.com/ml.svg'),
  Country(name: 'Malta', code: 'MT', dialCode: '+356', flagUrl: 'https://flagcdn.com/mt.svg'),
  Country(name: 'Marshall Islands', code: 'MH', dialCode: '+692', flagUrl: 'https://flagcdn.com/mh.svg'),
  Country(name: 'Mauritania', code: 'MR', dialCode: '+222', flagUrl: 'https://flagcdn.com/mr.svg'),
  Country(name: 'Mauritius', code: 'MU', dialCode: '+230', flagUrl: 'https://flagcdn.com/mu.svg'),
  Country(name: 'Mexico', code: 'MX', dialCode: '+52', flagUrl: 'https://flagcdn.com/mx.svg'),
  Country(name: 'Micronesia', code: 'FM', dialCode: '+691', flagUrl: 'https://flagcdn.com/fm.svg'),
  Country(name: 'Moldova', code: 'MD', dialCode: '+373', flagUrl: 'https://flagcdn.com/md.svg'),
  Country(name: 'Monaco', code: 'MC', dialCode: '+377', flagUrl: 'https://flagcdn.com/mc.svg'),
  Country(name: 'Mongolia', code: 'MN', dialCode: '+976', flagUrl: 'https://flagcdn.com/mn.svg'),
  Country(name: 'Montenegro', code: 'ME', dialCode: '+382', flagUrl: 'https://flagcdn.com/me.svg'),
  Country(name: 'Morocco', code: 'MA', dialCode: '+212', flagUrl: 'https://flagcdn.com/ma.svg'),
  Country(name: 'Mozambique', code: 'MZ', dialCode: '+258', flagUrl: 'https://flagcdn.com/mz.svg'),
  Country(name: 'Myanmar', code: 'MM', dialCode: '+95', flagUrl: 'https://flagcdn.com/mm.svg'),
  Country(name: 'Namibia', code: 'NA', dialCode: '+264', flagUrl: 'https://flagcdn.com/na.svg'),
  Country(name: 'Nauru', code: 'NR', dialCode: '+674', flagUrl: 'https://flagcdn.com/nr.svg'),
  Country(name: 'Nepal', code: 'NP', dialCode: '+977', flagUrl: 'https://flagcdn.com/np.svg'),
  Country(name: 'Netherlands', code: 'NL', dialCode: '+31', flagUrl: 'https://flagcdn.com/nl.svg'),
  Country(name: 'New Zealand', code: 'NZ', dialCode: '+64', flagUrl: 'https://flagcdn.com/nz.svg'),
  Country(name: 'Nicaragua', code: 'NI', dialCode: '+505', flagUrl: 'https://flagcdn.com/ni.svg'),
  Country(name: 'Niger', code: 'NE', dialCode: '+227', flagUrl: 'https://flagcdn.com/ne.svg'),
  Country(name: 'Nigeria', code: 'NG', dialCode: '+234', flagUrl: 'https://flagcdn.com/ng.svg'),
  Country(name: 'North Korea', code: 'KP', dialCode: '+850', flagUrl: 'https://flagcdn.com/kp.svg'),
  Country(name: 'North Macedonia', code: 'MK', dialCode: '+389', flagUrl: 'https://flagcdn.com/mk.svg'),
  Country(name: 'Norway', code: 'NO', dialCode: '+47', flagUrl: 'https://flagcdn.com/no.svg'),
  Country(name: 'Oman', code: 'OM', dialCode: '+968', flagUrl: 'https://flagcdn.com/om.svg'),
  Country(name: 'Pakistan', code: 'PK', dialCode: '+92', flagUrl: 'https://flagcdn.com/pk.svg'),
  Country(name: 'Palau', code: 'PW', dialCode: '+680', flagUrl: 'https://flagcdn.com/pw.svg'),
  Country(name: 'Panama', code: 'PA', dialCode: '+507', flagUrl: 'https://flagcdn.com/pa.svg'),
  Country(name: 'Papua New Guinea', code: 'PG', dialCode: '+675', flagUrl: 'https://flagcdn.com/pg.svg'),
  Country(name: 'Paraguay', code: 'PY', dialCode: '+595', flagUrl: 'https://flagcdn.com/py.svg'),
  Country(name: 'Peru', code: 'PE', dialCode: '+51', flagUrl: 'https://flagcdn.com/pe.svg'),
  Country(name: 'Philippines', code: 'PH', dialCode: '+63', flagUrl: 'https://flagcdn.com/ph.svg'),
  Country(name: 'Poland', code: 'PL', dialCode: '+48', flagUrl: 'https://flagcdn.com/pl.svg'),
  Country(name: 'Portugal', code: 'PT', dialCode: '+351', flagUrl: 'https://flagcdn.com/pt.svg'),
  Country(name: 'Qatar', code: 'QA', dialCode: '+974', flagUrl: 'https://flagcdn.com/qa.svg'),
  Country(name: 'Romania', code: 'RO', dialCode: '+40', flagUrl: 'https://flagcdn.com/ro.svg'),
  Country(name: 'Russia', code: 'RU', dialCode: '+7', flagUrl: 'https://flagcdn.com/ru.svg'),
  Country(name: 'Rwanda', code: 'RW', dialCode: '+250', flagUrl: 'https://flagcdn.com/rw.svg'),
  Country(name: 'Saint Kitts and Nevis', code: 'KN', dialCode: '+1-869', flagUrl: 'https://flagcdn.com/kn.svg'),
  Country(name: 'Saint Lucia', code: 'LC', dialCode: '+1-758', flagUrl: 'https://flagcdn.com/lc.svg'),
  Country(name: 'Saint Vincent and the Grenadines', code: 'VC', dialCode: '+1-784', flagUrl: 'https://flagcdn.com/vc.svg'),
  Country(name: 'Samoa', code: 'WS', dialCode: '+685', flagUrl: 'https://flagcdn.com/ws.svg'),
  Country(name: 'San Marino', code: 'SM', dialCode: '+378', flagUrl: 'https://flagcdn.com/sm.svg'),
  Country(name: 'Sao Tome and Principe', code: 'ST', dialCode: '+239', flagUrl: 'https://flagcdn.com/st.svg'),
  Country(name: 'Saudi Arabia', code: 'SA', dialCode: '+966', flagUrl: 'https://flagcdn.com/sa.svg'),
  Country(name: 'Senegal', code: 'SN', dialCode: '+221', flagUrl: 'https://flagcdn.com/sn.svg'),
  Country(name: 'Serbia', code: 'RS', dialCode: '+381', flagUrl: 'https://flagcdn.com/rs.svg'),
  Country(name: 'Seychelles', code: 'SC', dialCode: '+248', flagUrl: 'https://flagcdn.com/sc.svg'),
  Country(name: 'Sierra Leone', code: 'SL', dialCode: '+232', flagUrl: 'https://flagcdn.com/sl.svg'),
  Country(name: 'Singapore', code: 'SG', dialCode: '+65', flagUrl: 'https://flagcdn.com/sg.svg'),
  Country(name: 'Slovakia', code: 'SK', dialCode: '+421', flagUrl: 'https://flagcdn.com/sk.svg'),
  Country(name: 'Slovenia', code: 'SI', dialCode: '+386', flagUrl: 'https://flagcdn.com/si.svg'),
  Country(name: 'Solomon Islands', code: 'SB', dialCode: '+677', flagUrl: 'https://flagcdn.com/sb.svg'),
  Country(name: 'Somalia', code: 'SO', dialCode: '+252', flagUrl: 'https://flagcdn.com/so.svg'),
  Country(name: 'South Africa', code: 'ZA', dialCode: '+27', flagUrl: 'https://flagcdn.com/za.svg'),
  Country(name: 'South Korea', code: 'KR', dialCode: '+82', flagUrl: 'https://flagcdn.com/kr.svg'),
  Country(name: 'South Sudan', code: 'SS', dialCode: '+211', flagUrl: 'https://flagcdn.com/ss.svg'),
  Country(name: 'Spain', code: 'ES', dialCode: '+34', flagUrl: 'https://flagcdn.com/es.svg'),
  Country(name: 'Sri Lanka', code: 'LK', dialCode: '+94', flagUrl: 'https://flagcdn.com/lk.svg'),
  Country(name: 'Sudan', code: 'SD', dialCode: '+249', flagUrl: 'https://flagcdn.com/sd.svg'),
  Country(name: 'Suriname', code: 'SR', dialCode: '+597', flagUrl: 'https://flagcdn.com/sr.svg'),
  Country(name: 'Sweden', code: 'SE', dialCode: '+46', flagUrl: 'https://flagcdn.com/se.svg'),
  Country(name: 'Switzerland', code: 'CH', dialCode: '+41', flagUrl: 'https://flagcdn.com/ch.svg'),
  Country(name: 'Syria', code: 'SY', dialCode: '+963', flagUrl: 'https://flagcdn.com/sy.svg'),
  Country(name: 'Taiwan', code: 'TW', dialCode: '+886', flagUrl: 'https://flagcdn.com/tw.svg'),
  Country(name: 'Tajikistan', code: 'TJ', dialCode: '+992', flagUrl: 'https://flagcdn.com/tj.svg'),
  Country(name: 'Tanzania', code: 'TZ', dialCode: '+255', flagUrl: 'https://flagcdn.com/tz.svg'),
  Country(name: 'Thailand', code: 'TH', dialCode: '+66', flagUrl: 'https://flagcdn.com/th.svg'),
  Country(name: 'Togo', code: 'TG', dialCode: '+228', flagUrl: 'https://flagcdn.com/tg.svg'),
  Country(name: 'Tonga', code: 'TO', dialCode: '+676', flagUrl: 'https://flagcdn.com/to.svg'),
  Country(name: 'Trinidad and Tobago', code: 'TT', dialCode: '+1-868', flagUrl: 'https://flagcdn.com/tt.svg'),
  Country(name: 'Tunisia', code: 'TN', dialCode: '+216', flagUrl: 'https://flagcdn.com/tn.svg'),
  Country(name: 'Turkey', code: 'TR', dialCode: '+90', flagUrl: 'https://flagcdn.com/tr.svg'),
  Country(name: 'Turkmenistan', code: 'TM', dialCode: '+993', flagUrl: 'https://flagcdn.com/tm.svg'),
  Country(name: 'Tuvalu', code: 'TV', dialCode: '+688', flagUrl: 'https://flagcdn.com/tv.svg'),
  Country(name: 'Uganda', code: 'UG', dialCode: '+256', flagUrl: 'https://flagcdn.com/ug.svg'),
  Country(name: 'Ukraine', code: 'UA', dialCode: '+380', flagUrl: 'https://flagcdn.com/ua.svg'),
  Country(name: 'United Arab Emirates', code: 'AE', dialCode: '+971', flagUrl: 'https://flagcdn.com/ae.svg'),
  Country(name: 'United Kingdom', code: 'GB', dialCode: '+44', flagUrl: 'https://flagcdn.com/gb.svg'),
  Country(name: 'United States', code: 'US', dialCode: '+1', flagUrl: 'https://flagcdn.com/us.svg'),
  Country(name: 'Uruguay', code: 'UY', dialCode: '+598', flagUrl: 'https://flagcdn.com/uy.svg'),
  Country(name: 'Uzbekistan', code: 'UZ', dialCode: '+998', flagUrl: 'https://flagcdn.com/uz.svg'),
  Country(name: 'Vanuatu', code: 'VU', dialCode: '+678', flagUrl: 'https://flagcdn.com/vu.svg'),
  Country(name: 'Vatican City', code: 'VA', dialCode: '+379', flagUrl: 'https://flagcdn.com/va.svg'),
  Country(name: 'Venezuela', code: 'VE', dialCode: '+58', flagUrl: 'https://flagcdn.com/ve.svg'),
  Country(name: 'Vietnam', code: 'VN', dialCode: '+84', flagUrl: 'https://flagcdn.com/vn.svg'),
  Country(name: 'Yemen', code: 'YE', dialCode: '+967', flagUrl: 'https://flagcdn.com/ye.svg'),
  Country(name: 'Zambia', code: 'ZM', dialCode: '+260', flagUrl: 'https://flagcdn.com/zm.svg'),
  Country(name: 'Zimbabwe', code: 'ZW', dialCode: '+263', flagUrl: 'https://flagcdn.com/zw.svg'),
];

/// The CountryPickerWidget shows a country picker button and a text field below it.
/// Tapping the button shows a search/filterable list of countries; on small screens it uses a bottom sheet,
/// while on larger screens it shows a dialog (simulating a dropdown).

/// GetX Controller to manage country selection and search filtering.
class CountryPickerController extends GetxController {
  // The currently selected country.
  Rx<Country?> selectedCountry = Rx<Country?>(null);
  // The list of countries filtered based on search input.
  RxList<Country> filteredCountries = <Country>[].obs;

  @override
  void onInit() {
    super.onInit();
    // Set Kenya as the default country, or fallback to the first country.
    selectedCountry.value = countriesList.firstWhere(
      (c) => c.name == 'Kenya',
      orElse: () => countriesList.first,
    );
    // Initially, show all countries.
    filteredCountries.assignAll(countriesList);
  }

  /// Filters the [countriesList] based on [query] (ignoring case).
  void filterCountries(String query) {
    if (query.trim().isEmpty) {
      filteredCountries.assignAll(countriesList);
    } else {
      filteredCountries.assignAll(
        countriesList
            .where((country) =>
                country.name.toLowerCase().contains(query.toLowerCase()))
            .toList(),
      );
    }
  }

  /// Update the selected country.
  void setSelectedCountry(Country country) {
    selectedCountry.value = country;
  }
}

/// The main CountryPickerWidget that contains a phone number TextField
/// with a country picker as the prefix.
class CountryPickerWidget extends StatelessWidget {
  final TextEditingController textEditingController;
  final Function(String)? onInputChanged;

  CountryPickerWidget({
    super.key,
    required this.textEditingController,
    this.onInputChanged,
  });

  // Instantiate (or retrieve) the controller.
  final CountryPickerController controller = Get.put(CountryPickerController());

  /// Opens the country picker – as a bottom sheet on small screens and as a dialog on larger screens.
  void openCountryPicker(BuildContext context) {
    const double breakpoint = 600;
    if (MediaQuery.of(context).size.width < breakpoint) {
      showModalBottomSheet(
        context: context,
        shape: const RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
        builder: (context) {
          return SizedBox(
            height: 400,
            child: CountryPickerContent(),
          );
        },
      );
    } else {
      showDialog(
        context: context,
        builder: (context) {
          return Dialog(
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: SizedBox(
              width: 300,
              height: 400,
              child: CountryPickerContent(),
            ),
          );
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => TextField(
          keyboardType: TextInputType.phone,
          controller: textEditingController,
          decoration: InputDecoration(
            contentPadding: const EdgeInsets.all(12),
            prefixIcon: GestureDetector(
              onTap: () => openCountryPicker(context),
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Display the selected country flag.
                    SvgPicture.network(
                      controller.selectedCountry.value!.flagUrl,
                      width: 24,
                      height: 24,
                      placeholderBuilder: (context) => const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    ),
                    const SizedBox(width: 8),
                    // Display the selected country dial code.
                    Text(
                      controller.selectedCountry.value!.dialCode,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const Icon(Icons.arrow_drop_down),
                  ],
                ),
              ),
            ),
            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            hintText: 'Enter phone number',
          ),
          onChanged: (val) {
            // Remove the leading zero if present.
            final value = val.startsWith('0') ? val.substring(1) : val;
            if (onInputChanged != null) {
              onInputChanged!("${controller.selectedCountry.value!.dialCode}$value");
            }
          },
        ));
  }
}

/// A separate widget for the country picker content (search field + country list).
class CountryPickerContent extends StatelessWidget {
  CountryPickerContent({super.key});

  final CountryPickerController controller = Get.find<CountryPickerController>();
  // Local controller for the search field.
  final TextEditingController searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Search Field
        Padding(
          padding: const EdgeInsets.all(12.0),
          child: TextField(
            controller: searchController,
            decoration: InputDecoration(
              hintText: 'Search country...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
            ),
            onChanged: (value) {
              controller.filterCountries(value);
            },
          ),
        ),
        const Divider(),
        // List of filtered countries.
        Expanded(
          child: Obx(() {
            return ListView.separated(
              itemCount: controller.filteredCountries.length,
              separatorBuilder: (_, __) => const Divider(),
              itemBuilder: (context, index) {
                final country = controller.filteredCountries[index];
                return ListTile(
                  leading: ClipRRect(
                    borderRadius: BorderRadius.circular(4),
                    child: SvgPicture.network(
                      country.flagUrl,
                      width: 32,
                      height: 32,
                      placeholderBuilder: (context) => const SizedBox(
                        width: 32,
                        height: 32,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    ),
                  ),
                  title: Text(country.name),
                  subtitle: Text(country.dialCode),
                  onTap: () {
                    controller.setSelectedCountry(country);
                    Navigator.pop(context);
                  },
                );
              },
            );
          }),
        ),
      ],
    );
  }
}
