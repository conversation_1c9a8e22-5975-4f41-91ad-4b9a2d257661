import 'dart:typed_data';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/controllers/events/create_event_controller.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/custom_logger.dart';
import 'package:onekitty/services/http_service.dart';
import 'package:dio/dio.dart' as dios;
import 'events_admin_controller.dart';

class BlockEventController extends GetxController {
  // Observable variables
  final isLoading = false.obs;
  final errorMessage = ''.obs;
  final reasonController = TextEditingController();
  final images = <Map<String, dynamic>>[].obs;
  final logger = Logger(filter: CustomLogFilter());
  final HttpService apiProvider = Get.find();
  final isUploadingImage = false.obs;
  RxString apiMessage = ''.obs;
  Future<String> uploadFileWeb({
    required Uint8List bytes,
    required String fileName,
  }) async {
    try {
      // Create form data for file upload
      final formData = dios.FormData.fromMap({
        'file' : await dios.MultipartFile.fromBytes(bytes, filename: fileName),
        // 'file': MultipartFile(, filename: fileName),
        'bucket': 'onekitty',
      });

      final response = await apiProvider.request(
        method: Method.POST,
        url: ApiUrls.UPLOADFILE,
        formdata: formData,
      );

      if (response.data == null) {
        throw Exception('Server returned null response');
      }

      if (response.data['status'] == true && response.data['data'] != null) {
        return response.data['data']['file_path'] ?? '';
      } else {
        throw Exception(response.data['message'] ?? 'Failed to upload file');
      }
    } catch (e) {
      if (e.toString().contains('No internet connection')) {
        ToastUtils.showToast('Please check your internet connection');
      } else {
        ToastUtils.showToast('Failed to upload file. Please try again.');
      }
      logger.e('Error uploading file: $e');
      throw Exception('Failed to upload file');
    }
  }

  Future pickImages() async {
    isUploadingImage(true);
    try {
      FilePickerResult? image = await FilePicker.platform
          .pickFiles(type: FileType.image, allowMultiple: false);

      if (image != null && image.files.isNotEmpty) {
        final _eventsController = Get.put(CreateEventController());
        final file = image.files.first;

        if (GetPlatform.isWeb) {
          if (file.bytes != null) {
            final url = await uploadFileWeb(
                bytes: file.bytes!,
                fileName:
                    "${DateTime.now().millisecondsSinceEpoch}${file.name.replaceAll(' ', '')}");
            images.add({"path": file.bytes, "url": url, "type": "Image"});
            debugPrint('$images');
          } else {
            ToastUtils.showToast('Invalid file data');
            return false;
          }
        } else {
          if (file.path != null) {
            final url = await _eventsController.uploadFile(
                path: file.path!,
                fileName:
                    "${DateTime.now().millisecondsSinceEpoch}${file.name}");
            images.add({"path": file.path, "url": url, "type": "Image"});
          } else {
            ToastUtils.showToast('Invalid file path');
            return false;
          }
        }
      } else {
        ToastUtils.showToast('No image selected');
        return false;
      }
    } catch (e) {
      logger.e('General error in pickImage: $e');
      ToastUtils.showToast('Failed to process image. Please try again.');
      return false;
    } finally {
      isUploadingImage(false);
    }
  }

  // Submit blocking event
  void submitBlock(int eventId) async {
    try {
      isLoading(true);
      if (reasonController.text.isEmpty) {
        Get.snackbar('Error', 'Please provide a reason');
        return;
      }

      // Prepare media array from uploaded images
      final mediaList = images
          .map((image) => {"url": image["url"], "type": "Image"})
          .toList();

      var response = await apiProvider.request(
        method: Method.POST,
        url: ApiUrls.blockEvent,
        params: {
          "event_status": "BLOCKED",
          "event_id": eventId,
          "media": mediaList,
          "description": reasonController.text
        },
      );

      if (response.data['status'] ?? false) {
        Get.find<EventsAdminController>()
            .events
            .removeWhere((event) => event.id == eventId);
        Get.back();
        Get.snackbar('Success', 'Event blocked successfully');
      } else {
        throw Exception(response.data['message'] ?? 'Failed to block event');
      }
    } catch (e) {
      logger.e('Error blocking event: $e');
      Get.snackbar('Error', 'Failed to block event: $e');
    } finally {
      isLoading(false);
    }
  }

  // Handle unblocking event
  Future<void> unblockEvent(int eventId) async {
    try {
      isLoading.value = true;
      var response = await apiProvider.request(
        method: Method.POST,
        url: ApiUrls.blockEvent,
        params: {
          "event_status": "ACTIVE",
          "event_id": eventId,
        },
      );
      if (response.data['status'] ?? false) {
        Get.back();
        Get.snackbar('Success', 'Event unblocked successfully');
      } else {
        throw Exception(response.data['message'] ?? 'Failed to unblock event');
      }
    } catch (e) {
      errorMessage.value = e.toString();
    } finally {
      isLoading.value = false;
    }
  }

  // Clear error message
  void clearError() {
    errorMessage.value = '';
  }
}
