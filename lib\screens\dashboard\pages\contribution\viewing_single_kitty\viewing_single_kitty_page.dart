import 'dart:async';
import 'dart:io';
import 'dart:ui';
import 'package:date_time_format/date_time_format.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_quill/flutter_quill.dart' as q;
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:onekitty/controllers/events/controllers.dart';
import 'package:onekitty/controllers/events/create_event_controller.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/controllers/kitty_settings.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/connectivity_wrapper.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/edit_kitty/whatsapp_link.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/viewing_single_kitty/widgets/whatsApp_groups_widget.dart';
import 'package:onekitty/utils/date_formatter.dart';
import 'package:onekitty/utils/my_quill_editor.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/models/auth/user_model.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/viewing_single_kitty/widgets/services_widget.dart';
import 'package:onekitty/utils/size_config.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:share_plus/share_plus.dart';
import '../../../../../utils/utils_exports.dart';
import 'widgets/kitty_flexible_spacebar.dart';
import 'widgets/kitty_transactions_widget.dart';
import 'package:flutter/material.dart';
// ignore: library_prefixes
import 'widgets/beneficiary_card.dart' as beneficiaryCard;
import 'package:onekitty/main.dart' as main;

class ViewingSingleKittyScreen extends StatefulWidget {
  const ViewingSingleKittyScreen({
    super.key,
  });

  static onRefresh() async {
    final UserKittyController controller = Get.find();
    final ContributeController getKittyController =
        Get.put(ContributeController());
    final DataController dataController = Get.put(DataController());
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      controller.getLocalUser();
      getKittyController.kittyMedia.clear();
      await getKittyController.getKitty(
          id: dataController.kitty.value.kitty?.id);
      Get.put(SettingsController()).fetchSettings(
        dataController.kitty.value.kitty?.id ?? 0,
      );
    });
  }

  @override
  State<ViewingSingleKittyScreen> createState() =>
      _ViewingSingleKittyScreenState();
}

class _ViewingSingleKittyScreenState extends State<ViewingSingleKittyScreen>
    with TickerProviderStateMixin {
  final DataController dataController = Get.put(DataController());
  late TabController tabController;
  GlobalKey globalKey = GlobalKey();
  UserModelLatest user = UserModelLatest();
  q.QuillController quillController = q.QuillController.basic();
  final activeIndex = 0.obs;
  final UserKittyController controller = Get.find();
  final extendedScreen = 300.0.obs;
  int? kittyId;

  final KittyController kittyController = Get.put(KittyController());
  final ContributeController getKittyController =
      Get.put(ContributeController());
  final KittyDataController kittyData = Get.put(KittyDataController());
  String greeting = getGreeting();
  DateTime tagetDate = DateTime.now().add(const Duration(days: 1));
  final carouselController = CarouselController();

  final RefreshController _refreshController =
      RefreshController(initialRefresh: true);
  void onRefresh() async {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      controller.getLocalUser();
      getKittyController.kittyMedia.clear();
      await getKittyController.getKitty(
          id: dataController.kitty.value.kitty?.id);
      Get.put(SettingsController()).fetchSettings(
        dataController.kitty.value.kitty?.id ?? 0,
      );
      _refreshController.refreshCompleted();
    });
  }

  @override
  void initState() {
    onRefresh();
    tabController = TabController(length: 2, vsync: this);
    tabController.addListener(() {
      extendedScreen.value = tabController.index == 0
          ? SizeConfig.screenHeight * 0.5
          : SizeConfig.screenHeight * 0.8;
    });
    super.initState();
  }


  final _eventsController = Get.find<CreateEventController>();

  Future<void> _captureAndSharePng() async {
    try {
      RenderRepaintBoundary boundary =
          globalKey.currentContext?.findRenderObject() as RenderRepaintBoundary;

      var image = await boundary.toImage(pixelRatio: 5.0);

      ByteData byteData =
          await image.toByteData(format: ImageByteFormat.png) as ByteData;
      Uint8List pngBytes = byteData.buffer.asUint8List();

      final tempDir = await getTemporaryDirectory();
      final file = await File('${tempDir.path}/image.png').create();
      await file.writeAsBytes(pngBytes);

      await Share.shareXFiles(
        [XFile(file.path)],
      );
    } catch (e) {
      if (kDebugMode) {
        print(e.toString());
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: ConnectivityCheck(
        child: Scaffold(
          // backgroundColor:
          //     main.isLight.value ? appTheme.gray50 : Colors.grey[900],
          //appBar: buildAppBar(context),
          body: SmartRefresher(
            onRefresh: onRefresh,
            controller: _refreshController,
            child: CustomScrollView(
              slivers: [
                Obx(
                  () => SliverAppBar(
                    leadingWidth: 80.w,
                    leading: InkWell(
                      onTap: () => Navigator.pop(context),
                      child: Container(
                          decoration: BoxDecoration(
                            color: Colors.black45,
                            borderRadius: BorderRadius.circular(25),
                          ),
                          margin: const EdgeInsets.all(6),
                          padding: const EdgeInsets.all(4),
                          alignment: Alignment.center,
                          child: const Row(children: [
                            Icon(Icons.navigate_before, color: Colors.white),
                            Text('Back', style: TextStyle(color: Colors.white))
                          ])),
                    ),
                    expandedHeight:
                        getKittyController.kittyMedia.isEmpty ? 0 : 250,
                    backgroundColor: Theme.of(context).scaffoldBackgroundColor,
                    pinned: true,
                    flexibleSpace: Obx(() =>
                        getKittyController.kittyMedia.isEmpty
                            ? const SizedBox()
                            : KittyFlexibleSpacebar()),
                    actions: getKittyController.kittyMedia.isEmpty
                        ? [
                            SizedBox(width: 8.w),
                            InkWell(
                              onTap: () async {
                                if (kittyController.isUploadingImage.value) {
                                  return;
                                }
                                kittyController.pickImage(
                                    context: context,
                                    kittyId:
                                        dataController.kitty.value.kitty?.id ??
                                            0,
                                    name: dataController
                                            .kitty.value.kitty?.title ??
                                        '');
                              },
                              child: Obx(
                                () => kittyController.isUploadingImage.value
                                    ? const CircleAvatar(
                                        backgroundColor: Colors.black38,
                                        child: CircularProgressIndicator(
                                            backgroundColor: Colors.white))
                                    : const CircleAvatar(
                                        backgroundColor: Colors.black38,
                                        child: Icon(Icons.add_a_photo_outlined,
                                            color: Colors.white)),
                              ),
                            ),
                            SizedBox(width: 8.w),
                            InkWell(
                              onTap: () async {
                                String shareMsg =
                                    "Kitty Title: ${dataController.kitty.value.kitty?.title ?? ""}\nClick: https://onekitty.co.ke/kitty/${dataController.kitty.value.kitty?.id ?? 0}\n to Pay";
                                await Share.share(shareMsg,
                                    subject: 'Kitty Details');
                              },
                              child: const CircleAvatar(
                                  backgroundColor: Colors.black38,
                                  child:
                                      Icon(Icons.share, color: Colors.white)),
                            ),
                            SizedBox(width: 8.w),
                            InkWell(
                              onTap: () {
                                showModalBottomSheet(
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(10.0),
                                  ),
                                  context: context,
                                  isScrollControlled: true,
                                  constraints: BoxConstraints(
                                    maxHeight: SizeConfig.screenHeight * .8,
                                    maxWidth: SizeConfig.screenWidth,
                                  ),
                                  builder: (_) => SizedBox(
                                    height: SizeConfig.screenHeight * .7,
                                    width: SizeConfig.screenWidth,
                                    child: Column(
                                      children: [
                                        const SizedBox(
                                          height: 10,
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.all(15.0),
                                          child: RepaintBoundary(
                                            key: globalKey,
                                            child: Container(
                                              margin:
                                                  const EdgeInsets.all(20.0),
                                              color: Colors.white,
                                              child: Column(
                                                children: [
                                                  const Text(
                                                    "Scan to Pay",
                                                    style: TextStyle(
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      fontSize: 20,
                                                    ),
                                                  ),
                                                  QrImageView(
                                                    padding:
                                                        const EdgeInsets.all(
                                                            10.0),
                                                    data:
                                                        "onekitty.co.ke/kitty/${dataController.kitty.value.kitty?.id}",
                                                    version: QrVersions.auto,
                                                    gapless: false,
                                                    errorCorrectionLevel:
                                                        QrErrorCorrectLevel.H,
                                                    embeddedImage: AssetImage(
                                                        AssetUrl.logo4),
                                                    embeddedImageStyle:
                                                        const QrEmbeddedImageStyle(
                                                      size: Size(80, 80),
                                                    ),
                                                    size: 250.0,
                                                  ),
                                                  const SizedBox(
                                                    height: 5.0,
                                                  ),
                                                  Text(
                                                    "  onekitty.co.ke/kitty/${dataController.kitty.value.kitty?.id} ",
                                                    style: const TextStyle(
                                                      color: Colors.blue,
                                                      fontSize: 16,
                                                    ),
                                                  ),
                                                  const SizedBox(height: 10),
                                                  Text(
                                                    dataController.kitty.value
                                                            .kitty?.title ??
                                                        "\n",
                                                    style: const TextStyle(
                                                        fontSize: 17),
                                                  )
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                        Container(
                                          margin: const EdgeInsets.only(
                                              right: 10.0),
                                          child: ElevatedButton(
                                              style: ElevatedButton.styleFrom(
                                                backgroundColor:
                                                    Theme.of(context)
                                                        .colorScheme
                                                        .primary,
                                                fixedSize: const Size(250, 20),
                                              ),
                                              child: Text(
                                                "share".tr,
                                                style: const TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 15,
                                                ),
                                              ),
                                              onPressed: () async {
                                                _captureAndSharePng();
                                              }),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                              child: const CircleAvatar(
                                  backgroundColor: Colors.black38,
                                  child: Icon(Icons.qr_code_2,
                                      color: Colors.white)),
                            ),
                          ]
                        : [],
                  ),
                ),
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 22.0, vertical: 8),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Expanded(
                              child: Text(
                                dataController.kitty.value.kitty?.title ?? "",
                                style: TextStyle(
                                    color: main.isLight.value
                                        ? Colors.black
                                        : appTheme.whiteA700,
                                    fontWeight: FontWeight.w700,
                                    fontSize: 22),
                                overflow: TextOverflow.visible,
                              ),
                            ),
                            Text(
                                FormattedCurrency().getFormattedCurrency(
                                    dataController.kitty.value.kitty?.balance),
                                style: CustomTextStyles.titleMediumSemiBold),
                          ],
                        ),
                        ListTile(
                          contentPadding: const EdgeInsets.all(2),
                          leading: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10),
                                color: primaryColor.withOpacity(0.15),
                              ),
                              child: Image.asset(
                                  'assets/images/icons/calendar.png',
                                  height: 30,
                                  width: 30,
                                  color: primaryColor)),
                          subtitle: Text(
                            DateTimeFormat.relative(
                                dataController.kitty.value.kitty?.endDate ??
                                    DateTime.now(),
                                levelOfPrecision: 1,
                                prependIfBefore: 'Ends In',
                                ifNow: "Now",
                                appendIfAfter: 'ago'),
                            style: TextStyle(
                              color: main.isLight.value
                                  ? appTheme.gray90001
                                  : appTheme.whiteA70001,
                              fontStyle: FontStyle.italic,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          trailing: Chip(
                            label: Text(
                              dataController.kitty.value.kittyStatus ?? "",
                              style: TextStyle(
                                color: getkittyStatusColor(
                                    dataController.kitty.value.kittyStatus ??
                                        ""),
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            backgroundColor: Colors.transparent,
                            side: const BorderSide(color: AppColors.primary),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(25),
                            ),
                          ),
                          title: Text(
                            "Created: ${formatDate("${dataController.kitty.value.kitty?.createdAt}")}",
                            style: TextStyle(
                              fontSize: 14.spMin,
                              fontWeight: FontWeight.w500,
                              color: main.isLight.value
                                  ? appTheme.gray90001
                                  : appTheme.whiteA70001,
                            ),
                          ),
                        ),
                        Align(
                          alignment: Alignment.topLeft,
                          child: Text(
                            'About: ',
                            style: TextStyle(
                                fontSize: 14.spMin,
                                color: main.isLight.value
                                    ? appTheme.gray900
                                    : appTheme.whiteA700,
                                fontWeight: FontWeight.w600),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SliverToBoxAdapter(
                    child: Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.0.w, vertical: 4.h),
                  child: QuillEditorWidget(
                      // readMore: true,
                      text:
                          dataController.kitty.value.kitty?.description ?? ""),
                )),
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0),
                    child: Column(
                      children: [
                        const beneficiaryCard.BeneficiaryCard(),
                        SizedBox(height: 8.h),
                        _buildRow(context),
                        SizedBox(height: 6.h),
                        _buildFrame1(context),
                        SizedBox(height: 8.h),
                        _buildTabs(context),
                      ],
                    ),
                  ),
                ),
                // const SliverToBoxAdapter(child: SizedBox(height: 50)),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // /// Section Widget
  // Widget _buildActive(BuildContext context) {
  //   return CustomOutlinedButton(
  //     height: 20.h,
  //     // width: 80.w,
  //     text: dataController.kitty.value.kittyStatus ?? "",
  //     buttonStyle: CustomButtonStyles.none,
  //     buttonTextStyle: TextStyle(
  //       color:
  //           getkittyStatusColor(dataController.kitty.value.kittyStatus ?? ""),
  //       fontWeight: FontWeight.bold,
  //     ),
  //   );
  // }

  /// Section Widget
  Widget _buildFrame(BuildContext context) {
    final endDate = dataController.kitty.value.kitty?.endDate;
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 10.h),
      decoration: AppDecoration.shadow1
          .copyWith(borderRadius: BorderRadiusStyle.roundedBorder6),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(height: 13.h),
          Row(
            children: [
              CustomImageView(
                  imagePath: AssetUrl.imgClock, height: 24.h, width: 24.w),
              Expanded(
                child: Padding(
                  padding: EdgeInsets.only(left: 4.w, top: 4.h),
                  child: RichText(
                    overflow: TextOverflow.ellipsis,
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text:
                              "End Date: ${DateTimeFormat.format(endDate?.toLocal() ?? DateTime.now(), format: 'D, M j, H:i A')}\n",
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontStyle: FontStyle.normal,
                            color: appTheme.gray90001,
                          ),
                        ),
                        TextSpan(
                          text: DateTimeFormat.relative(
                            endDate?.toLocal() ?? DateTime.now(),
                            levelOfPrecision: 3,
                            prependIfBefore: 'Ends In',
                            ifNow: "Now",
                            appendIfAfter: 'ago',
                          ),
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            fontStyle: FontStyle.italic,
                            color: appTheme.gray90001,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 7.h),
          dataController.kitty.value.kitty?.settlementType == 2
              ? Text(
                  "Beneficiary: ${dataController.kitty.value.kitty?.beneficiaryAccount} ${dataController.kitty.value.kitty?.bennefAccRef ?? ''}  ${getNetworkName(dataController.kitty.value.kitty?.beneficiaryChannel ?? "")} (${getSettlementType(dataController.kitty.value.kitty?.settlementType ?? 0)})",
                  style: CustomTextStyles.bodySmallGray900_1)
              : Text(
                  "Beneficiary: ${dataController.kitty.value.kitty?.beneficiaryAccount} ${getNetworkName(dataController.kitty.value.kitty?.beneficiaryChannel ?? "")} (${getSettlementType(dataController.kitty.value.kitty?.settlementType ?? 0)})",
                  style: CustomTextStyles.bodySmallGray900_1)
        ],
      ),
    );
  }

  /// Section Widget
  Widget _buildRow(BuildContext context) {
    final contributeController = Get.find<ContributeController>();
    return Obx(() =>
        Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          Align(
            alignment: Alignment.topLeft,
            child: Text(
              'Connected WhatsApp groups: ',
              style: TextStyle(
                  fontSize: 14.spMin,
                  color: main.isLight.value ? Colors.black : Colors.white,
                  fontWeight: FontWeight.w600),
            ),
          ),
          contributeController.whatsappList.isNotEmpty &&
                  !contributeController.isgetloading.value
              ? Row(
                  children: [
                    CustomImageView(
                      imagePath: AssetUrl.addGif,
                      height: 15.h,
                      width: 15.w,
                    ),
                    Padding(
                        padding: EdgeInsets.only(left: 2.w),
                        child: InkWell(
                            onTap: () {
                              Get.to(() => const WhatsAppEditLink());
                            },
                            child: Text("Add Group",
                                style: CustomTextStyles.titleSmallIndigo500)))
                  ],
                )
              : const SizedBox(),
        ]));
  }

  /// Section Widget
  Widget _buildFrame1(BuildContext context) {
    return GetX(
      init: ContributeController(),
      initState: (state) {
        Future.delayed(Duration.zero, () async {
          try {
            await state.controller
                ?.getWhatsapp(id: dataController.kitty.value.kitty?.id ?? 0);
            // print("Whatsapp accounts loaded successfully.");
            // print("KITTY ID IS: ${dataController.kittyId.value}");
          } catch (e) {
            print("Error loading whatsapp accounts: $e");
          }
        });
      },
      builder: (ContributeController contributeController) {
        if (contributeController.isgetloading.isTrue) {
          return SizedBox(
            height: SizeConfig.screenHeight * .1,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SpinKitDualRing(
                    color: ColorUtil.blueColor,
                    lineWidth: 4.sp,
                    size: 40.0.sp,
                  ),
                  const Text(
                    "loading..",
                    style: TextStyle(
                      color: Colors.white,
                    ),
                  )
                ],
              ),
            ),
          );
        } else if (contributeController.whatsappList.isEmpty) {
          return SizedBox(
            height: 75.h,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text(
                  "You don't have any whatsapp groups",
                  // style: context.dividerTextLarge,
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 4.h),
                TextButton.icon(
                  icon: Image.asset('assets/images/whatsapp.png',
                      height: 24, width: 24, fit: BoxFit.cover),
                  label: const Text('Add Group'),
                  onPressed: () {
                    Get.to(() => const WhatsAppEditLink());
                  },
                )
              ],
            ),
          );
        } else if (contributeController.whatsappList.isNotEmpty) {
          return ListView.separated(
            shrinkWrap: true,
            separatorBuilder: (context, index) {
              return SizedBox(height: 8.h);
            },
            itemCount: contributeController.whatsappList.length,
            itemBuilder: (context, index) {
              final whatsapp = contributeController.whatsappList[index];
              return WhatsAppWidget(
                whatsappName: whatsapp.whatsappGroupName ?? "",
                whatsapp: whatsapp,
                whatsappProfile: whatsapp.whatsAppProfile ?? "",
              );
            },
          );
        }
        return Text(
          "You don't have any whatsapp groups yet, click the 'Add Group' button to add.",
          style: context.dividerTextLarge,
          textAlign: TextAlign.center,
        );
      },
    );
  }

  Widget _buildTabs(BuildContext context) {
    return Column(
      children: [
        Column(
          children: [
            Container(
              padding: EdgeInsets.all(5.h),
              decoration: AppDecoration.fillSlate.copyWith(
                borderRadius: BorderRadiusStyle.roundedBorder6,
              ),
              child: TabBar(
                physics: const ClampingScrollPhysics(),
                //padding: const EdgeInsets.only(left: 5, right: 5),
                unselectedLabelColor: Colors.black,
                labelColor: Theme.of(context).primaryColor,
                indicatorSize: TabBarIndicatorSize.tab,
                dividerColor: Colors.transparent,
                controller: tabController,
                indicator: BoxDecoration(
                    borderRadius: BorderRadius.circular(5),
                    color: Colors.white),
                tabs: [
                  Tab(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 1.w),
                      child: const Text("Services"),
                    ),
                  ),
                  Tab(
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 1.w),
                      child: const Text("Transactions"),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(
              height: 10.h,
            ),
            Obx(
              () => SizedBox(
                height: extendedScreen.value,
                child: Column(
                  children: [
                    Expanded(
                      child: TabBarView(
                        controller: tabController,
                        children: [
                          const ServicesWidget(),
                          TransactionWidget(),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            )
          ],
        )
      ],
    );
  }
}

Color getkittyStatusColor(String status) {
  switch (status.toLowerCase()) {
    case "active":
      return const Color(0xFF56AF57);
    case "completed":
      return const Color(0xFF56AF57);
    case "settlement initiated":
      return const Color.fromARGB(255, 206, 104, 192);

    default:
      return const Color(0xFFEE5B60);
  }
}

String getGreeting() {
  var hour = DateTime.now().hour;
  if (hour < 12) {
    return "Good morning";
  } else if (hour < 18) {
    return "Good afternoon";
  } else {
    return "Good evening";
  }
}

String getSettlementType(int type) {
  switch (type) {
    case 0:
      return "Wallet";
    case 1:
      return "TILL";
    case 2:
      return "PAYBILL";
    case 3:
      return "BANK";
    default:
      return "Invalid input";
  }
}

String getNetworkName(String code) {
  switch (code) {
    case "0":
      return "Sasapay";
    case "1":
      return "Onekitty";
    case "63902":
      return "M-Pesa";
    case "63903":
      return "Airtel Money";
    case "63907":
      return "T-kash";
    case "55":
      return "Visa";
    default:
      return "UNKNOWN";
  }
}
