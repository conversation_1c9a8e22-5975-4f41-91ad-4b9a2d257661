import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/utils/my_text_field.dart';

import '../map_page.dart';

class TimeAndLocation extends StatelessWidget {
  final TextEditingController eventStartDate,
      referralCode,
      eventEndDate,
      venue,
      location;
  final GlobalKey<FormState> formKey;
  const TimeAndLocation({
    super.key,
    required this.eventStartDate,
    required this.eventEndDate,
    required this.venue,
    required this.location,
    required this.formKey,
    required this.referralCode,
  });

  @override
  Widget build(BuildContext context) {
    return GetX<TimeAndLocationController>(builder: (controller) {
      return Form(
          key: formKey,
          child: SingleChildScrollView(
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                SizedBox(height: 8.h),
                MyTextFieldwValidator(
                  readOnly: true,
                  onTap: () async {
                    DateTime? pickedDateTime = await showDatePicker(
                      context: context,
                      initialDate: DateTime.now(),
                      firstDate: DateTime.now(),
                      lastDate: DateTime.now().add(const Duration(days: 365)),
                    );

                    if (pickedDateTime != null) {
                      TimeOfDay? pickedTime = await showTimePicker(
                        context: context,
                        initialTime: TimeOfDay.now(),
                      );

                      if (pickedTime != null) {
                        DateTime finalDateTime = DateTime(
                          pickedDateTime.year,
                          pickedDateTime.month,
                          pickedDateTime.day,
                          pickedTime.hour,
                          pickedTime.minute,
                        );

                        String formattedDateTime =
                            DateFormat('dd/MM/yyyy HH:mm a')
                                .format(finalDateTime);
                        eventStartDate.text = formattedDateTime;
                      }
                    }
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Event Start Date is required';
                    }
                    return null;
                  },
                  controller: eventStartDate,
                  titleStyle: TextStyle(
                      fontSize: 14.spMin, fontWeight: FontWeight.w500),
                  iconSuffix: IconButton(
                    icon: const Icon(Icons.calendar_month),
                    onPressed: () async {
                      DateTime? pickedDateTime = await showDatePicker(
                        context: context,
                        initialDate: DateTime.now(),
                        firstDate: DateTime.now(),
                        lastDate: DateTime.now().add(const Duration(days: 365)),
                      );

                      if (pickedDateTime != null) {
                        TimeOfDay? pickedTime = await showTimePicker(
                          context: context,
                          initialTime: TimeOfDay.now(),
                        );

                        if (pickedTime != null) {
                          DateTime finalDateTime = DateTime(
                            pickedDateTime.year,
                            pickedDateTime.month,
                            pickedDateTime.day,
                            pickedTime.hour,
                            pickedTime.minute,
                          );

                          String formattedDateTime =
                              DateFormat('dd/MM/yyyy HH:mm a')
                                  .format(finalDateTime);
                          eventStartDate.text = formattedDateTime;
                        }
                      }
                    },
                  ),
                  hint: '13/2/2024 2:00 PM',
                  title: 'Event Start Date and Time',
                ),
                SizedBox(height: 8.h),
                MyTextFieldwValidator(
                  readOnly: true,
                  onTap: () async {
                    DateTime? pickedDateTime = await showDatePicker(
                      context: context,
                      initialDate: DateTime.now(),
                      firstDate: DateTime.now(),
                      lastDate: DateTime.now().add(const Duration(days: 365)),
                    );

                    if (pickedDateTime != null) {
                      TimeOfDay? pickedTime = await showTimePicker(
                        context: context,
                        initialTime: TimeOfDay.now(),
                      );

                      if (pickedTime != null) {
                        DateTime finalDateTime = DateTime(
                          pickedDateTime.year,
                          pickedDateTime.month,
                          pickedDateTime.day,
                          pickedTime.hour,
                          pickedTime.minute,
                        );

                        String formattedDateTime =
                            DateFormat('dd/MM/yyyy HH:mm a')
                                .format(finalDateTime);
                        eventEndDate.text = formattedDateTime;
                      }
                    }
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Event End Date is required';
                    }
                    return null;
                  },
                  controller: eventEndDate,
                  titleStyle: TextStyle(
                      fontSize: 14.spMin, fontWeight: FontWeight.w500),
                  iconSuffix: IconButton(
                    icon: const Icon(Icons.calendar_month),
                    onPressed: () async {
                      DateTime? pickedDateTime = await showDatePicker(
                        context: context,
                        initialDate: DateTime.now(),
                        firstDate: DateTime.now(),
                        lastDate: DateTime.now().add(const Duration(days: 365)),
                      );

                      if (pickedDateTime != null) {
                        TimeOfDay? pickedTime = await showTimePicker(
                          context: context,
                          initialTime: TimeOfDay.now(),
                        );

                        if (pickedTime != null) {
                          DateTime finalDateTime = DateTime(
                            pickedDateTime.year,
                            pickedDateTime.month,
                            pickedDateTime.day,
                            pickedTime.hour,
                            pickedTime.minute,
                          );

                          String formattedDateTime =
                              DateFormat('dd/MM/yyyy HH:mm a')
                                  .format(finalDateTime);
                          eventEndDate.text = formattedDateTime;
                        }
                      }
                    },
                  ),
                  hint: '13/2/2024 2:00 PM',
                  title: 'Event End Date and Time',
                ),
                Text(
                  'Event Type',
                  style: TextStyle(
                      fontSize: 14.spMin, fontWeight: FontWeight.w600),
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Column(
                        children: [
                          TextButton.icon(
                              onPressed: () {
                                controller.eventType.value =
                                    0; // Update the value directly
                              },
                              label: const Text('Physical event'),
                              icon: Icon(controller.eventType.value == 0
                                  ? Icons.radio_button_checked
                                  : Icons.radio_button_off)),
                        ],
                      ),
                    ),
                    Expanded(
                      child: TextButton.icon(
                          onPressed: () {
                            controller.eventType.value =
                                1; // Update the value directly
                          },
                          label: const Text('Online event'),
                          icon: Icon(controller.eventType.value == 1
                              ? Icons.radio_button_checked
                              : Icons.radio_button_off)),
                    )
                  ],
                ),
                MyTextFieldwValidator(
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Event Venue is required';
                      }
                      return null;
                    },
                    controller: venue,
                    titleStyle: const TextStyle(
                        fontSize: 14, fontWeight: FontWeight.w500),
                    title: 'Venue',
                    hint: 'e.g. KICC'),
                OutlinedButton(
                  onPressed: () async {
                    final results = await Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const MapScreen(),
                      ),
                    );
                    if (results != null) {
                      controller.mapCoordinates = {
                        "lat": double.parse(results['latitude'].toString()),
                        "long": double.parse(results['longitude'].toString())
                      }.obs;
                    }
                  },
                  child: const Text('Choose on Map'),
                ),
                MyTextFieldwValidator(
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Location is required';
                      }
                      return null;
                    },
                    controller: location,
                    titleStyle: const TextStyle(
                        fontSize: 14, fontWeight: FontWeight.w500),
                    title: 'Location Tip',
                    hint: 'Enter location'),
                MyTextFieldwValidator(
                    controller: referralCode,
                    titleStyle: const TextStyle(
                        fontSize: 14, fontWeight: FontWeight.w500),
                    title: 'referral code',
                    hint: 'Enter referral code'),
              ])));
    });
  }
}

class TimeAndLocationController extends GetxController implements GetxService {
  RxInt eventType = 0.obs;
  var mapCoordinates = {"lat": 0.0, "long": 0.0}.obs;
}
