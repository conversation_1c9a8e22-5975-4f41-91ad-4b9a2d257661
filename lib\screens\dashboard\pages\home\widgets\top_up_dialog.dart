import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/cardPayment.dart';
import 'package:onekitty/screens/widgets/payment_radio.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import '../../../../../controllers/user_ktty_controller.dart';

import '../../../../../utils/utils_exports.dart';

// ignore_for_file: must_be_immutable
class TopUp extends StatefulWidget {
  const TopUp({super.key});

  @override
  State<TopUp> createState() => _TopUp();
}

class _TopUp extends State<TopUp> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  TextEditingController amountEditTextController = TextEditingController();
  TextEditingController phoneNumberController = TextEditingController();
  TextEditingController emailTextController = TextEditingController();
  final UserKittyController _usercontroller = Get.put(UserKittyController());

  @override
  void initState() {
    phoneNumberController.text =
        _usercontroller.getLocalUser()?.phoneNumber ?? "";
    super.initState();
  }

  bool isNameChecked = false;
  bool isNumberChecked = false;

  String? selectedChannel = "M-Pesa";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        title: const Text('Top Up To Wallet'),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: EdgeInsets.only(top: 10.0.h),
          child: Container(
            margin: EdgeInsets.only(bottom: 5.h),
            padding: EdgeInsets.symmetric(horizontal: 30.w),
            child: Stack(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildPhoneFrame(context),
                    SizedBox(height: 20.h),
                    Padding(
                        padding: EdgeInsets.only(left: 1.w),
                        child: Text("Amount to contribute",
                            style: CustomTextStyles.titleSmallGray90001)),
                    SizedBox(height: 3.h),
                    _buildAmountEditText(context),
                    SizedBox(height: 18.h),
                    Padding(
                        padding: EdgeInsets.only(left: 1.w),
                        child: Text("Choose Payment Channel",
                            style: CustomTextStyles.titleSmallGray90001)),
                    SizedBox(height: 10.h),
                    ContributeChannelsBuilder(
                        selectedChannel: selectedChannel ?? "",
                        onChange: (String? newlySelectedChannel) {
                          setState(() {
                            selectedChannel = newlySelectedChannel;
                          });
                        }),
                    SizedBox(height: 18.h),
                    if (selectedChannel == 'Visa') // show email field
                      _buildAEmailField(context),
                    SizedBox(height: 14.h),
                    _buildMakeTopUpButton(context),
                    SizedBox(
                      height: 30.h,
                    )
                  ],
                ),
                // Visibility(
                //    visible: contributeController.confirmpayLoading.value,
                //   child: Positioned.fill(
                //     child: Container(
                //       color: Colors.black
                //           .withOpacity(0.5), // Semi-transparent background
                //       child: Center(
                //         child: Column(
                //           mainAxisAlignment: MainAxisAlignment.center,
                //           children: [
                //             SpinKitDualRing(
                //               color: Theme.of(context).primaryColor,
                //               size: 70.0,
                //             ),
                //             const SizedBox(
                //                 height:
                //                     20), // Adjust the spacing between the loader and text
                //             const Text(
                //               'Processing Payment.... Please wait', // Add your desired text here
                //               style: TextStyle(
                //                 fontSize: 16,
                //                 fontWeight: FontWeight.bold,
                //                 color: Colors.white,
                //               ),
                //             ),
                //           ],
                //         ),
                //       ),
                //     ),
                //   ),
                // ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Section Widget
  Widget _buildPhoneFrame(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 1.w, top: 20.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
              padding: EdgeInsets.only(left: 4.w),
              child: Text("Phone number",
                  style: CustomTextStyles.titleSmallGray900)),
          SizedBox(height: 2.h),
          SizedBox(
            child: CustomTextField(
              controller: phoneNumberController,
              paddingHorizontal: 2.spMin,
              maxLength: 13,
              showNoKeyboard: true,
              inputFormatters: <TextInputFormatter>[
                FilteringTextInputFormatter.allow(RegExp("[0-9]")),
              ],
              hintText: "phone number",
              labelText: "Mobile number * ",
              validator: (value) {
                if (value!.isEmpty) {
                  return "Enter your Mobile Number";
                } else if (value.length < 9) {
                  return "Invalid Mobile Number";
                } else {
                  return null;
                }
              },
            ),
          )
        ],
      ),
    );
  }

  /// Section Widget
  Widget _buildAmountEditText(BuildContext context) {
    return Padding(
        padding: EdgeInsets.only(left: 1.w),
        child: CustomTextField(
          controller: amountEditTextController,
          maxLength: 6,
          paddingHorizontal: 2.spMin,
          showNoKeyboard: true,
          inputFormatters: <TextInputFormatter>[
            FilteringTextInputFormatter.allow(RegExp("[0-9]")),
          ],
          hintText: "Amount",
          labelText: "Enter amount(KSH) * ",
          validator: (value) {
            if (value!.isEmpty) {
              return "Enter amount";
            }
            final amt = int.tryParse(value);
            if (amt == null) {
              return "Enter valid amount";
            } else {
              return null;
            }
          },
        ));
  }

  Widget _buildAEmailField(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 1.w),
      child: CustomTextField(
        controller: emailTextController,
        paddingHorizontal: 2.spMin,
        hintText: "Email",
        labelText: "Email",
        validator: (value) {
          return null;
        },
      ),
    );
  }

  /// Section Widget
  Widget _buildMakeTopUpButton(BuildContext context) {
    return Obx(() => CustomKtButton(
        isLoading: _usercontroller.topUploading.isTrue,
        onPress: () async {
          final chan = KittyController().getNetworkCode(
            networkTitle: selectedChannel ?? "",
          );

          bool res = await _usercontroller.topup(
            phoneNumber: phoneNumberController.text,
            amount: int.parse(amountEditTextController.text.trim()),
            channel: chan!,
            userId: _usercontroller.getLocalUser()?.id ?? 0,
            email: emailTextController.text,
          );

          if (!mounted) return;
          if (res) {
            if (chan == 0) {
              ToastUtils.showSuccessToast(
                context,
                _usercontroller.apiMessageTopup.string,
                "success",
              );
              Get.offNamed(NavRoutes.topupOtp);
            }
            if (chan == 63902) {
              ToastUtils.showSuccessToast(
                context,
                _usercontroller.apiMessageTopup.string,
                "success",
              );
              _usercontroller.getUser();
              Get.back();
            }

            if (chan == 55) {
              Get.off(() => const CardPayment(isChamaContribute: false));
              //Get.toNamed(NavRoutes.cardpayment);
            }
          } else {
            ToastUtils.showErrorToast(
              context,
              _usercontroller.apiMessageTopup.string,
              "Error",
            );
          }
        },
        btnText: "Top Up"));
  }

  /// Navigates back to the previous screen.
  onTapArrowLeft(BuildContext context) {
    Navigator.pop(context);
  }
}
