import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/admin-transactions/reconciliations_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/models/admin/reconciliation.dart';
import 'package:onekitty/utils/size_config.dart';
import 'package:pluto_grid_plus/pluto_grid_plus.dart';

class ReconciliationDataView extends StatefulWidget {
  const ReconciliationDataView({
    super.key,
  });

  @override
  State<ReconciliationDataView> createState() => _ReconciliationDataViewState();
}

class _ReconciliationDataViewState extends State<ReconciliationDataView> {
  final controller = Get.put(ReconController());
  List<PlutoRow<Reconciliation>> fakeFetchedRows = [];

  getInitialdata() async {
    await controller.getReconciliationRecords();
    final fetchedTrans = controller.reconciliation;
    fakeFetchedRows = fetchedTrans.map(
      (item) {
        print(
            "${item.createdAt} ${item.transactionAmount} ${item.ecosystemInternalBalance}");
        return PlutoRow<Reconciliation>(
          cells: {
            'created_at': PlutoCell(value: item.createdAt),
            'amount': PlutoCell(value: item.transactionAmount),
            'ecosystem': PlutoCell(value: item.ecosystemInternalBalance),
            'sp_555550': PlutoCell(value: item.sasapay555550),
            'sp_5055': PlutoCell(value: item.sasapay5055),
            'tanda': PlutoCell(value: item.tanda220429),
            'difference': PlutoCell(value: item.actualMinusEcosystem),
            'internal_id': PlutoCell(value: item.transaction?.internalId ?? ''),
            'description': PlutoCell(value: item.description),
          },
        );
      },
    ).toList();
  }

  @override
  void initState() {
    getInitialdata();
    super.initState();
  }

  // Pass an empty row to the grid initially.
  late final PlutoGridStateManager stateManager;

  final List<PlutoColumn> columns = [];

  // Pass an empty row to the grid initially.
  final List<PlutoRow> rows = [];

  Future<PlutoLazyPaginationResponse> fetch(
    PlutoLazyPaginationRequest request,
  ) async {
    List<PlutoRow> tempList = fakeFetchedRows;
    if (request.filterRows.isNotEmpty) {
      final filter = FilterHelper.convertRowsToFilter(
        request.filterRows,
        stateManager.refColumns,
      );

      tempList = fakeFetchedRows.where(filter!).toList();
    }

    // If there is a sort state,
    // you need to implement it so that the user gets data from the server
    // according to the sort state.
    //
    // request.page is 1 when the sort state changes.
    // This is because when the sort state changes,
    // new data to which the sort state is applied must be loaded.
    if (request.sortColumn != null && !request.sortColumn!.sort.isNone) {
      tempList = [...tempList];

      tempList.sort((a, b) {
        final sortA = request.sortColumn!.sort.isAscending ? a : b;
        final sortB = request.sortColumn!.sort.isAscending ? b : a;

        return request.sortColumn!.type.compare(
          sortA.cells[request.sortColumn!.field]!.valueForSorting,
          sortB.cells[request.sortColumn!.field]!.valueForSorting,
        );
      });
    }

    final page = request.page;
    const pageSize = 10;
    final totalPage = (tempList.length / pageSize).ceil();
    final start = (page - 1) * pageSize;
    final end = start + pageSize;

    // Iterable<PlutoRow> fetchedRows = tempList.getRange(
    //   max(0, start),
    //   min(tempList.length, end),
    // );
    await getInitialdata();

    return Future.value(PlutoLazyPaginationResponse(
      totalPage: totalPage,
      rows: fakeFetchedRows,
    ));
  }

  @override
  Widget build(BuildContext context) {
    final sc = SizeConfig();

    return Scaffold(
      body: PlutoGrid(
        configuration: const PlutoGridConfiguration(
            style: PlutoGridStyleConfig(
                activatedColor: AppColors.blueButtonColor)),
        columns: [
          PlutoColumn(
              title: "Created At",
              field: "created_at",
              // backgroundColor: AppColors.landGreen,
              type: PlutoColumnType.date(
                format: "yyyy-MM-dd HH:mm:ss",
              )),
          PlutoColumn(
            title: "Amount",
            field: "amount",
            type: PlutoColumnType.number(),
          ),
          PlutoColumn(
            title: "Ecosystem",
            field: "ecosystem",
            type: PlutoColumnType.number(),
          ),
          PlutoColumn(
            title: "SasaPay 555550",
            field: "sp_555550",
            type: PlutoColumnType.number(),
          ),
          PlutoColumn(
            title: "SasaPay 5055",
            field: "sp_5055",
            type: PlutoColumnType.number(),
          ),
          PlutoColumn(
            title: "Tanda",
            field: "tanda",
            type: PlutoColumnType.number(),
          ),
          PlutoColumn(
            title: "Difference",
            field: "difference",
            type: PlutoColumnType.number(),
          ),
          PlutoColumn(
            title: "Internal Id",
            field: "internal_id",
            type: PlutoColumnType.text(),
          ),
          PlutoColumn(
            title: "Decription",
            field: "description",
            type: PlutoColumnType.text(),
          ),
        ],
        onLoaded: (event) {
          //` logger.d("LOADEEEEED WEe!");
          event.stateManager.setShowColumnFilter(true);
          // event.stateManager.set
        },
        onChanged: (event) {
          // Handle cell changes
        },
        rows: fakeFetchedRows,
        createFooter: (stateManager) {
          return PlutoLazyPagination(
            // Determine the first page.
            // Default is 1.
            initialPage: 1,
            // First call the fetch function to determine whether to load the page.
            // Default is true.
            initialFetch: true,

            // Decide whether sorting will be handled by the server.
            // If false, handle sorting on the client side.
            // Default is true.
            fetchWithSorting: true,

            // Decide whether filtering is handled by the server.
            // If false, handle filtering on the client side.
            // Default is true.
            fetchWithFiltering: true,

            // Determines the page size to move to the previous and next page buttons.
            // Default value is null. In this case,
            // it moves as many as the number of page buttons visible on the screen.
            pageSizeToMove: null,
            fetch: fetch,
            stateManager: stateManager,
          );
        },
      ),
    );
  }
}
