import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/models/auth/payments_channels.dart';
import 'package:onekitty/models/kitty/beneficiary_model.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/custom_logger.dart';
import 'package:onekitty/services/http_service.dart';
import 'package:logger/logger.dart';

class BeneficiaryController extends GetxController implements GetxService {
  final isFetchingBeneficiaries = false.obs;
  final remainingPercentage = 0.0.obs;
  final isAddingBeneficiaries = false.obs;
  final isDeletingBeneficiary = false.obs;
  final beneficiaries = <BeneficiaryModel>[].obs;
  var logger = Logger(filter: CustomLogFilter());
  int channel = 63902;
  final channelName = 'M-PESA'.obs;
  final HttpService apiProvider = Get.find();
  final page = 0.obs;
  final Rx<PaymentChannels?> selectedBank = Rx<PaymentChannels?>(null);
  void setChannel(String name, int code) {
    channel = code;
    channelName(name);
  }

  final filteredBeneficiaries = <BeneficiaryModel>[].obs;

  Future<void> getPercentage() async {
    double total = 0;
    print('#############################');
    for (var ben in beneficiaries) {
      print(ben.percentage);
      total = (ben.percentage ?? 0) + total;
    }
    remainingPercentage.value = 1 - total;
    print('total : $total, remaining: $remainingPercentage');
    print('#############################');
  }

  filterBeneficiaries(String query) {
    if (query.isEmpty) {
      filteredBeneficiaries.value = beneficiaries;
    } else {
      filteredBeneficiaries.value = beneficiaries.where((beneficiary) {
        return beneficiary.accountName!
                .toLowerCase()
                .contains(query.toLowerCase()) ||
            beneficiary.phoneNumber.contains(query);
      }).toList();
    }
    update();
  }

  Future getBeneficiaries(int kittyId) async {
    try {
      isFetchingBeneficiaries(true);
      final response = await apiProvider.request(
          method: Method.GET,
          url: "${ApiUrls.fetchBeneficiaries}?kitty_id=$kittyId");

      if (response.data['status'] ?? false) {
        final _returneddata = response.data['data'] ?? [];

        beneficiaries.value = _returneddata.map((item) {
          return BeneficiaryModel.fromJson(item);
        }).toList();
        filteredBeneficiaries(beneficiaries);
        // Avoid calling update() during the build phase
        // Instead, use a post-frame callback to ensure the update happens after the build
        WidgetsBinding.instance.addPostFrameCallback((_) {
          update();
        });
      } else {
        Snack.show(false, '${response.data['message']}');
      }
    } catch (e) {
      logger.e(e);
      ToastUtils.showToast('error occured');
    } finally {
      isFetchingBeneficiaries(false);
      WidgetsBinding.instance.addPostFrameCallback((_) {
        update();
      });
    }
  }

  Future<bool> addBeneficiaries(Map<String, dynamic> data, int kittyId) async {
    try {
      isAddingBeneficiaries(true);
      final response = await apiProvider.request(
          params: data, method: Method.POST, url: ApiUrls.beneficiary);

      if (response.data['status'] ?? false) {
        getBeneficiaries(kittyId);
        Get.snackbar('Success', response.data['message'] ?? 'Success',
            backgroundColor: Colors.green);
        return true;
      } else {
        Get.snackbar('error', response.data['message'] ?? 'error adding user',
            backgroundColor: Colors.red);
        return false;
      }
    } catch (e) {
      logger.e(e);
      ToastUtils.showToast('error occured');
      isAddingBeneficiaries(false);
      update();
      return false;
    } finally {
      isAddingBeneficiaries(false);
    }
  }

  Future<bool> updateBeneficiary(Map<String, dynamic> data, int kittyId) async {
    try {
      isAddingBeneficiaries(true);
      final response = await apiProvider.request(
          params: data, method: Method.PUT, url: ApiUrls.beneficiary);

      if (response.data['status'] ?? false) {
        getBeneficiaries(kittyId);
        Get.snackbar('Success', response.data['message'] ?? "success Editing",
            backgroundColor: Colors.green);
        return true;
      } else {
        Get.snackbar(
            'error', '${response.data['message'] ?? 'an error occured'}',
            backgroundColor: Colors.red);
        return false;
      }
    } catch (e) {
      logger.e(e);
      ToastUtils.showToast('error occured');
      return false;
    } finally {
      isAddingBeneficiaries(false);
    }
  }

  Future deleteBeneficiary(int id, int kittyId) async {
    try {
      isDeletingBeneficiary(true);
      final response = await apiProvider.request(
          method: Method.DELETE, url: "${ApiUrls.beneficiary}$id");

      if (response.data['status'] ?? false) {
        getBeneficiaries(kittyId);
        Get.snackbar('Success', 'deleted Operator');
      } else {
        Get.snackbar('error', '${response.data['message']}',
            backgroundColor: Colors.red);
      }
    } catch (e) {
      logger.e(e);
      ToastUtils.showToast('error occured');
    } finally {
      isDeletingBeneficiary(false);
    }
  }
}
