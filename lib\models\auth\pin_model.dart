class PinModel {
    bool status;
    String message;
    Data data;

    PinModel({
        required this.status,
        required this.message,
        required this.data,
    });

}

class Data {
    int expiresIn;
    String token;
    User user;

    Data({
        required this.expiresIn,
        required this.token,
        required this.user,
    });

}

class User {
    int id;
    DateTime createdAt;
    DateTime updatedAt;
    dynamic deletedAt;
    String phoneNumber;
    String firstName;
    String secondName;
    String empty;
    int status;

    User({
        required this.id,
        required this.createdAt,
        required this.updatedAt,
        required this.deletedAt,
        required this.phoneNumber,
        required this.firstName,
        required this.secondName,
        required this.empty,
        required this.status,
    });

}