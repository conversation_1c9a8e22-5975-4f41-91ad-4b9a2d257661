import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty/admin_screens/common/color_codes.dart';
import 'package:onekitty/controllers/admin/chama/chama_admin_controller.dart';
import 'package:onekitty/models/chama/chama_model.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/screens/dashboard/pages/chama/viewing_single_chama/viewing_single_chama.dart';
import 'package:onekitty/utils/formatted_currency.dart';
import 'package:onekitty/admin_screens/chama/widgets/filter_widget.dart';
import 'package:onekitty/admin_screens/chama/widgets/table_footer.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import '../../controllers/chama/chama_controller.dart';
import '../common/colum_widths.dart';

class ChamaAdmin extends StatelessWidget {
  const ChamaAdmin({super.key});
  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: ChamaDataTable(),
    );
  }
}

class ChamaDataTable extends StatefulWidget {
  const ChamaDataTable({super.key});

  @override
  _ChamaDataTableState createState() => _ChamaDataTableState();
}
 
class _ChamaDataTableState extends State<ChamaDataTable> {
  final controller = Get.put(ChamaAdminController());
  int? _sortColumnIndex;
  bool _sortAscending = true;

  @override
  void initState() {
    super.initState();
    controller.fetchAllChamas(0);
  }

  void _sort<T>(Comparable<T> Function(Chama chama) getField, int columnIndex,
      bool ascending) {
    setState(() {
      _sortColumnIndex = columnIndex;
      _sortAscending = ascending;

      controller.chamas.sort((a, b) {
        final aValue = getField(a);
        final bValue = getField(b);
        return ascending
            ? Comparable.compare(aValue, bValue)
            : Comparable.compare(bValue, aValue);
      });
    });
  }

  void _onRowsPerPageChanged(int value) {
    controller.size.value = value;

    controller.fetchAllChamas(0);
  }

  void _onRefresh() {
    controller.chamas.clear();
    controller.fetchAllChamas(0);
  }

  void _onPreviousPage() {
    if (!controller.isFirst.value) {
      controller.fetchAllChamas(controller.currentPage.value - 1);
    }
  }

  void _onNextPage() {
    if (!controller.isLast.value) {
      controller.fetchAllChamas(controller.currentPage.value + 1);
    }
  }

  @override
  Widget build(BuildContext context) {
    final idSize = 80.0.obs;
    final showAll = false.obs;
    return Scaffold(
      bottomNavigationBar: Obx(
        () => TablePaginationFooter(
          currentPage: controller.currentPage.value,
          totalPages: controller.totalPages.value,
          rowsPerPage: controller.size.value,
          availableRowsPerPage: const [15, 30, 50, 100],
          onRowsPerPageChanged: _onRowsPerPageChanged,
          onRefresh: _onRefresh,
          onPreviousPage: _onPreviousPage,
          onNextPage: _onNextPage,
        ),
      ),
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            pinned: false,
            expandedHeight: 400.0,
            flexibleSpace: FlexibleSpaceBar(
              background: ChamaFilterWidget(controller: controller),
            ),
          ),
        
          SliverFillRemaining(
            child:  Obx(
                () => controller.isLoading.value
                    ? const Center(child: CircularProgressIndicator())
                    : Align(
                        alignment: Alignment.center,
                        child: SfDataGrid(
                          source: ChamaDataSource(chamaData: controller.chamas),
                          allowColumnsResizing: true,
                          columnResizeMode: ColumnResizeMode.onResize,
                          gridLinesVisibility: GridLinesVisibility.both,
                          headerGridLinesVisibility: GridLinesVisibility.both,
                          isScrollbarAlwaysShown: false,
                          // shrinkWrapColumns: true,
                          // shrinkWrapRows: true,
                          
                          frozenColumnsCount: 1,
                          navigationMode: GridNavigationMode.cell,
                          // verticalScrollPhysics: const NeverScrollableScrollPhysics(),
                          columnWidthMode: ColumnWidthMode.fitByCellValue,
                          onCellTap: (details) {
                            final singleChamaController =
                                Get.put(ChamaDataController());
                            singleChamaController.chama.value = UserChama(
                                chama: controller
                                    .chamas[details.rowColumnIndex.rowIndex - 1]);
                            singleChamaController.singleChamaDts.value =
                                controller
                                    .chamas[details.rowColumnIndex.rowIndex - 1];
                            Get.to(() => const ViewingSingleChama());
                          },                        
                          onColumnResizeUpdate:
                              (ColumnResizeUpdateDetails details) {
                            setState(() {
                              final double newWidth = details.width;
                              switch (details.column.columnName) {
                                case 'id':
                                  ColumnWidths.idWidth.value = newWidth;
                                  break;
                                case 'created_at':
                                  ColumnWidths.createdAtWidth.value = newWidth;
                                  break;
                                case 'title':
                                  ColumnWidths.titleWidth.value = newWidth;
                                  break;
                                case 'username':
                                  ColumnWidths.usernameWidth.value = newWidth;
                                  break;
                                  case 'kittyId':
                                  ColumnWidths.kittyIdWidth.value = newWidth;
                                  break;
                                case 'email':
                                  ColumnWidths.emailWidth.value = newWidth;
                                  break;
                                case 'phone':
                                  ColumnWidths.phoneWidth.value = newWidth;
                                  break;
                                case 'amount':
                                  ColumnWidths.amountWidth.value = newWidth;
                                  break;
                                   case 'balance':
                                  ColumnWidths.balanceWidth.value = newWidth;
                                  break;
                                case 'status':
                                  ColumnWidths.statusWidth.value = newWidth;
                                  break;
                                case 'actions':
                                  ColumnWidths.actionsWidth.value = newWidth;
                                  break;
                              }
                              print(
                                  'Column ${details.column.columnName} resized to $newWidth');
                            });
                            return true;
                          },
                          columns: <GridColumn>[
                            GridColumn(
                              columnName: 'id',
                              width: ColumnWidths.idWidth.value,
                              minimumWidth: 60,
                              label: Container(
                                padding: const EdgeInsets.all(8.0),
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey.shade300),
                                ),
                                alignment: Alignment.centerLeft,
                                child: const Text('ID'),
                              ),
                            ),
                            GridColumn(
                              columnName: 'created_at',
                              width: ColumnWidths.createdAtWidth.value,
                              minimumWidth: 140,
                              label: Container(
                                padding: const EdgeInsets.all(8.0),
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey.shade300),
                                ),
                                alignment: Alignment.centerLeft,
                                child: const Text('Created At'),
                              ),
                            ),
                            GridColumn(
                              columnName: 'title',
                              width: ColumnWidths.titleWidth.value,
                              minimumWidth: 150,
                              label: Container(
                                padding: const EdgeInsets.all(8.0),
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey.shade300),
                                ),
                                alignment: Alignment.centerLeft,
                                child: const Text('Title'),
                              ),
                            ),
                            GridColumn(
                              columnName: 'username',
                              width: ColumnWidths.usernameWidth.value,
                              minimumWidth: 140,
                              label: Container(
                                padding: const EdgeInsets.all(8.0),
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey.shade300),
                                ),
                                alignment: Alignment.centerLeft,
                                child: const Text('Username'),
                              ),
                            ),
                             GridColumn(
                              columnName: 'kittyId',
                              // width: ColumnWidths.usernameWidth.value,
                              minimumWidth: 140,
                              label: Container(
                                padding: const EdgeInsets.all(8.0),
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey.shade300),
                                ),
                                alignment: Alignment.centerLeft,
                                child: const Text('kittyId'),
                              ),
                            ),
                            GridColumn(
                              columnName: 'email',
                              width: ColumnWidths.emailWidth.value,
                              minimumWidth: 170,
                              label: Container(
                                padding: const EdgeInsets.all(8.0),
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey.shade300),
                                ),
                                alignment: Alignment.centerLeft,
                                child: const Text('Email'),
                              ),
                            ),
                            GridColumn(
                              columnName: 'phone',
                              width: ColumnWidths.emailWidth.value,
                              minimumWidth: 170,
                              label: Container(
                                padding: const EdgeInsets.all(8.0),
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey.shade300),
                                ),
                                alignment: Alignment.centerLeft,
                                child: const Text('Phone'),
                              ),
                            ),
                            GridColumn(
                              columnName: 'amount',
                              width: ColumnWidths.amountWidth.value,
                              minimumWidth: 100,
                              label: Container(
                                padding: const EdgeInsets.all(8.0),
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey.shade300),
                                ),
                                alignment: Alignment.centerLeft,
                                child: const Text('Amount'),
                              ),
                            ),
                             GridColumn(
                              columnName: 'balance',
                              width: ColumnWidths.balanceWidth.value,
                              minimumWidth: 100,
                              label: Container(
                                padding: const EdgeInsets.all(8.0),
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey.shade300),
                                ),
                                alignment: Alignment.centerLeft,
                                child: const Text('Balance'),
                              ),
                            ),
                            GridColumn(
                              columnName: 'next_occurence',
                              width: ColumnWidths.createdAtWidth.value,
                              minimumWidth: 140,
                              label: Container(
                                padding: const EdgeInsets.all(8.0),
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey.shade300),
                                ),
                                alignment: Alignment.centerLeft,
                                child: const Text('Next Occurence'),
                              ),
                            ),
                            GridColumn(
                              columnName: 'status',
                              width: ColumnWidths.statusWidth.value,
                              minimumWidth: 80,
                              label: Container(
                                padding: const EdgeInsets.all(8.0),
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey.shade300),
                                ),
                                alignment: Alignment.centerLeft,
                                child: const Text('Status'),
                              ),
                            ),
                            // GridColumn(
                            //   columnName: 'actions',
                            //   width: 100,
                            //   minimumWidth: 80,
                            //   label: Container(
                            //     padding: const EdgeInsets.all(8.0),
                            //     decoration: BoxDecoration(
                            //       border:
                            //           Border.all(color: Colors.grey.shade300),
                            //     ),
                            //     alignment: Alignment.centerLeft,
                            //     child: const Text('Actions'),
                            //   ),
                            // ),
                          ],
                        ),
                      ),
              
            ),
          ),
        ],
      ),
    );
  }
}

class ChamaDataSource extends DataGridSource {
  ChamaDataSource({required List<Chama> chamaData}) {
    _chamaData = chamaData
        .map<DataGridRow>((chama) => DataGridRow(cells: [
              DataGridCell<String>(
                  columnName: 'id', value: chama.id.toString()),
              DataGridCell<String>(
                  columnName: 'created_at',
                  value: DateFormat('yyyy-MM-dd HH:mm')
                      .format(chama.createdAt ?? DateTime.now())),
              DataGridCell<String>(
                  columnName: 'title', value: chama.title ?? ''),
              DataGridCell<String>(
                  columnName: 'username', value: chama.username ?? ''),
              DataGridCell<String>(
                  columnName: 'kittyId', value: chama.kittyId.toString()),
            
              DataGridCell<String>(
                  columnName: 'email', value: chama.email ?? ''),
              DataGridCell<String>(
                  columnName: 'phone', value: chama.phoneNumber ?? ''),
              DataGridCell<String>(
                  columnName: 'amount',
                  value: FormattedCurrency()
                      .getFormattedCurrency(chama.amount)
                      .toString()),
                         DataGridCell<String>(
                  columnName: 'balance',
                  value: FormattedCurrency()
                      .getFormattedCurrency(chama.balance)
                      .toString()),
              DataGridCell<String>(
                  columnName: 'next_occurence',
                  value: DateFormat('yyyy-MM-dd HH:mm')
                      .format(chama.nextOccurrence ?? DateTime.now())),
              DataGridCell<String>(
                  columnName: 'status', value: chama.status ?? ''),
            ]))
        .toList();
  }

  List<DataGridRow> _chamaData = [];

  @override
  List<DataGridRow> get rows => _chamaData;

  @override
  DataGridRowAdapter? buildRow(DataGridRow row) {
    return DataGridRowAdapter(
        cells: row.getCells().map<Widget>((dataGridCell) {
      if (dataGridCell.columnName == 'status') {
        return Text(
          dataGridCell.value.toString(),
          style: TextStyle(
            color: getStatusColor(dataGridCell.value.toString())
          ),
        );
      }
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        alignment: Alignment.centerLeft,
        child: Text(dataGridCell.value.toString()),
      );
    }).toList());
  }
}
