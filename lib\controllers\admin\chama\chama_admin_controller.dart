import 'package:dio/dio.dart' show Options, ResponseType;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/models/admin/chama_occurence_model.dart';
import 'package:onekitty/models/chama/chama_memebers_model.dart';
import 'package:onekitty/models/chama/chama_model.dart';
import 'package:onekitty/models/chama/chama_settings.dart';
import 'package:onekitty/models/chama/chama_transactions.dart';
import 'package:onekitty/models/chama/configs_model.dart';
import 'package:onekitty/models/chama/invoices.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/custom_logger.dart';
import 'package:onekitty/services/http_service.dart';

class ChamaAdminController extends GetxController implements GetxService {
  @override
  void onInit() {
    super.onInit();
    getConfigs();
  }

  final logger = Logger(filter: CustomLogFilter());
  final HttpService apiProvider = Get.find();
  RxBool isLoading = false.obs;
  final chamas = <Chama>[].obs;
  final chamaDetails = Chama().obs;
  final isGetAllChamaDetailsLoading = false.obs;
  final penaltyCount = 0.obs;
  final penaltyBal = 0.0.obs;
  final penaltyKittyBalance = 0.0.obs;
  final beneficiaries = <NextBeneficiary>[].obs;
  final notifications = <NotificationCls>[].obs;
  final isgetRes = false.obs;
  final currentPage = 0.obs;
  final size = 15.obs;
  final chamaId = ''.obs;
  final frequency = ''.obs;
  final kittyId = ''.obs;
  final search = ''.obs;
  final startDate = ''.obs;
  final endDate = ''.obs;
  final members = <ChamaMembers>[].obs;
  final maxPage = 0.obs;
  final totalPages = 0.obs;
  final isLast = false.obs;
  final isFirst = true.obs;
  final invoices = <Invoice>[].obs;
  final chamaOcurences = <ChamaOcurenceModel>[].obs;
  final chamaSettings = ChamaSetting().obs;
  //Frequency
  final apiMessage = ''.obs;
  final getStatus = false.obs;
  final isGettingConfigs = false.obs;
  final frequencies = <Frequency>[].obs;
  final roles = <ChamaRole>[].obs;
  //transactions
  final transactions = <Transaction>[].obs;

  Future addBeneficiaryAccount(Map<String, dynamic> params) async {
    try {
      isLoading(true);
      var response = await apiProvider.request(
          method: Method.POST,
          url: ApiUrls.addBeneficiaryAccount,
          params: params);
      if (response.data['status'] ?? false) {
        Get.snackbar(
            'Success', 'Successfully added general BeneficiaryAccount');
      } else {
        Get.snackbar('Error',
            '${response.data['message'] ?? "Failed to add general BeneficiaryAccount"}',
            backgroundColor: Colors.red);
      }
      print(response.data);
    } catch (e) {
      Get.snackbar('Error', '$e', backgroundColor: Colors.amber);
      logger.e('Error adding general penalty: $e');
    } finally {
      isLoading(false);
    }
  }

Future fetchAllChamas(int page) async {
  try {
    isLoading(true);
    final String url = "${ApiUrls.getAllChamasAdmin}?page=$page&size=${size.value}&search=${search.value}&chama_id=${chamaId.value}&frequency=${frequency.value}&kitty_id=${kittyId.value}&start_date=${startDate.value}&end_date=${endDate.value}";
    
    var response = await apiProvider.request(
      method: Method.GET,
      url: url,
      options: Options(
        // Add extra options for troubleshooting
        validateStatus: (_) => true,
        followRedirects: true,
        responseType: ResponseType.json,
      ),
    );
    
    if (response.statusCode == 200 && response.data != null) {
      // Process successful response
      chamas((response.data['data']['items'] as List)
          .map((e) => Chama.fromJson(e))
          .toList());

      currentPage.value = response.data['data']['page'];
      maxPage.value = response.data['data']['total_pages'];
      totalPages.value = response.data['data']['total_pages'];
      isLast.value = response.data['data']['last'];
      isFirst.value = response.data['data']['first'];
    } else {
      logger.e("Error Response: ${response.statusCode} - ${response.statusMessage}");
      throw Exception('Failed to load chamas: ${response.statusMessage}');
    }
  } catch (e) {
    logger.e("Error fetching chamas: $e");
    Get.snackbar(
      'Error',
      'Failed to load chamas. Please check your connection and try again.',
      backgroundColor: Colors.red.shade100,
      duration: const Duration(seconds: 5),
    );
    throw Exception(e);
  } finally {
    isLoading(false);
  }
}

  
  Future<bool> getChamaTrnsactions(
      {required int chamaId, required int page, String filters = ''}) async {
    try {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        isLoading(true);
      });
      String url =
          "${ApiUrls.getChamaTransactions}?page=$page&size=${invoiceSize.value}&chama_id=$chamaId&$filters";
      var response = await apiProvider.request(url: url, method: Method.GET);
      apiMessage(response.data["message"]);
      logger.log(Level.debug, response.data);
      if (response.data["status"]) {
        transactions([]);
        invoiceMaxPage.value = response.data['data']['max_page'] ?? 0;
        invoiceTotalPages.value = response.data['data']['total_pages'] ?? 0;
        invoiceIsLast.value = response.data['data']['last'] ?? false;
        invoiceIsFirst.value = response.data['data']['is_first'] ?? false;
        invoiceCurrentPage.value = response.data['data']['page'] ?? 0;
        for (var element in response.data["data"]["items"] ?? []) {
          transactions.add(Transaction.fromJson(element));
        }
        return true;
      } else {
        Get.snackbar(
            'Error', response.data['message'] ?? 'Failed to load transactions');
        return false;
      }
    } catch (e) {
      Get.snackbar('Error', '$e', backgroundColor: Colors.amber);
      logger.e(e);
      apiMessage(e.toString());
      return false;
    } finally {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        isLoading(false);
      });
    }
  }

  getAllChamaDetails({required int chamaId}) async {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      isGetAllChamaDetailsLoading(true);
      update();
    });
    update();
    try {
      var res = await apiProvider.request(
          url: ApiUrls.getAllChamaDetails + chamaId.toString(),
          method: Method.GET);
      // logger.log(Level.debug, res.data);
      // print(res.data['']);
      if (res.data["status"]) {
        // settings(res.data["data"]["settings"]);
        penaltyCount(res.data["data"]["penalties_count"]);
        penaltyKittyBalance(double.tryParse(
                res.data["data"]["penalty_kitty_balance"].toString()) ?? 0.0);
        beneficiaries([]);
        notifications([]);
        members([]);
        for (var element in res.data["data"]["next_beneficiaries"] ?? []) {
          beneficiaries.add(NextBeneficiary.fromJson(element));
        }
        for (var element in res.data["data"]["notification"]) {
          notifications.add(NotificationCls.fromJson(element));
        }
        for (var element in res.data["data"]["members"]) {
          members.add(ChamaMembers.fromJson(element));
        }
        chamaDetails(Chama.fromJson(res.data["data"]["chama"]));
        penaltyBal(double.parse(
            "${res.data["data"]["penalty_kitty_balance"] ?? 0.0}"));
      }
    } catch (e) {
      logger.e(e);
      update();
    } finally {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        isGetAllChamaDetailsLoading(false);
        update();
      });
    }
  }

  Future addGeneralPenalty(
      {bool update = false, required Map<String, dynamic> params}) async {
    try {
      isLoading(true);
      var response = await apiProvider.request(
          method: update ? Method.PUT : Method.POST,
          url: ApiUrls.addGeneralPenalty,
          params: params);
      if (response.data['status'] ?? false) {
        Get.snackbar('Success', 'Successfully added general penalty');
      } else {}
      print(response.data);
    } catch (e) {
      Get.snackbar('Error', '$e', backgroundColor: Colors.amber);
      logger.e('Error adding general penalty: $e');
    } finally {
      isLoading(false);
    }
  }

  final invoiceSize = 15.obs;
  final invoiceMaxPage = 0.obs;
  final invoiceTotalPages = 0.obs;
  final invoiceIsLast = false.obs;
  final invoiceIsFirst = true.obs;
  final invoiceCurrentPage = 0.obs;
  clearInvoice() {
    invoices([]);
    invoiceMaxPage(0);
    invoiceTotalPages(0);
    invoiceIsLast(false);
    invoiceIsFirst(true);
    invoiceCurrentPage(0);
  }

  Future getChamaSettings({required int chamaId}) async {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      isGetAllChamaDetailsLoading(true);
      update();
    });

    try {
      var res = await apiProvider.request(
          url: "${ApiUrls.getChamaSettings}?chama_id=$chamaId",
          method: Method.GET);
      final data = res.data["data"];
      logger.log(Level.debug, res.data);
      if (data != null) {
        final settingss = ChamaSetting.fromJson(data);
        chamaSettings(settingss);
      } else {
        logger.e(res.data["message"]);
      }
      WidgetsBinding.instance.addPostFrameCallback((_) {
        isGetAllChamaDetailsLoading(false);
      });
      return res.data["status"];
    } catch (e) {
      logger.e(e);
      logger.e("Error,Please try again");
      WidgetsBinding.instance.addPostFrameCallback((_) {
        isGetAllChamaDetailsLoading(false);
      });
      return false;
    }
  }

  Future getChamaOccurence(int id, {String filters = ''}) async {
    try {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        isLoading(true);
      });
      var response = await apiProvider.request(
          method: Method.GET,
          url:
              "${ApiUrls.getChamaOccurence}?page=${invoiceCurrentPage.value}&size=${invoiceSize.value}&chama_id=$id&$filters");
      if (response.data['status'] ?? false) {
        chamaOcurences.value = (response.data['data']['items'] as List)
            .map((e) => ChamaOcurenceModel.fromJson(e))
            .toList();
        invoiceMaxPage.value = response.data['data']['max_page'] ?? 0;
        invoiceTotalPages.value = response.data['data']['total_pages'] ?? 0;
        invoiceIsLast.value = response.data['data']['last'] ?? false;
        invoiceIsFirst.value = response.data['data']['is_first'] ?? false;
        invoiceCurrentPage.value = response.data['data']['page'] ?? 0;
        update();
      } else {
        Get.snackbar('Error', '${response.data['message']}',
            backgroundColor: const Color.fromRGBO(244, 67, 54, 1));
      }
    } catch (e) {
      logger.e(e);
      Get.snackbar(
        'Error',
        'An error occurred while fetching Chama occurrence data. Please try again later.',
        backgroundColor: Colors.amber,
      );
    } finally {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        isLoading(false);
      });
    }
  }

  Future<bool> getConfigs() async {
    isGettingConfigs(true);
    try {
      var res = await apiProvider.request(
        url: ApiUrls.getFreq,
        method: Method.GET,
      );
      apiMessage(res.data["message"]);
      getStatus(res.data["status"]);
      if (res.data['status']) {
        frequencies([]);
        for (var element in res.data["data"]["frequencies"]) {
          frequencies.add(Frequency.fromJson(element));
        }
        roles([]);
        for (var role in res.data["data"]["chama_roles"]) {
          roles.add(ChamaRole.fromJson(role));
        }
        update();
        isGettingConfigs(false);
        return true;
      } else {
        isGettingConfigs(false);

        return false;
      }
    } catch (e) {
      logger.e(e);
      isGettingConfigs(false);
      return false;
    }
  }

  Future getInvoices(
      {required int chamaId, required int page, String filters = ''}) async {
    try {
      isLoading(true);
      var response = await apiProvider.request(
          method: Method.GET,
          url:
              "${ApiUrls.getInvoices}?page=$page&size=${invoiceSize.value}&chama_id=$chamaId$filters");
      if (response.data['status'] ?? false) {
        invoices((response.data['data']['items'] as List)
            .map((e) => Invoice.fromJson(e))
            .toList());
        invoiceMaxPage(response.data['data']['max_page']);
        invoiceTotalPages(response.data['data']['total_pages']);
        invoiceIsLast(response.data['data']['is_last']);
        invoiceIsFirst(response.data['data']['is_first']);
        invoiceCurrentPage(response.data['data']['page']);
        update();
      } else {
        Get.snackbar('Error', '${response.data['message']}',
            backgroundColor: Colors.red);
      }
    } catch (e) {
      Get.snackbar('Error', '$e', backgroundColor: Colors.amber);
      logger.e(e);
    } finally {
      isLoading(false);
    }
  }

  Future sendFundsToBeneficiary({required int id}) async {
    try {
      isLoading(true);
      var response = await apiProvider.request(
          method: Method.POST,
          url: ApiUrls.sendfundstobeneficiary,
          params: {'ID': id.toString()});
      if (response.data['status'] ?? false) {
        Get.snackbar('Success', '${response.data['message']}');
      } else {
        Get.snackbar('Error', '${response.data['message']}',
            backgroundColor: Colors.red);
      }
      print(response.data);
    } catch (e) {
      Get.snackbar('Error', '$e', backgroundColor: Colors.amber);
      logger.e('Error sending funds: $e');
    } finally {
      isLoading(false);
    }
  }
}
