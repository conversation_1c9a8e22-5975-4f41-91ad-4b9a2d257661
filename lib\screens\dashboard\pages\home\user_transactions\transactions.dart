// ignore_for_file: must_be_immutable, use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter_animated_dialog_updated/flutter_animated_dialog.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:grouped_list/grouped_list.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/models/user_transaction_model.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/see_all_transactions_screen/widgets/export_widget.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/utils/size_config.dart';
import 'package:share_plus/share_plus.dart';

import '../../../../../../utils/utils_exports.dart';
import '../../../../../models/auth/user_model.dart';

class UserTransactionWidget extends StatelessWidget {
  final DataController dataController = Get.find<DataController>();
  ContributeController contributeController = ContributeController();
  UserKittyController controller = Get.find<UserKittyController>();
  UserModelLatest user = UserModelLatest();

  UserTransactionWidget({super.key});
  final dateformat = DateFormat('EE, dd MMMM');

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Column(
        children: [
          GestureDetector(
            onTap: () {},
            child: Padding(
              padding: EdgeInsets.only(top: 20.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text("Transactions",
                      style: CustomTextStyles.titleSmallGray900),
                  GestureDetector(
                    onTap: () {
                      Navigator.pushNamed(context, NavRoutes.usertransactions);
                    },
                    child: Text("See all",
                        style: CustomTextStyles.titleSmallIndigo500),
                  )
                ],
              ),
            ),
          ),
          SizedBox(
            height: 15.h,
          ),
          GetX(
            init: UserKittyController(),
            initState: (state) {
              Future.delayed(Duration.zero, () async {
                try {
                  await state.controller?.getUserTransactions(
                      phoneNo: controller.getLocalUser()?.phoneNumber ?? "");
                } catch (e) {
                  throw e;
                }
              });
            },
            builder: (UserKittyController controller) {
              if (controller.loadingTransactions.isTrue) {
                return SizedBox(
                  height: SizeConfig.screenHeight * .33,
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SpinKitDualRing(
                          color: ColorUtil.blueColor,
                          lineWidth: 4.sp,
                          size: 40.0.sp,
                        ),
                        const Text(
                          "loading..",
                          style: TextStyle(
                            color: Colors.white,
                          ),
                        )
                      ],
                    ),
                  ),
                );
              } else if (controller.alltransactions.isEmpty) {
                return Column(
                  children: [
                    const Text("You have no transactions yet"),
                    Image.asset(
                      AssetUrl.notFound,
                      height: 150.h,
                    ),
                    Center(
                      child: CustomKtButton(
                        onPress: () async {
                          Get.toNamed(NavRoutes.urlScreen);
                        },
                        width: 110.w,
                        height: 44.h,
                        btnText: "Contribute",
                      ),
                    ),
                  ],
                );
              } else {
                return Expanded(
                  child: GroupedListView<Item, DateTime>(
                    sort: false,
                    elements: controller.alltransactions,
                    groupBy: (Item element) {
                      DateTime date = element.createdAt!;
                      return DateTime(date.year, date.month, date.day);
                    },
                    groupHeaderBuilder: (value) {
                      final date = dateformat.format(value.createdAt!);
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: Text(
                          date,
                          style: const TextStyle(
                            color: Colors.black,
                            fontWeight: FontWeight.bold,
                            fontSize: 20.0,
                          ),
                        ),
                      );
                    },
                    itemBuilder: (_, Item item) {
                      return TransactionCard(item: item);
                    },
                    separator: const Padding(
                      padding: EdgeInsets.symmetric(vertical: 8.0),
                    ),
                  ),
                );
              }
            },
          ),
        ],

        //TO DO REFACTOR
      ),
    );
  }
}

class TransactionCard extends StatelessWidget {
  final Item item;
  final DataController dataController = Get.find<DataController>();

  TransactionCard({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        showTransactionDialog(
          context: context,
          data: item,
        );
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 15.h, vertical: 16.h),
        decoration: AppDecoration.outlineGray
            .copyWith(borderRadius: BorderRadiusStyle.circleBorder22),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: EdgeInsets.only(left: 8.h),
              child: Container(
                margin: EdgeInsets.only(top: 3.h, bottom: 2.h),
                padding: EdgeInsets.all(7.h),
                decoration: AppDecoration.fillAGray.copyWith(
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Padding(
                    padding: const EdgeInsets.all(6.0),
                    child: Text(
                      item.kittyTitle?.isNotEmpty ?? false
                          ? item.kittyTitle![0]
                          : '',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
            ),
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(left: 14.h),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${item.kittyTitle}-${item.kittyId}',
                      overflow: TextOverflow.ellipsis,
                      style: CustomTextStyles.labelMediumff545963,
                    ),
                    Opacity(
                      opacity: 0.8,
                      child: Text(
                        item.transactionCode ?? item.internalId ?? "",
                        style: theme.textTheme.bodySmall,
                      ),
                    ),
                    Opacity(
                      opacity: 0.5,
                      child: Text(
                        '${item.status}',
                        style: TextStyle(
                            color: item.status == "SUCCESS"
                                ? Colors.green
                                : item.status
                                            ?.toUpperCase()
                                            .contains("FAILED") ??
                                        false
                                    ? Colors.red
                                    : const Color(0xFF95730C)),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Column(
              children: [
                Align(
                  alignment: Alignment.centerRight,
                  child: Text(
                    '${item.typeInOut == "OUT" ? '-' : '+'} ${item.currencyCode ?? "KES"}${item.amount.toString()}',
                    style: TextStyle(
                      color: '${item.typeInOut}' == "OUT"
                          ? Colors.red
                          : Colors.green,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
                SizedBox(height: 2.h),
                Opacity(
                  opacity: 0.2,
                  child: Text(
                    DateFormat.jm().format(item.createdAt!.toLocal()),
                    style: theme.textTheme.bodySmall,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

showTransactionDialog({
  required BuildContext context,
  required Item data,
  bool singleTrans = true,
}) {
  return showAnimatedDialog(
    barrierDismissible: true,
    animationType: DialogTransitionType.sizeFade,
    curve: Curves.fastOutSlowIn,
    duration: const Duration(milliseconds: 900),
    context: context,
    builder: (BuildContext context) {
      final DateFormat format = DateFormat.MMMEd().add_jms();
      DateTime createdAt = data.createdAt!.toLocal();

      return Dialog(
        child: SizedBox(
          height: SizeConfig.screenHeight * .36,
          width: SizeConfig.screenWidth * .8,
          child: Padding(
            padding: EdgeInsets.symmetric(
                horizontal: getProportionalScreenWidth(10)),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                    margin: EdgeInsets.only(top: 3.h, bottom: 2.h),
                    padding: EdgeInsets.all(7.h),
                    decoration: AppDecoration.fillAGray.copyWith(
                      shape: BoxShape.circle,
                    ),
                    child: Center(
                      child: Padding(
                        padding: const EdgeInsets.all(6.0),
                        child: Text(
                          '${data.kittyTitle?.isNotEmpty ?? false ? data.kittyTitle![0] : ''} ',
                          style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold),
                        ),
                      ),
                    )),
                SizedBox(height: 9.h),
                Text(data.kittyTitle ?? "",
                    style: CustomTextStyles.titleSmallIndigo500),
                SizedBox(height: 7.h),
                Text(
                  '${data.typeInOut == "OUT" ? '-' : '+'} ${data.currencyCode ?? "KES"}${(data.amount).toString()}',
                  style: TextStyle(
                    color: '${data.typeInOut}' == "OUT"
                        ? Colors.red
                        : Colors.green,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
                SizedBox(height: 7.h),
                SizedBox(height: 5.h),
                Text(data.phoneNumber.toString(), style: const TextStyle()),
                SizedBox(height: 7.h),
                Text("Transaction ID: ${data.transactionCode ?? ""}",
                    style: const TextStyle()),
                SizedBox(height: 8.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).canvasColor,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20.sp),
                          side: BorderSide(
                              width: 2.sp,
                              color: Theme.of(context).primaryColor),
                        ),
                      ),
                      onPressed: () async {
                        String shareMsg =
                            // "Phone Number: ${details?.phoneNumber}\nAmount: KSH ${details?.amount}\nTransaction Code: ${details?.resultCode}\nStatus: ${details?.resultDesc}\nChannel:${details?.channel}\nDate: ${format.format(createdAt.toLocal())}\nKitty: https://onekitty.co.ke/kitty/${details?.kittyId}";

                            "Phone Number: ${data.phoneNumber}\nAmount: KSH ${data.amount}\nTransaction Code: ${data.transactionCode}\nStatus: ${data.status}\nDate: ${format.format(createdAt.toLocal())}\nKitty: https://onekitty.co.ke/kitty/${data.kittyId}";
                        await Share.share(shareMsg,
                            subject: 'Transaction details');
                      },
                      child: const Text(
                        "Share",
                        style: TextStyle(color: Colors.black),
                      ),
                    ),
                    const Spacer(),
                    CustomElevatedButton(
                      buttonStyle: ButtonStyle(
                        backgroundColor: WidgetStateProperty.all<Color>(
                            const Color.fromARGB(255, 184, 129, 57)),
                      ),
                      width: 90.w,
                      height: 30.h,
                      text: "Export",
                      buttonTextStyle: TextStyle(
                          fontSize: 12.h, fontWeight: FontWeight.bold),
                      leftIcon: Container(
                          margin: EdgeInsets.only(right: 1.w),
                          child: CustomImageView(
                              imagePath: AssetUrl.expIcon,
                              height: 12.h,
                              width: 12.w)),
                      onPressed: () async {
                        showModalBottomSheet(
                          context: context,
                          builder: (BuildContext context) {
                            return ExportContentWidget(
                              singleTrans: singleTrans,
                              transaction: data,
                            );
                          },
                        );
                      },
                    ),
                    SizedBox(
                      height: 10.sp,
                    ),
                  ],
                )
              ],
            ),
          ),
        ),
      );
    },
  );
}
