import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onekitty/models/events/events_model.dart';
import 'package:onekitty/utils/date_formatter.dart';
import 'package:onekitty/utils/my_quill_editor.dart';
import 'package:onekitty/utils/search_widget.dart';
import 'package:onekitty/utils/show_cached_network_image.dart';
import 'package:onekitty/utils/utils_exports.dart';
import '../screens/dashboard/pages/events/view_single_event_organizer.dart';
import 'event_details.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/events/view_single_event.dart';

class MyEventCards extends StatelessWidget {
  final bool? organizer;
  final MyEventsModel eventModel;
  const MyEventCards({super.key, required this.eventModel, this.organizer});

  @override
  Widget build(BuildContext context) {
    final event = eventModel.event;
    return GestureDetector(
      onTap: () {
        Get.put(ViewSingleEventController()).event(event);
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => ViewSingleEventOrganizer(
                      eventmodel: eventModel,
                    )));
      },
      child: Card(
        elevation: 4,
        margin: const EdgeInsets.all(8),
        color: Colors.white,
        child: Padding(
          padding: EdgeInsets.only(
            left: 6.spMin,
            right: 6.spMin,
            top: 6.spMin,
            bottom: 10.spMin,
          ),
          child: Column(
            children: [
              Hero(
                tag: 'image:${event.id}o',
                child: AdaptiveCachedNetworkImage(
                  imageUrl: event.eventMedia == null ||
                          event.eventMedia!.isEmpty
                      ? AssetUrl.onekittyBannnerUrl
                      : event.eventMedia?[0].url ?? AssetUrl.onekittyBannnerUrl,
                  initialWidth: 390.w,
                  borderRadius: 8.r,
                  initialHeight: 300.h,
                ),
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Material(
                      color: Colors.transparent,
                      textStyle: TextStyle(
                        color: Colors.grey.shade700,
                        fontWeight: FontWeight.w400,
                        fontSize: 14,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(vertical: 8.0),
                            child: Hero(
                              tag: 'text:${event.id}o',
                              child: Material(
                                color: Colors.transparent,
                                child: Text(event.title,
                                    style: const TextStyle(
                                        color: Colors.black,
                                        fontWeight: FontWeight.w700,
                                        fontSize: 20)),
                              ),
                            ),
                          ),
                          EventDetailsWidget(
                              id: "location${event.id}o",
                              label: "${event.locationTip} - ${event.venue}",
                              image: 'assets/images/icons/location.png',
                              icon: Icons.location_on_outlined),
                          EventDetailsWidget(
                              id: "date${event.id}o",
                              image: 'assets/images/icons/calendar.png',
                              label:
                                  '${formatDate("${event.startDate?.toLocal()}")}, ${formatTime("${event.startDate?.toLocal()}")}',
                              icon: Icons.calendar_month),
                        ],
                      ),
                    ),
                  ),
                  if (organizer ?? false)
                    Hero(
                      tag: 'collected${event.id}o',
                      child: Material(
                        color: Colors.transparent,
                        child: Text.rich(
                            textAlign: TextAlign.end,
                            TextSpan(children: [
                              TextSpan(
                                  text: FormattedCurrency()
                                          .getFormattedCurrency(
                                              eventModel.event.balance ?? 0.0) +
                                      "\n",
                                  style: const TextStyle(
                                      color: Color(0xff4355b6),
                                      fontSize: 30,
                                      fontWeight: FontWeight.w600)),
                              const TextSpan(text: 'Collected')
                            ])),
                      ),
                    )
                ],
              ),
              Align(
                  alignment: Alignment.topLeft,
                  child: Hero(
                    tag: 'desc:${event.id}o',
                    child:
                        QuillEditorWidget(maxLines: 1, text: event.description),
                  )),
              if (organizer ?? false)
                AttendeesWidget(
                  count: eventModel.count,
                  size: 18,
                  textSize: 15,
                )
            ],
          ),
        ),
      ),
    );
  }
}
