import 'dart:convert';

class ChamaOcurenceModel {
  final int id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final DateTime? startDate;
  final DateTime? endDate;
  final dynamic occurenceCount;
  final String response;
  final bool isError;
  final int totalBenfsAmount;
  final int amount;
  final int chamaSavedAmount;
  final int chamaBalance;
  final String responseCode;
  final int memberId;
  final double percentageBen;
  final double percentageChama;
  final ChamaBeneficiary? chamaBeneficiary;
  final int chamaBeneficiaryId;
  final String requestId;
  final int chamaId;

  ChamaOcurenceModel({
    this.id = 0,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.startDate,
    this.endDate,
    this.occurenceCount,
    this.response = '',
    this.isError = false,
    this.totalBenfsAmount = 0,
    this.amount = 0,
    this.chamaSavedAmount = 0,
    this.chamaBalance = 0,
    this.responseCode = '',
    this.memberId = 0,
    this.percentageBen = 0.0,
    this.percentageChama = 0.0,
    this.chamaBeneficiary,
    this.chamaBeneficiaryId = 0,
    this.requestId = '',
    this.chamaId = 0,
  });

  factory ChamaOcurenceModel.fromRawJson(String str) =>
      ChamaOcurenceModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ChamaOcurenceModel.fromJson(Map<String, dynamic> json) =>
      ChamaOcurenceModel(
        id: json["ID"] ?? 0,
        createdAt: json["CreatedAt"] != null
            ? DateTime.parse(json["CreatedAt"])
            : null,
        updatedAt: json["UpdatedAt"] != null
            ? DateTime.parse(json["UpdatedAt"])
            : null,
        deletedAt: json["DeletedAt"],
        startDate: json["start_date"] != null
            ? DateTime.parse(json["start_date"])
            : null,
        endDate:
            json["end_date"] != null ? DateTime.parse(json["end_date"]) : null,
        occurenceCount: json["occurence_count"] ?? 0,
        response: json["response"] ?? '',
        isError: json["is_error"] ?? false,
        totalBenfsAmount: json["total_benfs_amount"] ?? 0,
        amount: json["amount"] ?? 0,
        chamaSavedAmount: json["chama_saved_amount"] ?? 0,
        chamaBalance: json["chama_balance"] ?? 0,
        responseCode: json["response_code"] ?? '',
        memberId: json["member_id"] ?? 0,
        percentageBen: (json["percentage_ben"] ?? 0).toDouble(),
        percentageChama: (json["percentage_chama"] ?? 0).toDouble(),
        chamaBeneficiary: json["chama_beneficiary"] != null
            ? ChamaBeneficiary.fromJson(json["chama_beneficiary"])
            : null,
        chamaBeneficiaryId: json["chama_beneficiary_id"] ?? 0,
        requestId: json["request_id"] ?? '',
        chamaId: json["chama_id"] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "start_date": startDate?.toIso8601String(),
        "end_date": endDate?.toIso8601String(),
        "occurence_count": occurenceCount,
        "response": response,
        "is_error": isError,
        "total_benfs_amount": totalBenfsAmount,
        "amount": amount,
        "chama_saved_amount": chamaSavedAmount,
        "chama_balance": chamaBalance,
        "response_code": responseCode,
        "member_id": memberId,
        "percentage_ben": percentageBen,
        "percentage_chama": percentageChama,
        "chama_beneficiary": chamaBeneficiary?.toJson(),
        "chama_beneficiary_id": chamaBeneficiaryId,
        "request_id": requestId,
        "chama_id": chamaId,
      };
}

class ChamaBeneficiary {
  final int id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String title;
  final int userId;
  final int chamaId;
  final int memberId;
  final String transferMode;
  final double percentage;
  final String beneficiaryType;
  final String channelName;
  final int channel;
  final String status;
  final String accountNumber;
  final String accountNumberRef;

  ChamaBeneficiary({
    this.id = 0,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.title = '',
    this.userId = 0,
    this.chamaId = 0,
    this.memberId = 0,
    this.transferMode = '',
    this.percentage = 0.0,
    this.beneficiaryType = '',
    this.channelName = '',
    this.channel = 0,
    this.status = '',
    this.accountNumber = '',
    this.accountNumberRef = '',
  });

  factory ChamaBeneficiary.fromRawJson(String str) =>
      ChamaBeneficiary.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ChamaBeneficiary.fromJson(Map<String, dynamic> json) =>
      ChamaBeneficiary(
        id: json["ID"] ?? 0,
        createdAt: json["CreatedAt"] != null
            ? DateTime.parse(json["CreatedAt"])
            : null,
        updatedAt: json["UpdatedAt"] != null
            ? DateTime.parse(json["UpdatedAt"])
            : null,
        deletedAt: json["DeletedAt"],
        title: json["title"] ?? '',
        userId: json["user_id"] ?? 0,
        chamaId: json["chama_id"] ?? 0,
        memberId: json["member_id"] ?? 0,
        transferMode: json["transfer_mode"] ?? '',
        percentage: (json["percentage"] ?? 0).toDouble(),
        beneficiaryType: json["beneficiary_type"] ?? '',
        channelName: json["channel_name"] ?? '',
        channel: json["channel"] ?? 0,
        status: json["status"] ?? '',
        accountNumber: json["account_number"] ?? '',
        accountNumberRef: json["account_number_ref"] ?? '',
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "title": title,
        "user_id": userId,
        "chama_id": chamaId,
        "member_id": memberId,
        "transfer_mode": transferMode,
        "percentage": percentage,
        "beneficiary_type": beneficiaryType,
        "channel_name": channelName,
        "channel": channel,
        "status": status,
        "account_number": accountNumber,
        "account_number_ref": accountNumberRef,
      };
}
