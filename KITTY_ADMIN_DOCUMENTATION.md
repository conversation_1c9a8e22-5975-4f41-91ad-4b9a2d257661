# Kitty Admin Documentation

## Overview
The Kitty Admin module provides administrative functionality for managing kitties (contribution groups) in the OneKitty application. This documentation covers how kitties are fetched, displayed, filtered, and viewed.

## 1. Kitty Admin Screen Structure

### Main Components
- **KittyAdmin** (`lib/admin_screens/kitty/kitty_admin.dart`)
- **<PERSON>AdminController** (`lib/controllers/admin/kitty/kitty_controller.dart`)
- **KittyTable** (Data grid component)
- **ChamaFilterWidget** (Filtering component)

## 2. Data Fetching

### API Endpoint
- **URL**: `admin/get-kitties/`
- **Method**: GET
- **Base URL**: Configurable (Live/Dev/Local)

### Request Parameters
The API accepts the following query parameters:
```
?page={page}&size={size}&search={search}&chama_id={chamaId}&frequency={frequency}&kitty_id={kittyId}&start_date={startDate}&end_date={endDate}
```

| Parameter | Type | Description |
|-----------|------|-------------|
| `page` | int | Page number for pagination (default: 0) |
| `size` | int | Number of items per page (default: 15) |
| `search` | string | Search term for kitty names |
| `chama_id` | string | Filter by specific chama ID |
| `frequency` | string | Filter by frequency |
| `kitty_id` | string | Filter by specific kitty ID |
| `start_date` | string | Filter by start date (ISO format) |
| `end_date` | string | Filter by end date (ISO format) |

### Controller Implementation
```dart
Future fetchKitties(int page) async {
  try {
    isLoading(true);
    var response = await apiProvider.request(
      method: Method.GET,
      url: "${ApiUrls.getAllKitties}?page=$page&size=${size.value}&search=${search.value}&chama_id=${chamaId.value}&frequency=${frequency.value}&kitty_id=${kittyId.value}&start_date=${startDate.value}&end_date=${endDate.value}",
    );
    
    if (response.data['status'] ?? false) {
      kitties((response.data['data']['results']['items'] as List)
          .map((e) => Kitty.fromJson(e['kitty']))
          .toList());
    }
  } catch (e) {
    logger.e('Error fetching Kitties: $e');
  } finally {
    isLoading(false);
  }
}
```

## 3. Table Display Values

### Data Grid Columns
The kitty table displays the following columns:

| Column | Data Source | Format | Width |
|--------|-------------|--------|-------|
| **ID** | `kitty.id` | String | 80px |
| **Created At** | `kitty.createdAt` | `dd/MM/yy HH:mm` | 120px |
| **Title** | `kitty.title` | String | 150px |
| **Description** | `kitty.description` | String | 200px |
| **Beneficiary Account** | `kitty.beneficiaryAccount` | String | 150px |
| **Channel** | `kitty.beneficiaryChannel` | String | 150px |
| **Phone** | `kitty.beneficiaryPhoneNumber` | String | 130px |
| **End Date** | `kitty.endDate` | `dd/MM/yy HH:mm` | 120px |
| **Balance** | `kitty.balance` | Currency (KES) | 120px |
| **Phone Number** | `kitty.phoneNumber` | String | 130px |

### Data Source Implementation
```dart
class KittyDataSource extends DataGridSource {
  KittyDataSource({required List<Kitty> kitties}) {
    dataGridRows = kitties.map<DataGridRow>((kitty) => DataGridRow(cells: [
      DataGridCell<String>(columnName: 'id', value: kitty.id?.toString() ?? '-'),
      DataGridCell<String>(columnName: 'created_at', 
          value: DateFormat('dd/MM/yy HH:mm').format(kitty.createdAt ?? DateTime.now())),
      DataGridCell<String>(columnName: 'title', value: kitty.title ?? '-'),
      DataGridCell<String>(columnName: 'description', value: kitty.description ?? '-'),
      DataGridCell<String>(columnName: 'beneficiary_account', value: kitty.beneficiaryAccount ?? '-'),
      DataGridCell<String>(columnName: 'beneficiary_channel', value: kitty.beneficiaryChannel ?? '-'),
      DataGridCell<String>(columnName: 'beneficiary_phone', value: kitty.beneficiaryPhoneNumber ?? '-'),
      DataGridCell<String>(columnName: 'end_date', 
          value: kitty.endDate != null ? DateFormat('dd/MM/yy HH:mm').format(kitty.endDate!) : '-'),
      DataGridCell<String>(columnName: 'balance', 
          value: kitty.balance != null ? NumberFormat.currency(symbol: 'KES ').format(kitty.balance) : '-'),
      DataGridCell<String>(columnName: 'phone_number', value: kitty.phoneNumber ?? '-'),
    ])).toList();
  }
}
```

## 4. Filtering Functionality

### Filter Widget Components
The `ChamaFilterWidget` provides the following filters:

#### Date Range Filters
- **Start Date**: Date picker for filtering from a specific date
- **End Date**: Date picker for filtering to a specific date
- Both dates are stored in ISO format and can be cleared

#### Search Filters
- **Kitty ID Filter**: Text input with 1-second debounce
- **Phone Number Filter**: Text input with 1-second debounce  
- **Search by Name**: Text input with 1-second debounce

### Filter Implementation
```dart
// Date selection
Future<void> _selectDate(BuildContext context, {required bool isStartDate}) async {
  final DateTime? picked = await showDatePicker(
    context: context,
    initialDate: DateTime.now(),
    firstDate: DateTime(2000),
    lastDate: isStartDate ? DateTime.now() : DateTime.now().add(const Duration(days: 365)),
  );

  if (picked != null) {
    if (isStartDate) {
      controller.startDate.value = picked.toUtc().toIso8601String();
    } else {
      controller.endDate.value = picked.toUtc().toIso8601String();
    }
    controller.fetchKitties(0);
  }
}

// Search with debounce
onChanged: (val) {
  if (_debounce?.isActive ?? false) _debounce!.cancel();
  _debounce = Timer(const Duration(seconds: 1), () async {
    controller.search.value = val;
    controller.fetchKitties(0);
  });
}
```

## 5. Kitty Click Navigation

### On Cell Tap Event
When a user clicks on any cell in the kitty table:

```dart
onCellTap: (details) {
  var dataController = Get.put(DataController());
  dataController.kitty.value = UserKitty(
      kitty: controller.kitties[details.rowColumnIndex.rowIndex - 1]);

  Navigator.pushNamed(
    context,
    NavRoutes.viewingsinglekittyScreen,
  );
}
```

### Navigation Flow
1. **Click Detection**: Any cell click triggers the `onCellTap` callback
2. **Data Setting**: Selected kitty is stored in `DataController.kitty`
3. **Navigation**: User is navigated to `viewingsinglekittyScreen`
4. **Route**: `/view_single_kitty`

## 6. Single Kitty View Page

### Screen: `ViewingSingleKittyScreen`
**Location**: `lib/screens/dashboard/pages/contribution/viewing_single_kitty/viewing_single_kitty_page.dart`

### Key Features Displayed

#### Header Section
- **Kitty Title**: Large, prominent display
- **Balance**: Formatted currency (KES)
- **Status Chip**: Color-coded status indicator
- **Creation Date**: Formatted date display
- **End Date**: Relative time display ("Ends in X days")

#### Media Section
- **Image Carousel**: If kitty has media attachments
- **Add Photo**: Button to upload images
- **Share**: Share kitty details
- **QR Code**: Generate QR code for payments

#### Details Section
- **Description**: Rich text editor display
- **Beneficiary Information**: Account details and payment channel
- **WhatsApp Groups**: Connected groups for notifications

#### Action Tabs
1. **Services Tab**:
   - Payment options
   - Contribution services
   - Settings access

2. **Transactions Tab**:
   - Transaction history
   - Payment records
   - Contribution tracking

### Data Refresh
```dart
void onRefresh() async {
  controller.getLocalUser();
  getKittyController.kittyMedia.clear();
  await getKittyController.getKitty(id: dataController.kitty.value.kitty?.id);
  Get.put(SettingsController()).fetchSettings(dataController.kitty.value.kitty?.id ?? 0);
  _refreshController.refreshCompleted();
}
```

### Status Color Coding
```dart
Color getkittyStatusColor(String status) {
  switch (status.toLowerCase()) {
    case "active": return const Color(0xFF56AF57);
    case "completed": return const Color(0xFF56AF57);
    case "settlement initiated": return const Color.fromARGB(255, 206, 104, 192);
    default: return const Color(0xFFEE5B60);
  }
}
```

## 7. Data Models

### Kitty Model Structure
```dart
class Kitty {
  int? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  String? title;
  String? description;
  String? beneficiaryAccount;
  String? beneficiaryChannel;
  String? beneficiaryPhoneNumber;
  DateTime? endDate;
  dynamic balance;
  dynamic limit;
  int? settlementType;
  String? phoneNumber;
  List<KittyMediaModel>? media;
  bool? hasSignatories;
}
```

### UserKitty Wrapper
```dart
class UserKitty {
  Kitty? kitty;
  String? kittyStatus;
  String? kittBeneficiaryChannel;
  String? kittyType;
}
```

## 8. Key Features Summary

### Admin Table Features
- ✅ Sortable columns
- ✅ Resizable columns
- ✅ Frozen first column
- ✅ Cell-based navigation
- ✅ Loading states
- ✅ Error handling

### Filtering Features
- ✅ Date range filtering
- ✅ Text search with debounce
- ✅ Multiple filter criteria
- ✅ Clear filter options
- ✅ Real-time filtering

### Single Kitty View Features
- ✅ Comprehensive kitty details
- ✅ Media management
- ✅ Social sharing
- ✅ QR code generation
- ✅ Transaction history
- ✅ WhatsApp integration
- ✅ Pull-to-refresh
- ✅ Responsive design

## 9. Technical Implementation Notes

### State Management
- Uses **GetX** for reactive state management
- Controllers handle API calls and data state
- Observable variables for real-time UI updates

### UI Components
- **SfDataGrid** for table display
- **CustomScrollView** with slivers for single kitty view
- **TabBarView** for organized content sections
- **SmartRefresher** for pull-to-refresh functionality

### Performance Optimizations
- Debounced search inputs (1-second delay)
- Lazy loading with pagination
- Efficient data grid rendering
- Image caching for media content