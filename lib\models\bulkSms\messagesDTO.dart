// To parse this JSON data, do
//
//     final messagesDto = messagesDtoFromJson(jsonString);

import 'dart:convert';

import 'package:flutter_contacts/contact.dart';

MessagesDto messagesDtoFromJson(String str) =>
    MessagesDto.fromJson(json.decode(str));

String messagesDtoToJson(MessagesDto data) => json.encode(data.toJson());

class MessagesDto {
  int userId;
  String message;
  List<Contact> recipient;
  final double? latitude;
  final double? longitude;

  MessagesDto({
    required this.userId,
    required this.message,
    required this.recipient,
    this.latitude,
    this.longitude,
  });

  factory MessagesDto.fromJson(Map<String, dynamic> json) => MessagesDto(
        userId: json["user_id"],
        message: json["message"],
        recipient: List<Contact>.from(json["recipient"].map((x) => x)),
        latitude: double.tryParse(json['latitude'] as String? ?? ''),
        longitude: double.tryParse(json['longitude'] as String? ?? ''),
      );

  Map<String, dynamic> toJson() => {
        "user_id": userId,
        "message": message,
        "recipient": List<dynamic>.from(
            recipient.map((x) => x.phones.first.normalizedNumber)),
        'latitude': latitude?.toString(),
        'longitude': longitude?.toString(),
      };
}
