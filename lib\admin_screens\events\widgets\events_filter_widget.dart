import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/admin/events/events_admin_controller.dart';

class EventsFilterWidget extends StatelessWidget {
  const EventsFilterWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<EventsAdminController>();
    Timer? _debounce;
    Timer? _debounce2;
    return Align(
      alignment: Alignment.center,
      child: Wrap(
        spacing: 8,
        alignment: WrapAlignment.start,
        crossAxisAlignment: WrapCrossAlignment.start,
        children: [
          InkWell(
            onTap: () => _selectDate(context, isStartDate: true),
            child: Container(
              width: 300,
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.grey.withOpacity(0.2),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Obx(() => Text(
                        controller.startDate.isEmpty
                            ? 'Start Date'
                            : _formatDate(controller.startDate.value),
                        style: const TextStyle(
                          fontSize: 14,
                        ),
                      )),
                  const Icon(
                    Icons.calendar_today,
                    size: 20,
                  ),
                ],
              ),
            ),
          ),

          InkWell(
            onTap: () => _selectDate(context, isStartDate: false),
            child: Container(
              width: 300,
              margin: const EdgeInsets.all(8),
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.grey.withOpacity(0.2),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Obx(() => Text(
                        controller.endDate.isEmpty
                            ? 'End Date'
                            : _formatDate(controller.endDate.value),
                        style: const TextStyle(
                          fontSize: 14,
                        ),
                      )),
                  const Icon(
                    Icons.calendar_today,
                    size: 20,
                  ),
                ],
              ),
            ),
          ),

          // Kitty ID Filter
          Container(
            margin: const EdgeInsets.all(8),
            width: 300,
            child: TextField(
              // onChanged: (value) => controller.kittyId.value = value,
              onChanged: (val) {
                // Cancel the previous timer if it's still active
                if (_debounce?.isActive ?? false) {
                  _debounce!.cancel();
                } 
                 _debounce = Timer(const Duration(seconds: 1), () async {
                  controller.kittyId.value = val;
                  controller.getEvents(0);
                });
              },
              style: const TextStyle(color: Colors.white),
              decoration: InputDecoration(
                suffixIcon: const Icon(
                  Icons.search,
                ),
                hintText: 'Filter by Kitty-ID...',
                //filled: true,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ),
          // Phone Number Filter
          Container(
            margin: const EdgeInsets.all(8),
            width: 300,
            child: TextField(
              // onChanged: (value) => controller.chamaId.value = value,
              onChanged: (val) {
                // Cancel the previous timer if it's still active
                if (_debounce2?.isActive ?? false) {
                  _debounce2!.cancel();
                }

                // Set a new timer to execute the function after 1 second of inactivity
                _debounce2 = Timer(const Duration(seconds: 1), () async {
                  controller.phoneNumber(val);
                  controller.getEvents(0);
                });
              },
              style: const TextStyle(color: Colors.white),
              decoration: InputDecoration(
                suffixIcon: const Icon(
                  Icons.search,
                ),
                hintText: 'Filter by phoneNumber',
                //filled: true,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide.none,
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ),
       //search widget
           Container(
              margin: const EdgeInsets.all(8),
              width: 300,
              child: TextField(
                // onChanged: (value) => controller.chamaId.value = value,
                onChanged: (val) {
                  // Cancel the previous timer if it's still active
                  if (_debounce2?.isActive ?? false) {
                    _debounce2!.cancel();
                  }
      
                  // Set a new timer to execute the function after 1 second of inactivity
                  _debounce2 = Timer(const Duration(seconds: 1), () async {
                    controller.search.value = val;
                    controller.getEvents(0);
                  });
                },
                style: const TextStyle(color: Colors.white),
                decoration: InputDecoration(
                  suffixIcon: const Icon(
                    Icons.search,
                  ),
                  hintText: 'Search by name',
                  //filled: true,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide.none,
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
              ),
            ),
       
        ],
      ),
    );
  }

  String _formatDate(String date) {
    if (date.isEmpty) return '';
    final DateTime dateTime = DateTime.parse(date);
    return DateFormat('MMMM d, yyyy').format(dateTime);
  }

  Future<void> _selectDate(BuildContext context,
      {required bool isStartDate}) async {
    final controller = Get.find<EventsAdminController>();
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: isStartDate
          ? DateTime.now()
          : DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      final formattedDate = DateFormat('yyyy-MM-dd').format(picked);
      if (isStartDate) {
        controller.startDate.value = picked.toUtc().toIso8601String();
      } else {
        controller.endDate.value = picked.toUtc().toIso8601String();
      }
      controller.events.clear();
      controller.getEvents(0);
    }
  }
}
