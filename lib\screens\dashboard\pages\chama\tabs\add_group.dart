import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage_pro/get_storage_pro.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';

import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/custom_button.dart';

class AddGroup extends StatefulWidget {
  const AddGroup({super.key});

  @override
  State<AddGroup> createState() => _AddGroupState();
}

class _AddGroupState extends State<AddGroup> {
  final ChamaController _chamaController = Get.find<ChamaController>();
  final ChamaDataController _dataController = Get.put(ChamaDataController());
  TextEditingController linkController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  final box = GetStorage();
  bool isLoading = false;
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: Container(
          margin: const EdgeInsets.all(20),
          child: Form(
            key: formKey,
            child: Column(
              children: [
                Text(
                  "Connect WhatsApp Group",
                  style:
                      context.titleText?.copyWith(fontWeight: FontWeight.bold),
                ),
                Text(_dataController.singleChamaDts.value.title ?? ""),
                const SizedBox(
                  height: 30,
                ),
                Align(
                  alignment: Alignment.topLeft,
                  child: Text(
                    "Whatsapp group link",
                    style: context.titleText,
                  ),
                ),
                CustomTextField(
                  hintText:
                      "e.g https://chat.whatsapp.com/K8jDByxIyv4HLjCn32bEp0",
                  labelText: "Paste your whatsapp link",
                  controller: linkController,
                  validator: (p0) {
                    if (p0!.isEmpty) {
                      return "Provide a link";
                    } else if (!RegExp(
                            r'^(https:\/\/chat\.whatsapp\.com\/[A-Za-z0-9]{22})$')
                        .hasMatch(p0)) {
                      return "Enter a valid WhatsApp link address";
                    }
                    return null;
                  },
                ),
                Align(
                  alignment: Alignment.topLeft,
                  child: Text(
                    "Chama Email",
                    style: context.titleText,
                  ),
                ),
                CustomTextField(
                  controller: emailController,
                  hintText: "e.g <EMAIL>",
                  labelText: "Enter Your Chama Email",
                  validator: (p0) {
                    if (p0!.isEmpty) {
                      return "Email cannot be empty";
                    } else if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                        .hasMatch(p0)) {
                      return "Enter a valid email address";
                    }
                    return null;
                  },
                ),
                const SizedBox(
                  height: 20,
                ),
                SizedBox(
                  height: 50,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      OutlinedButton(
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          child: Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 12.0),
                            child: Text(
                              "Back",
                              style: context.dividerTextLarge
                                  ?.copyWith(color: AppColors.primary),
                            ),
                          )),
                      Obx(() => CustomKtButton(
                            width: 100.w,
                            isLoading: _chamaController.isAddingG.isTrue,
                            onPress: () async {
                              if (formKey.currentState!.validate()) {
                                final res = await _chamaController.addGroup(
                                  chId:
                                      _dataController.singleChamaDts.value.id ??
                                          1,
                                  link: linkController.text.trim(),
                                  email: emailController.text,
                                );
                                if (res) {
                                  ToastUtils.showSuccessToast(
                                      context,
                                      _chamaController.apiMessage.string,
                                      "Success");
                                  Get.offAndToNamed(NavRoutes.singleChama);
                                } else {
                                  ToastUtils.showErrorToast(
                                      context,
                                      _chamaController.apiMessage.string,
                                      "Error");
                                }
                              }
                            },
                            btnText: "Submit",
                          ))
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
