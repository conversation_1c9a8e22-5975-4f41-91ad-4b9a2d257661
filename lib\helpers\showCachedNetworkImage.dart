import 'package:fast_cached_network_image/fast_cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/utils/themes_colors.dart';
import 'package:photo_view/photo_view.dart';
import 'package:shimmer/shimmer.dart';

class ShowCachedNetworkImage extends StatelessWidget {
  const ShowCachedNetworkImage({
    super.key,
    required this.imageurl,
    this.loaderSize,
    this.fit,
    this.height,
    this.width,
    this.black = false,
    this.telegramLoading = false,
    this.zoomImage = false,
  });

  final String imageurl;
  final double? loaderSize;
  final double? height;
  final BoxFit? fit;
  final double? width;
  final bool? black;
  final bool telegramLoading;
  final bool zoomImage;

  @override
  Widget build(BuildContext context) {
    if (imageurl.isEmpty) {
      return const Icon(
        Icons.error,
        color: Colors.red,
      );
    }

    Widget imageWidget = FastCachedImage(
      url: imageurl,
      fit: fit ?? BoxFit.cover,
      height: height ?? 25,
      width: width ?? 35,
      fadeInDuration: const Duration(seconds: 1),
      errorBuilder: (context, exception, stacktrace) {
        Logger().e(exception);
        return const Icon(Icons.error);
      },
      loadingBuilder: (context, progress) {
        if (telegramLoading) {
          return _buildTelegramLoader(context, progress);
        }
        return _buildDefaultLoader(context, progress);
      },
    );

    if (zoomImage) {
      imageWidget = GestureDetector(
        onTap: () {
          _showZoomableImage(context);
        },
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  Widget _buildDefaultLoader(BuildContext context,   progress) {
    return Container(
      height: height ?? 25,
      width: width ?? 25,
      color: black ?? false ? Colors.black : Theme.of(context).primaryColor,
      child: Stack(
        alignment: Alignment.center,
        children: [
          if (progress.downloadedBytes != null && progress.totalBytes != null)
            Text(
              '${progress.downloadedBytes! ~/ 1024} / ${progress.totalBytes! ~/ 1024} kb',
              style: TextStyle(
                color: ColorUtil.blueColor,
              ),
            ),
          SizedBox(
            width: 120,
            height: 120,
            child: CircularProgressIndicator(
              color: black ?? false ? Colors.black : ColorUtil.blueColor,
              value: progress.downloadedBytes != null && progress.totalBytes != null
                  ? progress.downloadedBytes! / progress.totalBytes!
                  : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTelegramLoader(BuildContext context,   progress) {
    return Container(
      height: height ?? 120,
      width: width ?? 120,
      color: black ?? false ? Colors.black.withOpacity(0.1) : Colors.grey[200],
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Telegram-style blur placeholder
          Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: Container(
              height: height ?? 400,
              width: width ?? 120,
              color: Colors.white,
            ),
          ),
          // Progressive loading indicator
          if (progress.downloadedBytes != null)
            Container(
              height: 50,
              width: 50,
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.5),
                shape: BoxShape.circle,
              ),
              child: CircularProgressIndicator(
                color: Colors.white,
                value: progress.totalBytes != null
                    ? progress.downloadedBytes! / progress.totalBytes!
                    : null,
                strokeWidth: 2,
              ),
            ),
        ],
      ),
    );
  }

  void _showZoomableImage(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => Scaffold(
          backgroundColor: Colors.black,
          body: Stack(
            children: [
              PhotoView(
                imageProvider: NetworkImage(imageurl),
                minScale: PhotoViewComputedScale.contained,
                maxScale: PhotoViewComputedScale.covered * 2,
                initialScale: PhotoViewComputedScale.contained,
                backgroundDecoration: const BoxDecoration(
                  color: Colors.black,
                ),
              ),
              Positioned(
                top: 40,
                left: 20,
                child: IconButton(
                  icon: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 30,
                  ),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}