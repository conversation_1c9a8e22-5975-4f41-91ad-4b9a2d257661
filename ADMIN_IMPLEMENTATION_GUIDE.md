# Admin System Implementation Guide

## Step-by-Step Implementation

### 1. Project Structure Setup

Create the following directory structure:
```
lib/
├── admin_screens/
│   ├── admin_screen.dart
│   ├── drawer.dart
│   ├── chama/
│   ├── events/
│   ├── kitty/
│   ├── settings/
│   ├── common/
│   └── utils/
├── controllers/
│   └── admin/
│       ├── dashboard_controller.dart
│       ├── chama/
│       ├── events/
│       └── kitty/
```

### 2. Dependencies Required

Add these to your `pubspec.yaml`:
```yaml
dependencies:
  get: ^4.6.6
  syncfusion_flutter_datagrid: ^23.1.36
  logger: ^2.0.2+1
  intl: ^0.18.1
  excel: ^2.1.0
  file_picker: ^6.1.1
  path_provider: ^2.1.1
```

### 3. Main Admin Screen Implementation

**File**: `lib/admin_screens/admin_screen.dart`
```dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:your_app/controllers/admin/dashboard_controller.dart';
import 'drawer.dart';

class AdminScreen extends StatefulWidget {
  const AdminScreen({super.key});

  @override
  State<AdminScreen> createState() => _AdminScreenState();
}

class _AdminScreenState extends State<AdminScreen> {
  final controller = Get.put(DashboardController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: const AdminDrawer(),
      appBar: AppBar(
        centerTitle: true,
        leading: MediaQuery.of(context).size.width > 600
            ? null
            : Builder(builder: (context) {
                return IconButton(
                  icon: const Icon(Icons.menu),
                  onPressed: () {
                    Scaffold.of(context).openDrawer();
                  },
                );
              }),
        title: const Text("Admin Screen"),
      ),
      body: Row(
        children: [
          if (MediaQuery.of(context).size.width > 600)
            SizedBox(
              width: MediaQuery.of(context).size.width > 600 &&
                      MediaQuery.of(context).size.width < 720
                  ? 60
                  : 300,
              child: const AdminDrawer(),
            ),
          Expanded(
            child: Obx(() => controller.AdminPages(controller.pageNo.value)),
          ),
        ],
      ),
    );
  }
}
```

### 4. Dashboard Controller Implementation

**File**: `lib/controllers/admin/dashboard_controller.dart`
```dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:your_app/admin_screens/chama/chama_admin.dart';
import 'package:your_app/admin_screens/events/events_admin.dart';
import 'package:your_app/admin_screens/settings/settings.dart';
import 'package:your_app/admin_screens/kitty/kitty_admin.dart';

class DashboardController extends GetxController {
  final pageNo = 0.obs;

  Widget AdminPages(int pageNo) {
    switch (pageNo) {
      case 0:
        return const ChamaAdmin();
      case 1:
        return const EventsAdmin();
      case 2:
        return const SettingsPage();
      case 3:
        return const KittyAdmin();
      default:
        return const ChamaAdmin();
    }
  }

  void logOutUser() async {
    // Implement logout logic
  }
}
```

### 5. Admin Drawer Implementation

**File**: `lib/admin_screens/drawer.dart`
```dart
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:your_app/controllers/admin/dashboard_controller.dart';

class AdminDrawer extends StatelessWidget {
  const AdminDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(DashboardController());
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  radius: 30,
                  child: Icon(Icons.admin_panel_settings),
                ),
                const SizedBox(height: 10),
                if (MediaQuery.of(context).size.width < 600 ||
                    MediaQuery.of(context).size.width > 720)
                  const Text(
                    'Admin Panel',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                    ),
                  ),
              ],
            ),
          ),
          MyDrawerListTile(
            icon: Icons.dashboard,
            label: 'Chama Dashboard',
            onTap: () => controller.pageNo(0),
          ),
          MyDrawerListTile(
            icon: Icons.event,
            label: 'Events Dashboard',
            onTap: () => controller.pageNo(1),
          ),
          MyDrawerListTile(
            icon: Icons.savings,
            label: 'Kitty Dashboard',
            onTap: () => controller.pageNo(3),
          ),
          MyDrawerListTile(
            icon: Icons.settings,
            label: 'Settings',
            onTap: () => controller.pageNo(2),
          ),
          const Divider(),
          MyDrawerListTile(
            icon: Icons.exit_to_app,
            label: 'Logout',
            onTap: () => controller.logOutUser(),
          ),
        ],
      ),
    );
  }
}

class MyDrawerListTile extends StatelessWidget {
  final String label;
  final IconData icon;
  final Function()? onTap;

  const MyDrawerListTile({
    super.key,
    required this.label,
    required this.icon,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    if (MediaQuery.of(context).size.width > 600 &&
        MediaQuery.of(context).size.width < 720) {
      return Tooltip(
        message: label,
        child: ListTile(leading: Icon(icon), onTap: onTap),
      );
    } else {
      return ListTile(
        leading: Icon(icon),
        title: Text(label),
        onTap: onTap,
      );
    }
  }
}
```

### 6. Base Admin Controller Pattern

**File**: `lib/controllers/admin/base_admin_controller.dart`
```dart
import 'package:get/get.dart';
import 'package:logger/logger.dart';

abstract class BaseAdminController extends GetxController implements GetxService {
  // Common observable variables
  final isLoading = false.obs;
  final currentPage = 0.obs;
  final size = 15.obs;
  final search = ''.obs;
  final startDate = ''.obs;
  final endDate = ''.obs;
  
  // Pagination
  final maxPage = 0.obs;
  final totalPages = 0.obs;
  final isLast = false.obs;
  final isFirst = true.obs;
  
  // Services
  final logger = Logger();
  
  // Abstract methods to be implemented by subclasses
  Future<void> fetchData(int page);
  void clearFilters();
  
  // Common pagination methods
  void onNextPage() {
    if (!isLast.value) {
      fetchData(currentPage.value + 1);
    }
  }
  
  void onPreviousPage() {
    if (!isFirst.value) {
      fetchData(currentPage.value - 1);
    }
  }
  
  void onRefresh() {
    fetchData(0);
  }
}
```

### 7. Custom Form Dialog Implementation

**File**: `lib/admin_screens/utils/my_form_dialog.dart`
```dart
import 'package:flutter/material.dart';

void showMyFormDialog({
  required BuildContext context,
  required String title,
  String? description,
  required List<Widget> contents,
  String? okText,
  String? cancelText,
  Function()? onOkPressed,
  Function()? onCancelPressed,
}) {
  showDialog(
    context: context,
    builder: (context) => MyFormDialog(
      title: title,
      contents: contents,
      description: description,
      okText: okText,
      cancelText: cancelText,
      onOkPressed: onOkPressed,
      onCancelPressed: onCancelPressed,
    ),
  );
}

class MyFormDialog extends StatelessWidget {
  final String title;
  final String? description;
  final List<Widget> contents;
  final String? okText;
  final String? cancelText;
  final Function()? onOkPressed;
  final Function()? onCancelPressed;

  const MyFormDialog({
    super.key,
    required this.title,
    this.description,
    required this.contents,
    this.okText,
    this.cancelText,
    this.onOkPressed,
    this.onCancelPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 600, minWidth: 380),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 20,
                ),
              ),
              if (description != null) Text(description!),
              const SizedBox(height: 16),
              ...contents,
              const SizedBox(height: 16),
              Row(
                children: [
                  if (onCancelPressed != null)
                    Expanded(
                      child: ElevatedButton(
                        onPressed: onCancelPressed,
                        child: Text(cancelText ?? 'Cancel'),
                      ),
                    ),
                  if (onOkPressed != null && onCancelPressed != null)
                    const SizedBox(width: 8),
                  if (onOkPressed != null)
                    Expanded(
                      child: ElevatedButton(
                        onPressed: onOkPressed,
                        child: Text(okText ?? 'OK'),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
```

### 8. Data Grid Implementation Pattern

**File**: `lib/admin_screens/common/base_data_grid.dart`
```dart
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';

class BaseDataGrid<T> extends StatelessWidget {
  final List<GridColumn> columns;
  final DataGridSource dataSource;
  final Widget? footer;

  const BaseDataGrid({
    super.key,
    required this.columns,
    required this.dataSource,
    this.footer,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: SfDataGrid(
            source: dataSource,
            columns: columns,
            allowSorting: true,
            allowFiltering: true,
            gridLinesVisibility: GridLinesVisibility.both,
            headerGridLinesVisibility: GridLinesVisibility.both,
          ),
        ),
        if (footer != null) footer!,
      ],
    );
  }
}
```

### 9. HTTP Service Integration

**File**: `lib/services/http_service.dart`
```dart
import 'package:dio/dio.dart';
import 'package:get/get.dart';

enum Method { GET, POST, PUT, DELETE, PATCH }

class HttpService extends GetxService {
  late Dio _dio;

  @override
  void onInit() {
    super.onInit();
    _dio = Dio();
    _dio.options.baseUrl = 'https://your-api-base-url.com/api/';
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
  }

  Future<Response> request({
    required Method method,
    required String url,
    Map<String, dynamic>? params,
    Options? options,
  }) async {
    try {
      switch (method) {
        case Method.GET:
          return await _dio.get(url, options: options);
        case Method.POST:
          return await _dio.post(url, data: params, options: options);
        case Method.PUT:
          return await _dio.put(url, data: params, options: options);
        case Method.DELETE:
          return await _dio.delete(url, options: options);
        case Method.PATCH:
          return await _dio.patch(url, data: params, options: options);
      }
    } catch (e) {
      rethrow;
    }
  }
}
```

### 10. Specific Controller Example - Chama Admin

**File**: `lib/controllers/admin/chama/chama_admin_controller.dart`
```dart
import 'package:get/get.dart';
import 'package:your_app/controllers/admin/base_admin_controller.dart';
import 'package:your_app/models/chama_model.dart';
import 'package:your_app/services/http_service.dart';

class ChamaAdminController extends BaseAdminController {
  final chamas = <Chama>[].obs;
  final chamaDetails = Chama().obs;
  final HttpService apiProvider = Get.find();

  @override
  void onInit() {
    super.onInit();
    fetchData(0);
  }

  @override
  Future<void> fetchData(int page) async {
    try {
      isLoading(true);
      currentPage(page);

      final url = "chamas?page=$page&size=${size.value}&search=${search.value}";

      var response = await apiProvider.request(
        method: Method.GET,
        url: url,
      );

      if (response.statusCode == 200 && response.data['status'] == true) {
        var data = response.data['data'];
        chamas.value = (data['items'] as List)
            .map((e) => Chama.fromJson(e))
            .toList();

        totalPages(data['total_pages']);
        isLast(data['last']);
        isFirst(data['first']);
      }
    } catch (e) {
      logger.e('Error fetching chamas: $e');
    } finally {
      isLoading(false);
    }
  }

  @override
  void clearFilters() {
    search('');
    startDate('');
    endDate('');
    fetchData(0);
  }

  Future<void> getChamaDetails(int chamaId) async {
    try {
      isLoading(true);
      var response = await apiProvider.request(
        method: Method.GET,
        url: "chamas/$chamaId",
      );

      if (response.statusCode == 200) {
        chamaDetails(Chama.fromJson(response.data['data']));
      }
    } catch (e) {
      logger.e('Error fetching chama details: $e');
    } finally {
      isLoading(false);
    }
  }
}
```

This implementation guide provides the foundation for creating a complete admin system. Each component follows the established patterns and can be extended based on specific requirements.
