import 'package:dio/dio.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:get_storage_pro/get_storage_pro.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/utils/cache_keys.dart';
import 'custom_logger.dart';

enum Method { POST, GET, PUT, DELETE, PATCH }

class HttpService {
  HttpService();

  // Use your live base URL from the API URLs file.
  static String? baseUrl = ApiUrls.BASE_URL_LIVE;
  static var box = GetStorage();
  Dio? dio = Dio(
    BaseOptions(
      baseUrl: baseUrl ?? "",
      receiveDataWhenStatusError: false,
      followRedirects: true,
      receiveTimeout: const Duration(minutes: 2),
      sendTimeout: const Duration(minutes: 1),
      headers: {
        'Content-Type': 'application/json',
        'Accept': '*/*',
        // Note: CORS headers are typically set by the server. Including them
        // here is for demonstration purposes and might not affect the browser's CORS behavior.
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization',
      },
      validateStatus: (status) => status! < 500,
    ),
  );

  String? mAccessToken = "";
  DateTime? mAccessExpiresAt;
  final logger = Logger(filter: CustomLogFilter());

  // Build headers for each request.
  Future<Map<String, String>> header() async {
    mAccessToken = await FirebaseAuth.instance.currentUser?.getIdToken() ??
        box.read(CacheKeys.token);
    if (mAccessToken != null && mAccessToken!.isNotEmpty) {
      return {
        "Content-Type": "application/json",
        "Authorization": "Bearer $mAccessToken",
        'Accept': '*/*',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization',
      };
    } else {
      return {
        'Content-Type': 'application/json',
        'Accept': '*/*',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept, Authorization',
      };
    }
  }

  Future<Response> request({
    required String url,
    required Method method,
    Map<String, dynamic>? params,
    Options? options,
    FormData? formdata,
  }) async {
    Response response;
    try {
      // Update headers before making the request.
      final headers = await header();
      dio!.options.headers.addAll(headers);

      // Choose the request type.
      if (method == Method.POST) {
        if (formdata == null) {
          response = await dio!.post(
            url,
            data: params,
            options: Options(validateStatus: (status) => true),
          );
        } else {
          response = await dio!.post(url, data: formdata, options: options);
        }
      } else if (method == Method.PUT) {
        response = await dio!.put(url, data: params);
      } else if (method == Method.DELETE) {
        response = await dio!.delete(url);
      } else if (method == Method.PATCH) {
        response = await dio!.patch(url);
      } else {
        // For GET requests, include queryParameters if provided.
        response = await dio!.get(url, queryParameters: params);
      }
      return response;
    } on DioError catch (e) {
      logger.e("DioError caught: ${e.message}");
      if (e.type == DioErrorType.connectionTimeout ||
          e.type == DioErrorType.sendTimeout ||
          e.type == DioErrorType.receiveTimeout) {
        throw Exception("Deadline Exceeded Exception");
      } else if (e.type == DioErrorType.unknown) {
        throw Exception("No internet connection");
      }
      throw Exception("Request failed: ${e.message}");
    } on FormatException catch (e) {
      logger.e(e);
      throw Exception("Bad response format");
    } catch (e) {
      logger.e(e);
      throw Exception("Something went wrong");
    }
  }
}
