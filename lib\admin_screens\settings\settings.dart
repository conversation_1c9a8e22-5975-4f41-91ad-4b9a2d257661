import 'package:flutter/material.dart';
import 'package:onekitty/admin_screens/settings/pages/getAllEvents.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  @override
  Widget build(BuildContext context) {
    return  Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: ListView(children: [ 
        ListTile(
          title: const Text('Get All Events'),
          onTap: () {
            Navigator.push(context, MaterialPageRoute(builder: (context) => const GetAllEvents()));
          },
        ),
      
        ListTile(
          title: const Text('Logout'),
          onTap: () {
            Navigator.pushNamed(context, '/logout');
          },
        ),
      ],)
    );
  }
}