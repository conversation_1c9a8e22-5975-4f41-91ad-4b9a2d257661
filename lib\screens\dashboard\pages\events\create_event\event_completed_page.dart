import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/events/controllers.dart';
import 'package:onekitty/controllers/events/create_event_controller.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/screens/dashboard/pages/events/manage_delegates.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

class EventCompletedPage extends StatelessWidget {
  final String eventName, username;

  // final ConfettiController _confetti1 =
  //     ConfettiController(duration: const Duration(seconds: 3));
  // final ConfettiController _confetti2 =
  //     ConfettiController(duration: const Duration(seconds: 3));

  EventCompletedPage(
      {super.key, required this.eventName, required this.username});

  @override
  Widget build(BuildContext context) {
    // _confetti1.play();
    // _confetti2.play();

    final ValueNotifier<int> _page = ValueNotifier(0);
    final controller = Get.put(CreateEventController());
    return ValueListenableBuilder(
        valueListenable: _page,
        builder: (context, page, child) {
          return page == 0
              ? Padding(
                  padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 20.h),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.check_circle_outlined,
                        color: Colors.green,
                        size: 120.sp,
                      ),
                      SizedBox(height: 20.h),
                      RichText(
                        textAlign: TextAlign.center,
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: eventName,
                              style: TextStyle(
                                fontSize: 20.sp,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                            ),
                            TextSpan(
                              text: ' was\nsuccessfully created',
                              style: TextStyle(
                                fontSize: 20.sp,
                                color: Colors.black,
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 30.h),
                      ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        style: ElevatedButton.styleFrom(
                          side: const BorderSide(color: primaryColor),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(24.r),
                          ),
                          padding: EdgeInsets.symmetric(
                              horizontal: 100.w, vertical: 12.h),
                        ),
                        child: Text(
                          'Okay',
                          style: TextStyle(fontSize: 16.sp),
                        ),
                      ),
                      SizedBox(height: 20.h),
                      Text(
                        'Share the event link to friends and families to register',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            color: Colors.grey.shade700, fontSize: 14.spMin),
                      ),
                      SizedBox(height: 20.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          GestureDetector(
                            onTap: () async {
                              final url =
                                  'https://www.onekitty.co.ke/events/$username';
                              Share.share(
                                'Check out this event: $url',
                                subject: eventName,
                              );
                            },
                            child: Image.asset(
                                'assets/images/icons/instagram.png',
                                color: primaryColor,
                                height: 24,
                                width: 24),
                          ),
                          SizedBox(width: 24.w),
                          GestureDetector(
                            onTap: () {
                              final url =
                                  'https://www.onekitty.co.ke/events/$username';
                              Share.share(
                                'Check out this event: $url',
                                subject: eventName,
                              );
                            },
                            child: Image.asset(
                                'assets/images/icons/whatsapp.png',
                                color: primaryColor,
                                height: 24,
                                width: 24),
                          ),
                          SizedBox(width: 24.w),
                          GestureDetector(
                            onTap: () async {
                              final Uri url = Uri.parse(
                                  "https://twitter.com/intent/tweet?text=https://www.onekitty.co.ke/events/$username");
                              if (!await launchUrl(url)) {
                                ToastUtils.showErrorToast(context,
                                    "Could not launch the url", "Error");
                              }
                            },
                            child: Image.asset(
                                'assets/images/icons/twitter.png',
                                color: primaryColor,
                                height: 24,
                                width: 24),
                          ),
                          SizedBox(width: 12.w),
                          GestureDetector(
                            onTap: () async {
                              // final url =
                              //     'https://www.onekitty.co.ke/events/${username}';
                              // Share.share(
                              //   'Check out this event: $url',
                              //   subject: eventName,
                              // );
                              final Uri url = Uri.parse(
                                  "https://www.facebook.com/sharer/sharer.php?quote=https://www.onekitty.co.ke/events/$username");
                              if (!await launchUrl(url)) {
                                ToastUtils.showErrorToast(context,
                                    "Could not launch the url", "Error");
                              }
                            },
                            child: Image.asset(
                                'assets/images/icons/facebook.png',
                                color: primaryColor,
                                height: 24,
                                width: 24),
                          ),
                          SizedBox(width: 12.w),
                          IconButton(
                            icon: const Icon(Icons.link,
                                color: primaryColor, size: 24),
                            onPressed: () {
                              final url =
                                  'https://www.onekitty.co.ke/events/$username'; // Replace with actual URL

                              Share.share(
                                'Check out this event: $url',
                                subject: eventName,
                              );
                            },
                          ),
                        ],
                      ),
                      SizedBox(height: 20.h),
                      Align(
                        alignment: Alignment.centerRight,
                        child: TextButton(
                          onPressed: () {
                            Get.to(() => InvitePage(
                                kittyId: controller.kittyId.value,
                                eventname: controller.eventName.value));
                          },
                          child: Text(
                            'Invite event operators →',
                            style:
                                TextStyle(color: primaryColor, fontSize: 16.sp),
                          ),
                        ),
                      ),
                    ],
                  ),
                )
              : InvitePage(
                  eventname: controller.eventName.value,
                  kittyId: controller.kittyId.value,
                );
        });
  }
}
