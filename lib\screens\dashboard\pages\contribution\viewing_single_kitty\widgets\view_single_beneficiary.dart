import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/models/kitty/beneficiary_model.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/contribution_kitties/create_beneficiary.dart';
import 'package:onekitty/utils/formatted_currency.dart';
import 'package:onekitty/utils/my_button.dart';
import 'package:onekitty/utils/timeSince.dart';

class ViewSingleBeneficiary extends StatelessWidget {
  final BeneficiaryModel beneficiary;

  const ViewSingleBeneficiary({super.key, required this.beneficiary});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Beneficiary Details'),
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0.sp),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 16.sp),
            Text(
              'Beneficiary Name: ${beneficiary.accountName}',
              style: TextStyle(fontSize: 20.sp, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 10.sp),
            Text(
              'Account Number: ${beneficiary.accountNumber}',
              style: TextStyle(fontSize: 16.sp),
            ),
            SizedBox(height: 10.sp),
            Text(
              'Transfer Mode: ${beneficiary.transferMode} - ${beneficiary.channelName}',
              style: TextStyle(fontSize: 16.sp),
            ),
            SizedBox(height: 10.sp),
            if (beneficiary.transferMode == "PAYBILL")
              Text(
                'Account Number Reference: ${beneficiary.accountNumberRef}',
                style: TextStyle(fontSize: 16.sp),
              ),
            SizedBox(height: 10.sp),
            if (beneficiary.endDate != null)
              Text.rich(TextSpan(children: [
                TextSpan(
                    text:
                        'End Date: ${DateFormat('d MMM HH:MM a').format(beneficiary.endDate!)}'),
                TextSpan(
                    style: const TextStyle(
                        // fontSize: 12.spMin,
                        ),
                    text:
                        ' - ${highPrecisiontimeSince(beneficiary.endDate!.toLocal())}')
              ])),
            SizedBox(height: 10.sp),
            Row(
              children: [
                Text("${beneficiary.splitConfig?.toLowerCase() ?? ''}: "),
                beneficiary.splitConfig == "AMOUNT"
                    ? Text(
                        "${FormattedCurrency().getFormattedCurrency(beneficiary.amount ?? 0)}",
                      )
                    : SizedBox(
                        width: 60.w,
                        height: 60.h,
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            CircularProgressIndicator(
                              value: beneficiary.percentage?.toDouble(),
                              strokeWidth: 4,
                              backgroundColor: Colors.transparent,
                              valueColor: const AlwaysStoppedAnimation<Color>(
                                  AppColors.primary),
                            ),
                            Text(
                              '${((beneficiary.percentage ?? 0) * 100).toStringAsFixed(1)}%',
                              style: TextStyle(fontSize: 8.spMin),
                            ),
                          ],
                        ),
                      ),
              ],
            ),
            SizedBox(height: 20.sp),
            MyButton(
              onClick: () {
                Get.to(() => CreateBeneficiary(
                    kittyId:
                        Get.find<DataController>().kitty.value.kitty?.id ?? 0,
                    edit: beneficiary));
              },
              label: 'Edit',
            ),
          ],
        ),
      ),
    );
  }
}
