import 'package:flutter/material.dart';

import '../utils_exports.dart';

// ignore: must_be_immutable
class AppbarTitle extends StatelessWidget {
  AppbarTitle({
    super.key,
    required this.text,
    this.margin,
    this.onTap,
    this.textColor,
    this.textSize,
  });

  String text;

  EdgeInsetsGeometry? margin;

  Function? onTap;

  final Color? textColor;
  final double? textSize;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onTap?.call();
      },
      child: Padding(
        padding: margin ?? EdgeInsets.zero,
        child: Text(
          text,
          style: theme.textTheme.titleMedium!.copyWith(
            color: textColor ?? appTheme.black900,
            fontSize: textSize ?? 18.0,
          ),
        ),
      ),
    );
  }
}
