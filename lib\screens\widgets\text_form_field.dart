import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomTextField extends StatelessWidget {
  final TextEditingController controller;
  final double? height;
  final double? width;
  final Function(String val)? onChanged;
  final String labelText;
  final String hintText;
  final IconData? prefixIcon;
  final bool? isRequired;
  final bool? readOnly;
  final int? maxLength;
  final bool obscureText;
  final bool showNoKeyboard;
  final List<TextInputFormatter>? inputFormatters;
  final double paddingHorizontal;
  final Widget? suffixIcon;
  final Widget? prefix;
  final String? Function(String?)? validator;
  final String? initialValue;
  final TextInputType? keyboardType;

  const CustomTextField({
    super.key,
    required this.controller,
    this.height,
    this.labelText = 'Enter text',
    this.hintText = '',
    this.suffixIcon,
    this.readOnly,
    this.width,
    this.maxLength,
    this.prefixIcon,
    this.inputFormatters,
    this.paddingHorizontal = 0,
    this.obscureText = false,
    this.isRequired = false,
    this.showNoKeyboard = false,
    this.validator,
    this.initialValue,
    this.prefix,
    this.onChanged,
    this.keyboardType,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height,
      width: width ?? double.infinity,
      child: Padding(
        padding: EdgeInsets.symmetric(
            horizontal: paddingHorizontal, vertical: paddingHorizontal),
        child: SizedBox(
          //height: height ?? 65.h,
          child: TextFormField(
            readOnly: readOnly ?? false,
            initialValue: initialValue,
            maxLength: maxLength,
            controller: controller,
            obscureText: obscureText,
            inputFormatters: showNoKeyboard
                ? [
                    FilteringTextInputFormatter.allow(RegExp("[0-9]")),
                  ]
                : null,
            keyboardType:
                keyboardType ?? (showNoKeyboard ? TextInputType.number : null),
            validator: validator,
            onChanged: onChanged,
            decoration: InputDecoration(
              prefix: prefix,
              label: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(labelText),
                  Visibility(
                    visible: isRequired ?? false,
                    child: Text(
                      " *",
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 13.sp,
                        color: Theme.of(context).colorScheme.error,
                      ),
                    ),
                  )
                ],
              ),
              suffix: suffixIcon,
              counter: const SizedBox.shrink(),
              hintText: hintText,
              prefixIcon: prefixIcon != null ? Icon(prefixIcon) : null,
              border: const OutlineInputBorder(),
              //filled: true,
              // fillColor: Colors.blueAccent.withOpacity(0.1),
            ),
          ),
        ),
      ),
    );
  }
}
