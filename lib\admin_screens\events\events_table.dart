import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/admin/events/events_admin_controller.dart';
import 'package:onekitty/controllers/events/view_single_event.dart';
import 'package:onekitty/models/events/events_model.dart';
import 'package:onekitty/screens/dashboard/pages/events/view_single_event_organizer.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';

import '../common/color_codes.dart';
import '../common/colum_widths.dart';

class EventDataSource extends DataGridSource {
  EventDataSource({required List<Event> eventData}) {
    dataGridRows = eventData
        .map<DataGridRow>((event) => DataGridRow(cells: [
              DataGridCell<String>(
                  columnName: 'id', value: event.id.toString()),
              DataGridCell<String>(
                  columnName: 'created_at',
                  value: DateFormat('dd/MM/yy HH:mm')
                      .format(event.createdAt ?? DateTime.now())
                      .toString()),
              DataGridCell<String>(columnName: 'title', value: event.title),
              DataGridCell<String>(
                  columnName: 'username', value: event.username),
                  
              DataGridCell<String>(
                  columnName: 'kittyId', value: event.kittyId.toString()),
              DataGridCell<String>(columnName: 'email', value: event.email),
              DataGridCell<String>(
                  columnName: 'phoneNumber', value: event.phoneNumber),
              DataGridCell<String>(columnName: 'venue', value: event.venue),
              DataGridCell<String>(
                  columnName: 'startDate',
                  value: DateFormat('dd/MM/yy HH:mm')
                      .format(event.startDate ?? DateTime.now())
                      .toString()),
              DataGridCell<String>(
                  columnName: 'endDate',
                  value: DateFormat('dd/MM/yy HH:mm')
                      .format(event.endDate ?? DateTime.now())
                      .toString()),
              DataGridCell<String>(
                  columnName: 'status', value: event.status?.name.toString()),
            ]))
        .toList();
  }

  List<DataGridRow> dataGridRows = [];

  @override
  List<DataGridRow> get rows => dataGridRows;

  @override
  DataGridRowAdapter? buildRow(DataGridRow row) {
    return DataGridRowAdapter(
        cells: row.getCells().map<Widget>((dataGridCell) {
      if (dataGridCell.columnName == 'status') {
        return Text(
          dataGridCell.value.toString(),
          style:
              TextStyle(color: getStatusColor(dataGridCell.value.toString())),
        );
      }
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        alignment: Alignment.centerLeft,
        child: Text(dataGridCell.value.toString()),
      );
    }).toList());
  }
}

class EventTable extends StatefulWidget {
  const EventTable({super.key});

  @override
  State<EventTable> createState() => _EventTableState();
}

class _EventTableState extends State<EventTable> {
  @override
  Widget build(BuildContext context) {
    final controller = Get.put(EventsAdminController());
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Obx(
        () => SfDataGrid(
          source: EventDataSource(eventData: controller.events),
          allowColumnsResizing: true,
          columnResizeMode: ColumnResizeMode.onResize,
          gridLinesVisibility: GridLinesVisibility.both,
          headerGridLinesVisibility: GridLinesVisibility.both,
          isScrollbarAlwaysShown: false,
          // shrinkWrapColumns: true,
          // shrinkWrapRows: true,

          frozenColumnsCount: 1,
          navigationMode: GridNavigationMode.cell,
          // verticalScrollPhysics: const NeverScrollableScrollPhysics(),
          columnWidthMode: ColumnWidthMode.fitByCellValue,
          onCellTap: (details) {
            Get.put(ViewSingleEventController())
                .event(controller.events[details.rowColumnIndex.rowIndex - 1]);
            Get.to(
                () => ViewSingleEventOrganizer(
                      eventmodel: MyEventsModel(
                          event: controller
                              .events[details.rowColumnIndex.rowIndex - 1]),
                    ),
                transition: Transition.rightToLeft);
          },
          onColumnResizeUpdate: (ColumnResizeUpdateDetails details) {
            setState(() {
              final double newWidth = details.width;
              switch (details.column.columnName) {
                case 'id':
                  ColumnWidths.idWidth.value = newWidth;
                  break;
                   case 'kittyId':
                  ColumnWidths.kittyIdWidth.value = newWidth;
                  break;
                case 'created_at':
                  ColumnWidths.createdAtWidth.value = newWidth;
                  break;
                case 'title':
                  ColumnWidths.titleWidth.value = newWidth;
                  break;
                case 'username':
                  ColumnWidths.usernameWidth.value = newWidth;
                  break;
                case 'email':
                  ColumnWidths.emailWidth.value = newWidth;
                  break;
                case 'phone':
                  ColumnWidths.phoneWidth.value = newWidth;
                  break;
                case 'amount':
                  ColumnWidths.amountWidth.value = newWidth;
                  break;
                case 'status':
                  ColumnWidths.statusWidth.value = newWidth;
                  break;
                case 'actions':
                  ColumnWidths.actionsWidth.value = newWidth;
                  break;
              }
              print('Column ${details.column.columnName} resized to $newWidth');
            });
            return true;
          },

          columns: <GridColumn>[
            GridColumn(
              columnName: 'id',
              width: ColumnWidths.idWidth.value,
              label: Container(
                padding: const EdgeInsets.all(8.0),
                alignment: Alignment.center,
                child: const Text('ID'),
              ),
            ),
            GridColumn(
              columnName: 'created_at',
              width: ColumnWidths.createdAtWidth.value,
              label: Container(
                padding: const EdgeInsets.all(8.0),
                alignment: Alignment.center,
                child: const Text('Created At'),
              ),
            ),
            GridColumn(
              columnName: 'title',
              width: ColumnWidths.titleWidth.value,
              label: Container(
                padding: const EdgeInsets.all(8.0),
                alignment: Alignment.center,
                child: const Text('Title'),
              ),
            ),
            GridColumn(
              columnName: 'username',
              width: ColumnWidths.usernameWidth.value,
              label: Container(
                padding: const EdgeInsets.all(8.0),
                alignment: Alignment.center,
                child: const Text('Username'),
              ),
            ),
            GridColumn(
              columnName: 'kittyId',
              width: ColumnWidths.idWidth.value,
              label: Container(
                padding: const EdgeInsets.all(8.0),
                alignment: Alignment.center,
                child: const Text('Kitty ID'),
              ),
            ),
            GridColumn(
              columnName: 'email',
              width: ColumnWidths.emailWidth.value,
              label: Container(
                padding: const EdgeInsets.all(8.0),
                alignment: Alignment.center,
                child: const Text('Email'),
              ),
            ),
            GridColumn(
              columnName: 'phoneNumber',
              width: ColumnWidths.phoneWidth.value,
              label: Container(
                padding: const EdgeInsets.all(8.0),
                alignment: Alignment.center,
                child: const Text('Phone Number'),
              ),
            ),
            GridColumn(
              columnName: 'venue',
              width: ColumnWidths.venueWidth.value,
              label: Container(
                padding: const EdgeInsets.all(8.0),
                alignment: Alignment.center,
                child: const Text('Venue'),
              ),
            ),
            GridColumn(
              columnName: 'startDate',
              width: ColumnWidths.startDateWidth.value,
              label: Container(
                padding: const EdgeInsets.all(8.0),
                alignment: Alignment.center,
                child: const Text('Start Date'),
              ),
            ),
            GridColumn(
              columnName: 'endDate',
              width: ColumnWidths.endDateWidth.value,
              label: Container(
                padding: const EdgeInsets.all(8.0),
                alignment: Alignment.center,
                child: const Text('End Date'),
              ),
            ),
            GridColumn(
              columnName: 'status',
              width: ColumnWidths.statusWidth.value,
              minimumWidth: 80,
              label: Container(
                padding: const EdgeInsets.all(8.0),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                ),
                alignment: Alignment.centerLeft,
                child: const Text('Status'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
