import 'dart:async';
import 'dart:io';
import 'package:connectivity_checker/connectivity_checker.dart';  
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage_pro/get_storage_pro.dart';
import 'package:onekitty/services/analytics.dart';
import 'package:responsive_framework/responsive_framework.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/onboarding/login_screen.dart';
import 'package:onekitty/services/auth_manager.dart'; 
import 'package:onekitty/services/init_service.dart';
import 'package:onekitty/utils/cache_keys.dart'; 
import 'package:onekitty/utils/utils_exports.dart';
import 'screens/bottom_navbar_screens/botton_navigation_section/bottom_nav_section.dart';
import 'screens/onboarding/passwd_req_screen.dart';
import 'screens/onboarding/splash_Screens/splash_screen.dart';
import 'utils/themes_colors.dart';

final isLight = ValueNotifier(true);

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  // setPathUrlStrategy();
  HttpOverrides.global = MyHttpOverrides();
  await servicesInitialize(); 
  runApp(const ProviderScope(child: MyMaterialAppSuper()));
}

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}

class Materialhome extends StatefulWidget {
  const Materialhome({super.key});

  @override
  State<Materialhome> createState() => _MaterialhomeState();
}

class _MaterialhomeState extends State<Materialhome> {
  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      theme: ThemeData(fontFamily: "Sen"),
      home: const MyMaterialAppSuper(),
    );
  }
}

class MyMaterialAppSuper extends StatefulWidget {
  const MyMaterialAppSuper({super.key});
  static void restartApp(BuildContext context) {
    context.findAncestorStateOfType<_MyMaterialAppSuperState>()?.restartApp();
  }

  @override
  State<MyMaterialAppSuper> createState() => _MyMaterialAppSuperState();
}

class _MyMaterialAppSuperState extends State<MyMaterialAppSuper> {
  // static final FirebaseMessaging _firebaseMessaging =
  //     FirebaseMessaging.instance;
  // final pushNotificationService = PushNotificationService(_firebaseMessaging);

  final _navigatorKey = GlobalKey<NavigatorState>();
  StreamSubscription<Uri>? _linkSubscription;

  @override
  void dispose() {
    _linkSubscription?.cancel();
    super.dispose();
  }
  Timer? _timer;
  final _authenticationManager = Get.put(AuthenticationManager());
  @override
  void initState() {
    super.initState();
    _initializeTimer();
    //pushNotificationService.setupInteractedMessage();
  }
  void _initializeTimer() {
    _authenticationManager.checkLoginStatus();

    if (_timer != null) {
      _timer!.cancel();
    }
    if (_authenticationManager.isLogged.isTrue) {
      _timer = Timer(const Duration(minutes: 5), _logOutUser);
      debugPrint("Countdown: ${_timer?.tick} ");
    }
  }
  void _logOutUser() async {
    _timer?.cancel();
    _timer = null;
    // Skip logout if in debug mode
    if (kDebugMode) return;
    if (_authenticationManager.isLogged.isTrue) {
      var isAuthenticationSuccesfull = await Get.to(
        () => const  AuthPasswdScreen(),
        arguments: [false],
      );
      if (isAuthenticationSuccesfull == true) {
        _initializeTimer();
      }
    }
  }
  void _handleUserInteraction([_]) {
    _initializeTimer();
  }
  Key appKey = UniqueKey();

  void restartApp() {
    setState(() {
      appKey = UniqueKey(); // Generate a new key to rebuild the entire app
    });
  }

  static final box = GetStorage();

  @override
  Widget build(BuildContext context) {
    isLight.value = box.read(CacheKeys.isLight) ?? true;
    return ValueListenableBuilder(
      valueListenable: isLight,
      builder: (context, isLight, _) => GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: _handleUserInteraction,
        onPanDown: _handleUserInteraction,
        onPanUpdate: _handleUserInteraction,
        child: ConnectivityAppWrapper(
          app: GetMaterialApp(
            key: appKey,
            navigatorObservers: <NavigatorObserver>[AnalyticsEngine.observer],
            navigatorKey: _navigatorKey,
            debugShowCheckedModeBanner: false,
            title: 'onekitty',
            themeMode: isLight ? ThemeMode.light : ThemeMode.dark,
            darkTheme: EbonyClayTheme().darkTheme,
            theme: EbonyClayTheme().darkTheme,
            initialRoute: '/',
            getPages: [
              GetPage(
                name: '/',
                page: () => const MaterialHome(),
              ),
               
              GetPage(
                name: '/loginPage',
                page: () => const LoginScreen(),
              ),
              GetPage(
                name: '/passreqPage',
                page: () => const AuthPasswdScreen(),
              ),
              GetPage(
                name: '/bottom_navigation',
                page: () => BottomNavSection(),
              ),
              ...NavRoutes.routes.entries.map(
                (entry) => GetPage(
                  name: entry.key,
                  page: () => entry.value(context),
                ),
              ),
            ],
            builder: EasyLoading.init(
              builder: (context, child) {
                BouncingScrollWrapper.builder(context, child!);
                var myWidget = ResponsiveBreakpoints.builder(
                  child: child,
                  breakpoints: [
                    const Breakpoint(start: 0, end: 450, name: MOBILE),
                    const Breakpoint(start: 451, end: 800, name: TABLET),
                    const Breakpoint(start: 801, end: 1920, name: DESKTOP),
                    const Breakpoint(
                        start: 1921, end: double.infinity, name: '4K'),
                  ],
                );
                return myWidget;
              },
            ),
            home: const MaterialHome(),
          ),
        ),
      ),
    );
  }
}

class MaterialHome extends StatefulWidget {
  const MaterialHome({super.key});

  @override
  State<MaterialHome> createState() => _MaterialHomeState();
}

class _MaterialHomeState extends State<MaterialHome> {
  @override
  Widget build(BuildContext context) {
    // Initialize ScreenUtil.
    ScreenUtil.init(context);
    return ScreenUtilInit(
      designSize: const Size(820, 800),
      builder: (context, child) {
        return const SplashScreen();
      },
    );
  }
}
