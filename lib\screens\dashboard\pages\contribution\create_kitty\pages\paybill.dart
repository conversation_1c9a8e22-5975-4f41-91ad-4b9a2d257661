import 'package:flutter/material.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/widgets/date_picker.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/widgets/row_widget.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/common_strings.dart';

class PayBill extends StatelessWidget {
  final TextEditingController paybillController;
  final TextEditingController accountController;
  final TextEditingController dateController;
  final TextEditingController timeController;
  const PayBill(
      {super.key,
      required this.paybillController,
      required this.accountController,
      required this.dateController,
      required this.timeController});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5.0),
            child: Text(
              "Mpesa PayBill",
              style: context.titleText?.copyWith(fontWeight: FontWeight.bold, fontSize: 15),
            ),
          ),
          CustomTextField(
            labelText: "PayBill number",
            controller: paybillController,
            hintText: "paybill",
            showNoKeyboard: true,
            isRequired: true,
            validator: (value) {
              RegExp regex = RegExp(r'[a-zA-Z]');
              if (value!.isEmpty) {
                return "Enter Paybill Number";
              } else if (regex.hasMatch(value)) {
                return "Paybill number can not contain Alphabets";
              } else {
                return null;
              }
            },
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5.0),
            child: Text(
              "Account number",
              style: context.titleText?.copyWith(fontWeight: FontWeight.bold, fontSize: 15),
            ),
          ),
          CustomTextField(
            labelText: "Account number",
            controller: accountController,
            isRequired: true,
            hintText: "account number",
            validator: (value) {
              if (value!.isEmpty) {
                return "Enter Account Number";
              } else {
                return null;
              }
            },
          ),
          SingleLineRow(text: "Expected contribution end date", popup: KtStrings.endDateInfo,),
          DatePicker(date: dateController, time: timeController, isAllow: true,)
        ],
      ),
    );
  }
}
