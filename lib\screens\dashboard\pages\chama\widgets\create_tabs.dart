import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/utils/size_config.dart';

import '../../../../../utils/utils_exports.dart';

class CreateTabs extends StatelessWidget {
  const CreateTabs({super.key});

  @override
  Widget build(BuildContext context) {
    return InteractiveViewer(
      minScale: 0.5,
      maxScale: 4,
      child: AlertDialog(
        content: SizedBox(
          height: SizeConfig.screenHeight * .25 + 100,
          width: SizeConfig.screenWidth * .8,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(height: 8.h),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 2.w),
                child: Wrap(
                  runSpacing: 10.h,
                  spacing: 18.w,
                  children: [
                    BuildCard(
                      edit: AssetUrl.createKitty,
                      resources: "Contribution Kitty",
                      onTap: () {
                        Navigator.pushNamed(
                            context, NavRoutes.createkittyScreen);
                      },
                    ),
                    BuildCard(
                      edit: AssetUrl.createChama,
                      resources: "Create Chama",
                      onTap: () {
                        Navigator.pushNamed(context, NavRoutes.chamaStepper);
                      },
                    ),
                    BuildCard(
                      edit: AssetUrl.createChama,
                      resources: "Create Event",
                      onTap: () {
                        Navigator.pushNamed(context, NavRoutes.createEvent);
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: <Widget>[
          TextButton(
            child: const Text('Close'),
            onPressed: () {
              Navigator.of(context).pop();
            },
          ),
        ],
      ),
    );
  }
}

class BuildCard extends StatelessWidget {
  final String edit;
  final String resources;
  final VoidCallback onTap;
  final Color? color;
  const BuildCard(
      {super.key,
      required this.edit,
      required this.resources,
      required this.onTap,
      this.color});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 2, vertical: 3),
        padding: EdgeInsets.symmetric(
          horizontal: 15.w,
          vertical: 4.h,
        ),
        decoration: AppDecoration.outlineIndigo.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (color != null)
              SvgPicture.asset(
                edit,
                height: 30.h,
                width: 30.w,
                colorFilter: const ColorFilter.mode(
                    AppColors.blueButtonColor, BlendMode.srcIn),
              )
            else
              SvgPicture.asset(
                edit,
                height: 30.h,
                width: 30.w,
              ),
            SizedBox(height: 2.h),
            Text(
              resources,
              style: theme.textTheme.labelLarge!.copyWith(
                color: appTheme.indigo500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
