class CreateKittyResponse {
  Data? data;
  String? message;
  SocialMesages? socialMesages;
  bool? status;
  dynamic whatsappData;
  String? whatsappMessage;
  bool? whatsappStatus;

  CreateKittyResponse(
      {this.data,
      this.message,
      this.socialMesages,
      this.status,
      this.whatsappData,
      this.whatsappMessage,
      this.whatsappStatus});

  CreateKittyResponse.fromJson(Map<String, dynamic> json) {
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
    message = json['message'];
    socialMesages = json['social_mesages'] != null
        ? SocialMesages.fromJson(json['social_mesages'])
        : null;
    status = json['status'];
    whatsappData = json['whatsapp_data'];
    whatsappMessage = json['whatsapp_message'];
    whatsappStatus = json['whatsapp_status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    data['message'] = message;
    if (socialMesages != null) {
      data['social_mesages'] = socialMesages!.toJson();
    }
    data['status'] = status;
    data['whatsapp_data'] = whatsappData;
    data['whatsapp_message'] = whatsappMessage;
    data['whatsapp_status'] = whatsappStatus;
    return data;
  }
}

class Data {
  Kitty? kitty;
  Merchant? merchant;
  String? url;

  Data({this.kitty, this.merchant, this.url});

  Data.fromJson(Map<String, dynamic> json) {
    kitty = json['kitty'] != null ? Kitty.fromJson(json['kitty']) : null;
    merchant =
        json['merchant'] != null ? Merchant.fromJson(json['merchant']) : null;
    url = json['url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (kitty != null) {
      data['kitty'] = kitty!.toJson();
    }
    if (merchant != null) {
      data['merchant'] = merchant!.toJson();
    }
    data['url'] = url;
    return data;
  }
}

class Kitty {
  int? iD;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? title;
  String? description;
  String? beneficiaryAccount;
  String? beneficiaryChannel;
  String? beneficiaryPhoneNumber;
  DateTime? endDate;
  double? balance;
  int? limit;
  int? refererMerchantCode;
  String? phoneNumber;
  int? settlementType;

  Kitty(
      {this.iD,
      this.createdAt,
      this.updatedAt,
      this.deletedAt,
      this.title,
      this.description,
      this.beneficiaryAccount,
      this.beneficiaryChannel,
      this.beneficiaryPhoneNumber,
      this.endDate,
      this.balance,
      this.limit,
      this.refererMerchantCode,
      this.phoneNumber,
      this.settlementType});

  Kitty.fromJson(Map<String, dynamic> json) {
    iD = json['ID'];
    createdAt = json['CreatedAt'];
    updatedAt = json['UpdatedAt'];
    deletedAt = json['DeletedAt'];
    title = json['title'];
    description = json['description'];
    beneficiaryAccount = json['beneficiary_account'];
    beneficiaryChannel = json['beneficiary_channel'];
    beneficiaryPhoneNumber = json['beneficiary_phone_number'];
    endDate =
        json["end_date"] == null ? null : DateTime.parse(json["end_date"]);
    balance = double.tryParse(json['balance'].toString());
    limit = json['limit'];
    refererMerchantCode = json['referer_merchant_code'];
    phoneNumber = json['phone_number'];
    settlementType = json['settlement_type'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['ID'] = iD;
    data['CreatedAt'] = createdAt;
    data['UpdatedAt'] = updatedAt;
    data['DeletedAt'] = deletedAt;
    data['title'] = title;
    data['description'] = description;
    data['beneficiary_account'] = beneficiaryAccount;
    data['beneficiary_channel'] = beneficiaryChannel;
    data['beneficiary_phone_number'] = beneficiaryPhoneNumber;
    data['end_date'] = endDate;
    data['balance'] = balance;
    data['limit'] = limit;
    data['referer_merchant_code'] = refererMerchantCode;
    data['phone_number'] = phoneNumber;
    data['settlement_type'] = settlementType;
    return data;
  }
}

class Merchant {
  int? iD;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  int? userID;
  String? merchantName;
  int? merchantCode;
  double? merchantPercent;

  Merchant(
      {this.iD,
      this.createdAt,
      this.updatedAt,
      this.deletedAt,
      this.userID,
      this.merchantName,
      this.merchantCode,
      this.merchantPercent});

  Merchant.fromJson(Map<String, dynamic> json) {
    iD = json['ID'];
    createdAt = json['CreatedAt'];
    updatedAt = json['UpdatedAt'];
    deletedAt = json['DeletedAt'];
    userID = json['UserID'];
    merchantName = json['merchant_name'];
    merchantCode = json['merchant_code'];
    merchantPercent = json['merchant_percent'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['ID'] = iD;
    data['CreatedAt'] = createdAt;
    data['UpdatedAt'] = updatedAt;
    data['DeletedAt'] = deletedAt;
    data['UserID'] = userID;
    data['merchant_name'] = merchantName;
    data['merchant_code'] = merchantCode;
    data['merchant_percent'] = merchantPercent;
    return data;
  }
}

class SocialMesages {
  int? iD;
  String? createdAt;
  String? updatedAt;
  String? deletedAt;
  String? facebook;
  String? tiktok;
  String? instagram;
  String? youtube;
  String? twitter;

  SocialMesages(
      {this.iD,
      this.createdAt,
      this.updatedAt,
      this.deletedAt,
      this.facebook,
      this.tiktok,
      this.instagram,
      this.youtube,
      this.twitter});

  SocialMesages.fromJson(Map<String, dynamic> json) {
    iD = json['ID'];
    createdAt = json['CreatedAt'];
    updatedAt = json['UpdatedAt'];
    deletedAt = json['DeletedAt'];
    facebook = json['facebook'];
    tiktok = json['tiktok'];
    instagram = json['instagram'];
    youtube = json['youtube'];
    twitter = json['twitter'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['ID'] = iD;
    data['CreatedAt'] = createdAt;
    data['UpdatedAt'] = updatedAt;
    data['DeletedAt'] = deletedAt;
    data['facebook'] = facebook;
    data['tiktok'] = tiktok;
    data['instagram'] = instagram;
    data['youtube'] = youtube;
    data['twitter'] = twitter;
    return data;
  }
}
