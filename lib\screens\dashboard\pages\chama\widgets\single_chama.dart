import 'package:date_time_format/date_time_format.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/models/chama/chama_model.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';

import '../../../../../../utils/utils_exports.dart';

// ignore: must_be_immutable
class SingleChamaWidget extends StatefulWidget {
  final UserChama chama;
  final Chama chamaDts;
  const SingleChamaWidget(
      {super.key, required this.chama, required this.chamaDts});

  @override
  State<SingleChamaWidget> createState() => _SingleChamaWidgetState();
}

class _SingleChamaWidgetState extends State<SingleChamaWidget> {
  List<String> circleImages = [
    AssetUrl.group6,
    AssetUrl.winter,
    AssetUrl.imgEllipse1,
  ];
  final ChamaController chamaController = Get.put(ChamaController());
  viewChamaDetails() {
    var chamaDataController = Get.put(ChamaDataController());
    chamaDataController.chama.value = widget.chama;
    chamaDataController.singleChamaDts.value = widget.chamaDts;

    Get.toNamed(NavRoutes.viewingSingleChama);
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: viewChamaDetails,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: 7.w,
          vertical: 14.h,
        ),
        decoration: AppDecoration.shadow1.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder6,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: EdgeInsets.only(right: 4.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.symmetric(vertical: 7.h),
                      child: Text(
                        "${widget.chama.chama?.title}",
                        overflow: TextOverflow.ellipsis,
                        style: CustomTextStyles.labelMediumff545963,
                      ),
                    ),
                  ),
                  Text(
                    FormattedCurrency()
                        .getFormattedCurrency(widget.chama.chama?.totaBal),
                    style: TextStyle(
                      fontSize: 20.h,
                      fontWeight: FontWeight.w900,
                      color: appTheme.gray900,
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.only(right: 4.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 4.h,
                    ),
                    decoration: AppDecoration.outlineIndigo.copyWith(
                      borderRadius: BorderRadiusStyle.circleBorder16,
                    ),
                    child: Text(
                      "${widget.chama.member?.role}",
                      style: CustomTextStyles.titleSmallGreen800,
                    ),
                  ),
                  const Spacer(),
                  Padding(
                    padding: EdgeInsets.only(bottom: 3.h),
                    child: Text(
                      "Chama Balance",
                      style: CustomTextStyles.bodySmallGray900,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 10.h),
            Padding(
              padding: EdgeInsets.only(right: 4.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(left: 8.w),
                        child: Row(
                          children: [
                            Row(
                              //mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                for (int i = 0; i < circleImages.length; i++)
                                  Align(
                                    widthFactor: 0.5,
                                    child: CircleAvatar(
                                      radius: 23,
                                      backgroundColor: Colors.white,
                                      child: CircleAvatar(
                                        radius: 20,
                                        backgroundImage:
                                            AssetImage(circleImages[i]),
                                      ),
                                    ),
                                  )
                              ],
                            ),
                            SizedBox(
                              width: 10.w,
                            ),
                            Text("${widget.chama.membersCount} Members")
                          ],
                        ),
                      ),
                      SizedBox(height: 8.h),
                      Row(
                        children: [
                          CustomImageView(
                              imagePath: AssetUrl.money,
                              height: 24.h,
                              width: 24.w),
                          SizedBox(
                            width: 10.w,
                          ),
                          Text(
                              "${FormattedCurrency().getFormattedCurrency("${widget.chama.chama?.amount ?? 0}")}/${widget.chama.chama?.frequency}")
                        ],
                      ),
                      SizedBox(height: 4.h),
                      Row(
                        children: [
                          CustomImageView(
                            imagePath: AssetUrl.imgClock,
                            height: 24.h,
                            width: 24.w,
                          ),
                          Padding(
                              padding: EdgeInsets.only(
                                left: 4.w,
                                top: 4.h,
                              ),
                              child: RichText(
                                text: TextSpan(
                                  children: [
                                    TextSpan(
                                      text: DateTimeFormat.relative(
                                        widget.chama.chama!.nextOccurrence ??
                                            DateTime.now(),
                                        levelOfPrecision: 1,
                                        prependIfBefore: 'Next cycle in',
                                        ifNow: "Now",
                                      ),
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        color: appTheme.gray90001,
                                      ),
                                    ),
                                  ],
                                ),
                              )),
                        ],
                      ),
                    ],
                  ),
                  _buildViewButton1(context),
                ],
              ),
            ),
            SizedBox(height: 3.h),
          ],
        ),
      ),
    );
  }

  /// Section Widget
  Widget _buildViewButton1(BuildContext context) {
    return CustomOutlinedButton(
      onPressed: viewChamaDetails,
      width: 68.w,
      height: 20.h,
      text: "View",
      margin: EdgeInsets.fromLTRB(1.w, 1.h, 1.w, 1.h),
    );
  }
}
