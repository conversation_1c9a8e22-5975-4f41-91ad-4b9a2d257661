import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:flutter_quill/quill_delta.dart';
import 'package:flutter_quill_delta_from_html/flutter_quill_delta_from_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:get_storage_pro/get_storage_pro.dart';
import 'package:intl/intl.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/controllers/events/edit_event_controller.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/controllers/user_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/context_extension.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/models/kitty_payload.dart';
import 'package:onekitty/models/user_kitties_model.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/kitty_settings.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/widgets/row_widget.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/common_strings.dart';
import 'package:onekitty/utils/formatted_currency.dart';
import 'package:onekitty/utils/iswysiwyg.dart'; 
import 'package:onekitty/utils/my_text_field.dart';
import '../../../../../utils/show_cached_network_image.dart';

class EditKittyDetails extends StatefulWidget {
  final UserKitty? kitty;

  const EditKittyDetails({
    this.kitty,
    super.key,
  });

  @override
  State<EditKittyDetails> createState() => _EditKittyDetailsState();
}

class _EditKittyDetailsState extends State<EditKittyDetails>
    with TickerProviderStateMixin {
  final formKey = GlobalKey<FormState>();
  //KittyController singleKitty = Get.put(KittyController());
  ContributeController singleKitty = Get.put(ContributeController());
  DataController dataController = Get.put(DataController());
  TextEditingController nameController = TextEditingController();
  TextEditingController beneficiaryController = TextEditingController();
  TextEditingController descriptionController = TextEditingController();
  TextEditingController accountController = TextEditingController();
  TextEditingController accountrefController = TextEditingController();
  final endDateController = TextEditingController();
  // late TabController tabController;
  KittyController kittyController = Get.put(KittyController());
  String? selectedChannel = "M-Pesa";
  String? phoneNum;
  bool isLoading = false;
  int? kittyId;
  DateTime? endDate;
  var params = Get.parameters;
  List tillPaybil = ["Mpesa Paybill", "Buy Goods(Till)"];
  PhoneNumber num = PhoneNumber(isoCode: 'KE');
  String myPhone = "";
  String selectedtillPaybil = "Mpesa Paybill";
  final box = GetStorage();
  //UserController userController = Get.find();
  final user = UserController().getLocalUser();
  final ContributeController contributeController =
      Get.find<ContributeController>();
  final eventController = Get.find<EditEventController>();
  final media = Get.find<ContributeController>().kittyMedia;
   
  late QuillController kittyDesc;
   

  final showSettings = false.obs;

  setValues() async {
    // final user = userController.getLocalUser();
    final kittyCreated = dataController.kitty.value.kitty;
    String? title = dataController.kitty.value.kitty?.title ?? "";
    kittyId = dataController.kitty.value.kitty?.id;
    DateTime? endDateGot = dataController.kitty.value.kitty?.endDate;

    endDateController.text =
        DateFormat('d MMM yyyy HH : mm a').format(endDateGot ?? DateTime.now());

    String? benef = dataController.kitty.value.kitty?.beneficiaryAccount;

    nameController.text = title;
    /*
    phoneNum = singleKitty.userPhoneNumber;
    beneficiaryController.text = benef!.substring(3);

    accountController.text =
        dataController.kitty.value.kitty?.beneficiaryAccount ?? "";

    String? accountRef = dataController.kitty.value.kitty?.bennefAccRef ?? '';
    accountrefController.text = accountRef;

    // accountrefController.text = ;
    if (dataController.kitty.value.kitty?.settlementType != null) {
      if (dataController.kitty.value.kitty?.settlementType == 1) {
        tabController.index = 2;
      } else if (dataController.kitty.value.kitty?.settlementType == 2) {
        tabController.index = 1;
      } else {
        tabController.index = 0;
      }
    }*/
    endDate = endDateGot;
    String description = dataController.kitty.value.kitty?.description ?? "";
    Document document;
    if (isWysiwygFormat(description)) {
      document = Document.fromJson(HtmlToDelta().convert(description).toJson());
    } else if (description.startsWith('[') && description.endsWith(']')) {
      // JSON format
      document = Document.fromJson(jsonDecode(description));
    } else {
      // Plain text
      document = Document.fromDelta(Delta()..insert(description)..insert('\n'));
    }
    var selection = const TextSelection.collapsed(offset: 0);
    kittyDesc = QuillController(
        document: document, selection: selection);
    String? desc =
        extractText(dataController.kitty.value.kitty?.description ?? "");
    descriptionController.text = desc;
    setState(() {});
  }

  late TabController pagetabController;

  @override
  void initState() {
    pagetabController = TabController(length: 2, vsync: this, initialIndex: 0);
    // tabController = TabController(length: 3, vsync: this, initialIndex: 0);
    // if (kDebugMode) {}
    pagetabController.addListener(() {
      showSettings.value = pagetabController.index == 1;
    });
    setValues();

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: const Text("Edit Kitty Details"),
          bottom: TabBar(
            controller: pagetabController,
            tabs: const [
              Tab(text: "Details"),
              Tab(text: "Settings"),
            ],
          ),
        ),
        body: TabBarView(
          controller: pagetabController,
          children: [
            Container(
              margin: const EdgeInsets.all(20),
              child: Form(
                key: formKey,
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Text(
                      //   "Edit Kitty Details",
                      //   style: context.titleText,
                      // ),
                      SingleLineRow(
                        text: "Kitty name",
                        popup: KtStrings.kittyName,
                      ),
                      CustomTextField(
                        isRequired: true,
                        labelText: "Enter Kitty name",
                        hintText: "Update kitty name",
                        controller: nameController,
                        validator: (value) {
                          if (value!.isEmpty) {
                            return "Enter kitty title";
                          } else if (value.length < 5) {
                            return "Kitty Name must be between 5 and 300";
                          } else {
                            return null;
                          }
                        },
                      ),
                      SingleLineRow(
                        text: "Kitty Description",
                        popup: KtStrings.kittyDescription,
                      ),
                   
            Padding(
              padding: const EdgeInsets.all(4.0),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.0),
                  border: Border.all(color: Colors.grey.shade400),
                ),
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 8),
                    QuillToolbar.simple(
                      configurations: QuillSimpleToolbarConfigurations(
                        multiRowsDisplay: false,
                        sharedConfigurations: const QuillSharedConfigurations(
                          locale: Locale('en'),
                        ),
                        controller: kittyDesc,
                      ),
                    ),
                    const SizedBox(height: 15),
                   QuillEditor.basic(
                      configurations: QuillEditorConfigurations(
                        placeholder: "e.g Time to award our best artist",
                        controller: kittyDesc,
                        // readOnly: false,
                        autoFocus: false,
                        enableInteractiveSelection:
                            true, // Enable interactive selection to allow text editing

                        sharedConfigurations: const QuillSharedConfigurations(
                          locale: Locale('en'),
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                  ],
                ),
              ),
            ),
          
                      /*      CustomTextField(
                        isRequired: true,
                        labelText: "Enter kitty description",
                        hintText: "Update Kitty description",
                        controller: descriptionController,
                        validator: (value) {
                          if (value!.isEmpty) {
                            return "Enter kitty description";
                          } else {
                            return null;
                          }
                        },
                      ), */
                      /*
                      SingleLineRow(
                        text: "Beneficiary payment channel",
                        popup: KtStrings.benfChannel,
                      ),
                      DefaultTabController(
                          length: 3,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              buildTabs(tabController, context),
                              SizedBox(
                                height: 208.h,
                                child: Column(
                                  children: [
                                    Expanded(
                                      child: TabBarView(
                                        controller: tabController,
                                        children: [
                                          Mobile2(
                                            internationalPhoneNumberInput:
                                                InternationalPhoneNumberInput(
                                              onInputChanged: (num) {
                                                setState(() {
                                                  myPhone = num.phoneNumber!;
                                                });
                                              },
                                              onInputValidated: (bool value) {},
                                              selectorConfig:
                                                  const SelectorConfig(
                                                selectorType:
                                                    PhoneInputSelectorType
                                                        .BOTTOM_SHEET,
                                                useBottomSheetSafeArea: true,
                                              ),
                                              ignoreBlank: false,
                                              autoValidateMode:
                                                  AutovalidateMode.disabled,
                                              selectorTextStyle: const TextStyle(
                                                  color: Colors.black),
                                              initialValue: num,
                                              textFieldController:
                                                  beneficiaryController,
                                              formatInput: true,
                                              keyboardType: const TextInputType
                                                  .numberWithOptions(
                                                  signed: true, decimal: true),
                                              inputBorder:
                                                  const OutlineInputBorder(),
                                              onSaved: (PhoneNumber number) {},
                                            ),
                                            paymentChannelsBuilder2:
                                                PaymentChannelsBuilder2(
                                                    selectedChannel:
                                                        selectedChannel ?? "",
                                                    onChange: (String? value) {
                                                      setState(() {
                                                        selectedChannel = value;
                                                      });
                                                    }),
                                          ),
                                          PayBill2(
                                              paybillController:
                                                  accountController,
                                              accountController:
                                                  accountrefController),
                                          TillPage2(
                                              tillController: accountController),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          )), */
                      const SingleLineRow(
                        text: "Kitty Media",
                        popup:
                            'The media will be displayed on the kitty profile',
                      ),
                      Align(
                        alignment: Alignment.centerLeft,
                        child: SizedBox(
                            height: 80.h,
                            child: Obx(
                              () => ListView.builder(
                                  itemCount: media.length + 1,
                                  scrollDirection: Axis.horizontal,
                                  shrinkWrap: true,
                                  itemBuilder: (context, index) {
                                    if (index == media.length) {
                                      return InkWell(
                                        onTap: () async {
                                          if (kittyController
                                              .isUploadingImage.value) {
                                            return;
                                          }
                                          kittyController.pickImage(
                                              context: context,
                                              kittyId: dataController
                                                      .kitty.value.kitty?.id ??
                                                  0,
                                              name: dataController.kitty.value
                                                      .kitty?.title ??
                                                  '');
                                        },
                                        child: Obx(
                                          () => Container(
                                              padding:
                                                  const EdgeInsets.all(8.0),
                                              height: 70.h,
                                              width: 70.w,
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(4.5),
                                                border: Border.all(
                                                    color: Colors.grey),
                                              ),
                                              child: kittyController
                                                      .isUploadingImage.value
                                                  ? const SizedBox(
                                                      height: 30,
                                                      width: 30,
                                                      child:
                                                          CircularProgressIndicator
                                                              .adaptive(),
                                                    )
                                                  : const Icon(Icons
                                                      .add_a_photo_outlined)),
                                        ),
                                      );
                                    }
                                    return Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Stack(children: [
                                        ShowCachedNetworkImage(
                                            height: 70.h,
                                            width: 70.w,
                                            errorWidget:
                                                const Icon(Icons.warning),
                                            imageurl: media[index].url ?? ''),
                                        IconButton(
                                            onPressed: () {
                                              showDialog(
                                                context: context,
                                                builder:
                                                    (BuildContext context) {
                                                  return AlertDialog(
                                                    title: const Text(
                                                        'Confirm Deletion'),
                                                    content: const Text(
                                                        'Are you sure you want to delete this Photo?'),
                                                    actions: <Widget>[
                                                      TextButton(
                                                        onPressed: () {
                                                          Navigator.of(context)
                                                              .pop(); // Close the dialog
                                                        },
                                                        child: const Text(
                                                            'Cancel'),
                                                      ),
                                                      TextButton(
                                                        onPressed: () {
                                                          kittyController
                                                              .deleteMedia(
                                                                  contributeController
                                                                          .kittyMedia[
                                                                              index]
                                                                          .id ??
                                                                      0,
                                                                  pos: index)
                                                              .whenComplete(() =>
                                                                  Navigator.of(
                                                                          context)
                                                                      .pop());
                                                        },
                                                        child: const Text(
                                                            'Delete',
                                                            style: TextStyle(
                                                                color: Colors
                                                                    .red)),
                                                      ),
                                                    ],
                                                  );
                                                },
                                              );
                                            },
                                            icon: Icon(Icons.delete_outlined,
                                                color: Colors.grey.shade400))
                                      ]),
                                    );
                                  }),
                            )),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: MyTextFieldwValidator(
                          title: 'End Date',
                          controller: endDateController,
                          hint: 'click to select end Date',
                          readOnly: true,
                          onTap: () async {
                            DateTime? pickedDateTime = await showDatePicker(
                              context: context,
                              initialDate: DateTime.now(),
                              firstDate: DateTime.now(),
                              lastDate:
                                  DateTime.now().add(const Duration(days: 365)),
                            );

                            if (pickedDateTime != null) {
                              TimeOfDay? pickedTime = await showTimePicker(
                                context: context,
                                initialTime: TimeOfDay.now(),
                              );

                              if (pickedTime != null) {
                                DateTime finalDateTime = DateTime(
                                  pickedDateTime.year,
                                  pickedDateTime.month,
                                  pickedDateTime.day,
                                  pickedTime.hour,
                                  pickedTime.minute,
                                );

                                String formattedDateTime =
                                    DateFormat('d MMM yyyy HH : mm a')
                                        .format(finalDateTime);
                                endDateController.text = formattedDateTime;
                              }
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            KittySettingsScreen(kittyId: kittyId ?? 0)
          ],
        ),
        floatingActionButton: Obx(
          () => showSettings.value == true
              ? const SizedBox()
              : FloatingActionButton.extended(
                  backgroundColor: AppColors.primary,
                  onPressed: () async {
                    setState(() {
                      isLoading = true;
                    });
                    await updateKitty();
                    if (mounted) {
                      setState(() {
                        isLoading = false;
                      });
                    }
                  },
                  label: isLoading
                      ? const SpinKitDualRing(
                          color: Colors.white,
                          size: 30.0,
                        )
                      : const Text("Update Details")),
        ));
  }

  updateKitty() async {
    final kittyCreated = dataController.kitty.value.kitty;
    if (formKey.currentState!.validate()) {
      context.hideKeyboard();
      /*
      int? referCode;
      int settlementType = 0;
      String beneficiaryChannel = '63902';
      String account = '';
      String accountref = '';
      String beneficiaryPhoneNumber = myPhone;
      String? userID = kittyController.user.value.iD?.toString();

      if (tabController.index == 1) {
        //paybill
        settlementType = 2;
        if (accountController.text.trim().isEmpty ||
            accountrefController.text.trim().isEmpty) {
          ToastUtils.showErrorToast(
              context, "Enter all required fields for Paybill", "Error");
          return;
        } else {
          account = accountController.text.trim();
          accountref = accountrefController.text.trim();
          beneficiaryPhoneNumber = user!.phoneNumber ?? "";
          beneficiaryChannel = beneficiaryChannel;
        }
      } else if (tabController.index == 2) {
        //till
        settlementType = 1;
        if (accountController.text.trim().isEmpty) {
          ToastUtils.showErrorToast(context, "Enter Till number", "Error");
          return;
        } else {
          account = accountController.text.trim();
          beneficiaryPhoneNumber = user!.phoneNumber ?? "";
          beneficiaryChannel = beneficiaryChannel;
        }
      } else {
        settlementType = 0;
        beneficiaryPhoneNumber = myPhone.substring(1);
        account = accountController.text.trim();
        account = myPhone.substring(1);
      }*/
      CreateKitPayload payload = CreateKitPayload(
          id: kittyId,
          title: nameController.text.trim(),
          description: quilltoHtml(kittyDesc), //descriptionController.text.trim(),
          beneficiaryChannel: kittyController
              .getNetworkCode(networkTitle: selectedChannel ?? "")
              .toString(),
          // null, //"0",
          beneficiaryAccount:
              dataController.kitty.value.kitty?.beneficiaryAccount ??
                  "", //null, //'************',
          //  account, // null,
          beneficiaryAccountRef:
              kittyCreated?.bennefAccRef ?? "", // accountref, //null,
          refererMerchantCode: kittyCreated?.refererMerchantCode,
          beneficiaryPhoneNumber:
              //null, //'************', //
              singleKitty.userPhoneNumber, //beneficiaryPhoneNumber,
          settlementType: kittyCreated?.settlementType,
          phoneNumber: kittyCreated?.phoneNumber ?? "",
          endDate: DateFormat('d MMM yyyy HH : mm a')
              .parse(endDateController.text)
              .toUtc());

      bool resp = await kittyController.updateKitty(request: payload);
      if (resp) {
        if (!mounted) {
          return;
        }
        var res = await contributeController.getKitty(id: kittyId);
        if (res) {
          var dataController = Get.put(DataController());
          dataController.kitty.value = singleKitty.singleKitty.value;
        }
        ToastUtils.showSuccessToast(
            context, kittyController.apiMessage.string, "Success");
        Get.find<UserKittyController>().getUserkitties();
        Get.offNamed(NavRoutes.viewingsinglekittyScreen);
        // Get.back();
      } else {
        if (!mounted) {
          return;
        }
        ToastUtils.showToast(kittyController.apiMessage.string,
            toastType: ToastType.error);
      }
      // Get.to(() => const ViewKitty());
    } else {
      ToastUtils.showErrorToast(context, "Error", "Check the values");
    }
  }
}
