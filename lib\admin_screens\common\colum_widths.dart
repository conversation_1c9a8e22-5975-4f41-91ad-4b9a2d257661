
import 'package:flutter/foundation.dart';

class ColumnWidths {
  static final ValueNotifier<double> idWidth = ValueNotifier(60.0);
  static final ValueNotifier<double> createdAtWidth = ValueNotifier(170.0);
  static final ValueNotifier<double> titleWidth = ValueNotifier(200.0);
  static final ValueNotifier<double> usernameWidth = ValueNotifier(120.0);
  static final ValueNotifier<double> kittyIdWidth = ValueNotifier(100.0);
  static final ValueNotifier<double> balanceWidth = ValueNotifier(120.0);
  
  static final ValueNotifier<double> emailWidth = ValueNotifier(200.0);
  static final ValueNotifier<double> phoneWidth = ValueNotifier(200.0);
  static final ValueNotifier<double> amountWidth = ValueNotifier(120.0);
  static final ValueNotifier<double> statusWidth = ValueNotifier(100.0);
  static final ValueNotifier<double> actionsWidth = ValueNotifier(100.0);
  static final ValueNotifier<double> venueWidth = ValueNotifier(100.0);
  static final ValueNotifier<double> startDateWidth = ValueNotifier(100.0);
  static final ValueNotifier<double> endDateWidth = ValueNotifier(100.0);
}

