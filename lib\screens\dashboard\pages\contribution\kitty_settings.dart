// settings_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/kitty_settings.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/screens/onboarding/passwd_req_screen.dart';
import 'package:onekitty/utils/my_button.dart';

class KittySettingsScreen extends StatelessWidget {
  final int kittyId;
  const KittySettingsScreen({super.key, required this.kittyId});

  @override
  Widget build(BuildContext context) {
    final isActivating = false.obs;
    final controller = Get.put(SettingsController());
    final maxAmountController = TextEditingController(
            text: (controller.settings.value.maximumAmount?.toString() ?? "")),
        minAmountController = TextEditingController(
            text: (controller.settings.value.minimumAmount?.toString() ?? "")),
        messageController = TextEditingController(
            text: controller.settings.value.customMessage),
        historyLimitController = TextEditingController(
            text: controller.settings.value.historyLimit?.toString() ?? '');
    // tarrifWithdrawPercentageController = TextEditingController(
    //     text: (controller.settings.value.tarrifWithdrawPercentage * 100)
    //         .toString());
    final historyStartDate = controller.settings.value.startDate != null
        ? DateFormat('dd/MM/yyyy HH : mm a')
            .format(controller.settings.value.startDate!)
            .obs
        : ''.obs;
    final hideNames = (controller.settings.value.hideNames ?? false).obs;
    final hideAmount = (controller.settings.value.hideAmount ?? false).obs;
    final groupBy = controller.settings.value.groupBy.obs;
    final showAdvancedSettings = false.obs;

    return Scaffold(
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(8.0),
        child: SizedBox(
          width: double.infinity,
          child: Obx(
            () => MyButton(
              showLoading: controller.isSubmitting.value,
              onClick: () async {
                final res = await controller.submitSettings({
                  'ID': controller.settings.value.id ?? kittyId,
                  'kitty_id': controller.settings.value.kittyId ?? kittyId,
                  'minimum_amount':
                      num.tryParse(minAmountController.text), // use null
                  'maximum_amount':
                      num.tryParse(maxAmountController.text), // should be null
                  'hide_amount': hideAmount.value,
                  'hide_names': hideNames.value,
                  'history_limit': num.tryParse(historyLimitController
                      .text), // should be null if not present
                  'group_by': groupBy.value,
                  // 'beneficiary_split_config': splitConfig.value.toUpperCase(),
                  'start_date': historyStartDate.value != ''
                      ? DateFormat('dd/MM/yyyy HH : mm a')
                          .parse(historyStartDate.value)
                          .toUtc()
                          .toIso8601String()
                      : null,
                  'custom_message': messageController.text,
                  'signatory_threshold': null
                });
                if (res) {
                  Navigator.pop(context);
                }
              },
              label: ('Submit'),
            ),
          ),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextField(
              controller: minAmountController,
              decoration: const InputDecoration(
                  labelText: 'Minimum Amount',
                  border: OutlineInputBorder(),
                  suffixIcon: Tooltip(
                    showDuration: Duration(seconds: 3),
                    triggerMode: TooltipTriggerMode.tap,
                    message: "Minimum amount allowed for the contribution",
                    child: Icon(Icons.help),
                  )),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: maxAmountController,
              decoration: const InputDecoration(
                  labelText: 'Maximum Amount',
                  border: OutlineInputBorder(),
                  suffixIcon: Tooltip(
                    showDuration: Duration(seconds: 3),
                    triggerMode: TooltipTriggerMode.tap,
                    message: "Maximum amount allowed for the contribution",
                    child: Icon(Icons.help),
                  )),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: messageController,
              maxLines: null,
              minLines: 3,
              decoration: const InputDecoration(
                labelText: 'Custom Thank You Message',
                border: OutlineInputBorder(),
                alignLabelWithHint: true,
                suffixIcon: Tooltip(
                  showDuration: Duration(seconds: 3),
                  triggerMode: TooltipTriggerMode.tap,
                  message:
                      "A personalized message displayed to users as a token of appreciation whenever they make a contribution. This feature ensures every contributor feels valued and recognized.",
                  child: Icon(Icons.help),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Obx(
              () => Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('Hide Amount'),
                    Switch(
                      value: hideAmount.value,
                      onChanged: (value) => hideAmount.value = value,
                    )
                  ]),
            ),
            Obx(
              () => Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('Hide Names'),
                    Switch(
                      value: hideNames.value,
                      onChanged: (value) => hideNames.value = value,
                    )
                  ]),
            ),
            Obx(
              () => Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('Group by Account? '),
                    Switch(
                      value: groupBy.value == "account",
                      onChanged: (value) =>
                          groupBy.value = value ? "account" : "",
                    )
                  ]),
            ),
            const SizedBox(height: 16),
            InkWell(
              onTap: () async {
                DateTime? pickedDateTime = await showDatePicker(
                  context: context,
                  initialDate: DateTime.now(),
                  firstDate: DateTime(2000),
                  lastDate: DateTime.now(),
                );

                if (pickedDateTime != null) {
                  TimeOfDay? pickedTime = await showTimePicker(
                    context: context,
                    initialTime: TimeOfDay.now(),
                  );

                  if (pickedTime != null) {
                    DateTime finalDateTime = DateTime(
                      pickedDateTime.year,
                      pickedDateTime.month,
                      pickedDateTime.day,
                      pickedTime.hour,
                      pickedTime.minute,
                    );

                    String formattedDateTime =
                        DateFormat('dd/MM/yyyy HH : mm a')
                            .format(finalDateTime);

                    historyStartDate.value = formattedDateTime;
                  }
                }
              },
              child: Obx(
                () => InputDecorator(
                  decoration: const InputDecoration(
                      labelText: 'History Start Date',
                      border: OutlineInputBorder(),
                      suffixIcon: Icon(Icons.calendar_month)),
                  child: Text(historyStartDate.value),
                ),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: historyLimitController,
              keyboardType: const TextInputType.numberWithOptions(),
              decoration: InputDecoration(
                labelText: 'History Limit',
                border: const OutlineInputBorder(),
                suffix: SizedBox(
                  width: 60,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      GestureDetector(
                        onTap: () {
                          int val =
                              int.tryParse(historyLimitController.text) ?? 0;
                          historyLimitController.text = (val + 1).toString();
                        },
                        child: const Icon(
                          Icons.add,
                          size: 18,
                        ),
                      ),
                      const Expanded(child: VerticalDivider()),
                      GestureDetector(
                        onTap: () {
                          int val =
                              int.tryParse(historyLimitController.text) ?? 0;
                          if (val > 1) {
                            historyLimitController.text = (val - 1).toString();
                          }
                        },
                        child: const Icon(
                          Icons.remove,
                          size: 18,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            // const SizedBox(height: 16),
            // TextField(
            //   controller: tarrifWithdrawPercentageController,
            //   decoration: const InputDecoration(
            //     labelText: 'Tarrif Withdraw Percentage',
            //     helperText: 'This only accepts decimals',
            //     border: OutlineInputBorder(),
            //   ),
            //   keyboardType: TextInputType.number,
            // ),
            /* const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: splitConfig.value,
              hint: const Text('Select split config'),
              decoration: const InputDecoration(
                labelText: 'Beneficiary split config',
                border: OutlineInputBorder(),
              ),
              items: <String>['Percentage', 'Amount']
                  .map<DropdownMenuItem<String>>((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
              onChanged: (String? newValue) {
                if (newValue != null) {
                  splitConfig.value = newValue;
                }
              },
            ), */
            const SizedBox(height: 16),

            TextButton(
              onPressed: () {
                showAdvancedSettings(!showAdvancedSettings.value);
              },
              child: const Text('Advanced Settings'),
            ),
            const SizedBox(height: 16),
            Obx(() => showAdvancedSettings.value
                ? Column(children: [
                    Get.find<DataController>()
                                .kitty
                                .value
                                .kittyStatus
                                ?.toLowerCase() !=
                            'active'
                        ? Container(
                            decoration: BoxDecoration(
                              border:
                                  Border.all(color: Colors.green, width: 0.6),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: ListTile(
                                leading: Obx(
                                  () => Container(
                                      height: 60,
                                      width: 60,
                                      decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          color: Colors.green.shade50),
                                      child: isActivating.value
                                          ? const CircularProgressIndicator(
                                              color: Colors.green,
                                            )
                                          : const Icon(Icons.key,
                                              color: Colors.green)),
                                ),
                                title: const Text('Activate Kitty',
                                    style: TextStyle(
                                        color: Colors.green,
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold)),
                                subtitle: const Text('',
                                    style: TextStyle(
                                        color: Colors.green,
                                        fontSize: 12,
                                        fontStyle: FontStyle.italic)),
                                onTap: () async {
                                  isActivating(true);
                                  await controller.deactivateKitty({
                                    'kitty_id': kittyId,
                                    'status': 0
                                  }).whenComplete(() {
                                    isActivating.value = false;
                                  });
                                }))
                        : Container(
                            decoration: BoxDecoration(
                              border:
                                  Border.all(color: Colors.green, width: 0.6),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: ListTile(
                                leading: Container(
                                    height: 60,
                                    width: 60,
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(8),
                                        color: Colors.red.shade50),
                                    child: const Icon(Icons.block,
                                        color: Colors.red)),
                                title: const Text('Deactivate Kitty',
                                    style: TextStyle(
                                        color: Colors.red,
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold)),
                                subtitle: const Text(
                                    'You can reactivate this kitty at any time',
                                    style: TextStyle(
                                        color: Colors.red,
                                        fontSize: 12,
                                        fontStyle: FontStyle.italic)),
                                onTap: () {
                                  final isDeleting = false.obs;
                                  Get.dialog(AlertDialog(
                                    title: const Text('Delete Kitty'),
                                    content: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        const Text(
                                            'Are you sure you want to deactivate this kitty?'),
                                        const SizedBox(height: 30),
                                        Row(children: [
                                          MyButton(
                                            label: ('Cancel'),
                                            width: 80.w,
                                            outlined: true,
                                            onClick: () {
                                              Get.back();
                                            },
                                          ),
                                          SizedBox(width: 6.w),
                                          Obx(
                                            () => MyButton(
                                              width: 150.w,
                                              showLoading: isDeleting.value,
                                              color: Colors.red,
                                              onClick: () async {
                                                var isAuthenticated =
                                                    await Get.to(
                                                        () =>
                                                            const AuthPasswdScreen(),
                                                        arguments: [false]);
                                                if (isAuthenticated != null &&
                                                    isAuthenticated == true) {
                                                  isDeleting.value = true;
                                                  await controller
                                                      .deactivateKitty({
                                                    'kitty_id': kittyId,
                                                    'status': 1
                                                  }).whenComplete(() {
                                                    isDeleting.value = false;
                                                    Navigator.pop(context);
                                                    Navigator.pop(context);
                                                  });
                                                }
                                              },
                                              label: ('Deactivate'),
                                            ),
                                          )
                                        ])
                                      ],
                                    ),
                                  ));
                                }),
                          ),
                    const SizedBox(height: 200),
                  ])
                : const SizedBox(height: 200))
          ],
        ),
      ),
    );
  }
}
