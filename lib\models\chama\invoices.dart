import 'dart:convert';

class Invoice {
  final int id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final int chamaId;
  final int memberId;
  final num dueAmount;
  final num chamaBalance;
  final num paidAmount;
  final DateTime? dueDate;
  final dynamic occurenceCount;
  final String status;
  final DateTime? cycleStartDate;
  final DateTime? cycleEndDate;
  final String notes;

  Invoice({
    this.id = 0,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.chamaId = 0,
    this.memberId = 0,
    this.dueAmount = 0.0,
    this.chamaBalance = 0.0,
    this.paidAmount = 0.0,
    this.dueDate,
    this.occurenceCount,
    this.status = '',
    this.cycleStartDate,
    this.cycleEndDate,
    this.notes = '',
  });

  factory Invoice.fromRawJson(String str) => Invoice.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Invoice.fromJson(Map<String, dynamic> json) => Invoice(
        id: json["ID"] ?? 0,
        createdAt: json["CreatedAt"] != null
            ? DateTime.parse(json["CreatedAt"])
            : null,
        updatedAt: json["UpdatedAt"] != null
            ? DateTime.parse(json["UpdatedAt"])
            : null,
        deletedAt: json["DeletedAt"],
        chamaId: json["chama_id"] ?? 0,
        memberId: json["member_id"] ?? 0,
        dueAmount: json["due_amount"] ?? 0.0,
        chamaBalance: json["chama_balance"] ?? 0.0,
        paidAmount: json["paid_amount"] ?? 0.0,
        dueDate:
            json["due_date"] != null ? DateTime.parse(json["due_date"]) : null,
        occurenceCount: json["occurence_count"],
        status: json["status"] ?? '',
        cycleStartDate: json["cycle_start_date"] != null
            ? DateTime.parse(json["cycle_start_date"])
            : null,
        cycleEndDate: json["cycle_end_date"] != null
            ? DateTime.parse(json["cycle_end_date"])
            : null,
        notes: json["notes"] ?? '',
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "chama_id": chamaId,
        "member_id": memberId,
        "due_amount": dueAmount,
        "chama_balance": chamaBalance,
        "paid_amount": paidAmount,
        "due_date": dueDate?.toIso8601String(),
        "occurence_count": occurenceCount,
        "status": status,
        "cycle_start_date": cycleStartDate?.toIso8601String(),
        "cycle_end_date": cycleEndDate?.toIso8601String(),
        "notes": notes,
      };
}
