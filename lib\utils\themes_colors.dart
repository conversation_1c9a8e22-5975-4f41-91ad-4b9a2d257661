import 'package:flex_color_scheme/flex_color_scheme.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/main.dart' as main;

class CustomTheme {
  late ThemeData lightTheme;
  late ThemeData darkTheme;
}

class EbonyClayTheme extends CustomTheme {
  @override
  ThemeData get lightTheme => FlexThemeData.light(
        scheme: FlexScheme.indigoM3,
        surfaceMode: FlexSurfaceMode.levelSurfacesLowScaffold,
        blendLevel: 7,
        subThemesData: const FlexSubThemesData(
          useTextTheme: true,
        ),
        textTheme:    const TextTheme(
          bodyLarge: TextStyle(
            color: Colors.black, 
            fontFamily: 'Poppins', 
          ),
          bodyMedium: TextStyle(
            color: Colors.black87, 
            fontFamily: 'Poppins', 
          ),
          // titleLarge: TextStyle(
          //   color: Colors.black, 
          //   fontFamily: 'Poppins', 
          // ),
          // titleMedium: TextStyle(
          //   color: Colors.black, 
          //   fontFamily: 'Poppins', 
          // ),
          // titleSmall: TextStyle(
          //   color: Colors.black54, 
          //   fontFamily: 'Poppins', 
          // ),
        ),
            keyColors: const FlexKeyColors(
          keepPrimary: true,
          keepTertiary: true,
          keepPrimaryContainer: true,
          keepSecondaryContainer: true,
          keepTertiaryContainer: true,
        ),
        visualDensity: FlexColorScheme.comfortablePlatformDensity,
        // To use the Playground font, add GoogleFonts package and uncomment
        fontFamily: GoogleFonts.sen().fontFamily,
      );

  @override
  ThemeData get darkTheme => FlexThemeData.dark(
        scheme: FlexScheme.indigoM3,
        surfaceMode: FlexSurfaceMode.levelSurfacesLowScaffold,
        blendLevel: 13,
        textTheme: 
        const TextTheme(
          bodyLarge: TextStyle(
            color: Colors.white, 
            fontFamily: 'Poppins', 
          ),
          bodyMedium: TextStyle(
            color: Colors.white70, 
            fontFamily: 'Poppins', 
          ),
          // titleLarge: TextStyle(
          //   color: Colors.white, 
          //   fontFamily: 'Poppins', 
          // ),
          // titleMedium: TextStyle(
          //   color: Colors.white, 
          //   fontFamily: 'Poppins',
          // ),
          // titleSmall: TextStyle(
          //   color: Colors.white70, 
          //   fontFamily: 'Poppins', 
          // ),
        ),
        subThemesData: const FlexSubThemesData(
          useTextTheme: true,
        ),
        keyColors: const FlexKeyColors(),
        visualDensity: FlexColorScheme.comfortablePlatformDensity,
        // To use the Playground font, add GoogleFonts package and uncomment
        fontFamily: GoogleFonts.sen().fontFamily,
      );
}

class ColorUtil {
  static Color error = const Color.fromARGB(255, 189, 27, 78);
  static Color blueColor = const Color(0xFF3661C2);
  static Color secColor = const Color(0xffBAE9FD);
  static Color yellowOrange = const Color(0xffD5B13C);
}

String _appTheme = "primary";

/// Helper class for managing themes and colors.
class ThemeHelper {
  // A map of custom color themes supported by the app
  final Map<String, PrimaryColors> _supportedCustomColor = {
    'primary': PrimaryColors()
  };

// A map of color schemes supported by the app
  final Map<String, ColorScheme> _supportedColorScheme = {
    'primary': ColorSchemes.primaryColorScheme
  };

  /// Changes the app theme to [_newTheme].
  void changeTheme(String _newTheme) {
    _appTheme = _newTheme;
  }

  /// Returns the primary colors for the current theme.
  PrimaryColors _getThemeColors() {
    //throw exception to notify given theme is not found or not generated by the generator
    if (!_supportedCustomColor.containsKey(_appTheme)) {
      throw Exception(
          "$_appTheme is not found.Make sure you have added this theme class in JSON Try running flutter pub run build_runner");
    }
    //return theme from map

    return _supportedCustomColor[_appTheme] ?? PrimaryColors();
  }

  /// Returns the current theme data.
  ThemeData _getThemeData() {
    //throw exception to notify given theme is not found or not generated by the generator
    if (!_supportedColorScheme.containsKey(_appTheme)) {
      throw Exception(
          "$_appTheme is not found.Make sure you have added this theme class in JSON Try running flutter pub run build_runner");
    }
    //return theme from map

    var colorScheme =
        _supportedColorScheme[_appTheme] ?? ColorSchemes.primaryColorScheme;
    return ThemeData(
      visualDensity: VisualDensity.standard,
      colorScheme: colorScheme,
      textTheme: TextThemes.textTheme(colorScheme),
      scaffoldBackgroundColor: AppColors.background,
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: colorScheme.primary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(22.h),
          ),
          visualDensity: const VisualDensity(
            vertical: -4,
            horizontal: -4,
          ),
          padding: EdgeInsets.zero,
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          backgroundColor: Colors.transparent,
          side: BorderSide(
            color: appTheme.indigo200,
            width: 1.h,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.h),
          ),
          visualDensity: const VisualDensity(
            vertical: -4,
            horizontal: -4,
          ),
          padding: EdgeInsets.zero,
        ),
      ),
    );
  }

  /// Returns the primary colors for the current theme.
  PrimaryColors themeColor() => _getThemeColors();

  /// Returns the current theme data.
  ThemeData themeData() => _getThemeData();
}

/// Class containing the supported text theme styles.
class TextThemes {
  static TextTheme textTheme(ColorScheme colorScheme) => 
  main.isLight.value ? 
  
  TextTheme(
        bodyLarge: TextStyle(
          color: appTheme.gray600,
          fontSize: 16,
          fontFamily: 'Poppins',
          fontWeight: FontWeight.w400,
        ),
        bodyMedium: TextStyle(
          color: colorScheme.primary,
          fontSize: 14,
          fontFamily: 'Poppins',
          fontWeight: FontWeight.w400,
        ),
         bodySmall: TextStyle(
          color: colorScheme.primary,
          fontSize: 12,
          fontFamily: 'Poppins',
          // fontWeight: FontWeight.w400,
        ),
        titleLarge: TextStyle(
          color: appTheme.black900,
          fontSize: 22,
          fontFamily: 'Poppins',
          fontWeight: FontWeight.w600,
        ),
        titleMedium: TextStyle(
          color: appTheme.black900,
          fontSize: 16,
          fontFamily: 'Poppins',
          fontWeight: FontWeight.w500,
        ),
        titleSmall: TextStyle(
          color: appTheme.gray90001,
          fontSize: 14,
          fontFamily: 'Poppins',
          fontWeight: FontWeight.w500,
        ),
      )
: TextTheme(
  bodyLarge: TextStyle(
    color: appTheme.whiteA700,
    fontSize: 16,
    fontFamily: 'Poppins',
    fontWeight: FontWeight.w400,
  ),
  bodyMedium: TextStyle(
    color: appTheme.whiteA70001,
    fontSize: 14,
    fontFamily: 'Poppins',
    fontWeight: FontWeight.w400,
  ),
    bodySmall: TextStyle(
    color: appTheme.whiteA70001,
    fontSize: 12,
    fontFamily: 'Poppins',
    // fontWeight: FontWeight.w400,
  ),
  titleLarge: TextStyle(
    color: appTheme.whiteA700,
    fontSize: 22,
    fontFamily: 'Poppins',
    fontWeight: FontWeight.w600,
  ),
  titleMedium: TextStyle(
    color: appTheme.whiteA700,
    fontSize: 16,
    fontFamily: 'Poppins',
    fontWeight: FontWeight.w500,
  ),
  titleSmall: TextStyle(
    color: appTheme.whiteA700.withOpacity(0.7),
    fontSize: 14,
    fontFamily: 'Poppins',
    fontWeight: FontWeight.w500,
  ),
      );

}

/// Class containing the supported color schemes.
class ColorSchemes {
  static const primaryColorScheme = ColorScheme.light(
    // Primary colors
    primary: Color(0XFF4355B6),
    secondaryContainer: Color(0XFF3D4EB0),

    // On colors(text colors)
    onPrimary: Color(0XFF130F26),
    onPrimaryContainer: Color(0XFF7D7D7D),
  );
}

/// Class containing custom colors for a primary theme.
class PrimaryColors {
  // Black
  Color get black900 => const Color(0XFF000000);
  Color get black90016 => const Color(0X16000000);

  // BlueGray
  Color get blueGray100 => const Color(0XFFCFD2D4);
  Color get blueGray10001 => const Color(0XFFCECED3);
  Color get blueGray200 => const Color(0XFFAAAEB1);
  Color get blueGray400 => const Color(0XFF8C8C8C);
  Color get blueGray700 => const Color(0XFF545963);

  // Gray
  Color get gray50 => const Color(0XFFF8FAFC);
  Color get gray5001 => const Color(0XFFF8F8F8);
  Color get gray200 => const Color(0XFFE5E7EB);
  Color get gray600 => const Color(0XFF6E6F79);
  Color get gray700 => const Color(0xFF404040);
  Color get gray800 => const Color(0xFF2B2B2B);
  Color get gray900 => const Color(0XFF202020);
  Color get gray90001 => const Color(0XFF1C1C1C);
  Color get gray100 => const Color(0XFFF6EDFF);

  //green
  Color get green800 => const Color(0XFF15803D);

//purple
  Color get deepPurple500 => const Color(0XFF6750A4);

  // Indigo
  Color get indigo100 => const Color(0XFFB8BFE5);
  Color get indigo200 => const Color(0XFF8F9AD5);
  Color get indigo50 => const Color(0XFFE2E5F4);
  Color get indigo500 => const Color(0XFF4160AF);

  // White
  Color get whiteA700 => const Color(0XFFFFFFFF);
  Color get whiteA70001 => const Color(0XFFFFFBFE);
  Color get shadowWhiteA70001 => const Color.fromARGB(255, 234, 229, 234);
}

PrimaryColors get appTheme => ThemeHelper().themeColor();
ThemeData get theme => ThemeHelper().themeData();

class AppDecoration {
  // Fill decorations
  static BoxDecoration get fillGray => BoxDecoration(
        color: main.isLight.value ? appTheme.gray50 : appTheme.gray900,
      );
  static BoxDecoration get fillGray100 => BoxDecoration(
        color: main.isLight.value ? appTheme.gray100 : appTheme.gray900,
      );

  static BoxDecoration get fillWhiteA => BoxDecoration(
        color: main.isLight.value ? appTheme.whiteA70001 : appTheme.gray900,
      );

  static BoxDecoration get fillAGray => BoxDecoration(
        color: main.isLight.value ? appTheme.indigo500 : appTheme.indigo200,
      );
  static BoxDecoration get fillSlate => BoxDecoration(
        color: main.isLight.value ? appTheme.gray200 : appTheme.gray700
      );

  static BoxDecoration get fillBlueGray => BoxDecoration(
        color: main.isLight.value ? appTheme.blueGray200 : appTheme.blueGray700,
      );
  static BoxDecoration get fillPrimary => BoxDecoration(
        color: theme.colorScheme.primary,
      );
  static BoxDecoration get outlineGray => BoxDecoration(
        color: main.isLight.value ? appTheme.whiteA70001 : appTheme.gray90001,
        border: Border.all(
          color: main.isLight.value 
              ? appTheme.gray900.withOpacity(0.1)
              : appTheme.gray50.withOpacity(0.1),
          width: 1.h,
        ),
      );
  static BoxDecoration get outlineIndigo => BoxDecoration(
        border: Border.all(
          color: main.isLight.value ? appTheme.indigo200 : appTheme.indigo100,
          width: 1.h,
        ),
      );
  static BoxDecoration get outlineBlueGray => BoxDecoration(
        color: main.isLight.value ? appTheme.whiteA70001 : appTheme.gray90001,
        border: Border.all(
          color: main.isLight.value ? appTheme.blueGray100 : appTheme.blueGray400,
          width: 1.h,
        ),
      );

  static BoxDecoration get shadow1 => BoxDecoration(
        color: main.isLight.value 
            ? appTheme.whiteA700 
            : appTheme.gray90001,
        boxShadow: [
          BoxShadow(
            color: main.isLight.value
                ? appTheme.black900.withOpacity(0.09)
                : appTheme.black900.withOpacity(0.4),
            spreadRadius: -16,
            blurRadius: 2,
            offset: const Offset(0, 20),
          ),
        ],
      );
  static BoxDecoration get shadow1Themed => BoxDecoration(
        color: main.isLight.value
            ? appTheme.whiteA700
            : appTheme.black900.withOpacity(0.2),
        boxShadow: [
          BoxShadow(
            color: main.isLight.value
                ? appTheme.black900.withOpacity(0.09)
                : appTheme.whiteA700.withOpacity(0.05),
            spreadRadius: 2.h,
            blurRadius: 4.h,
            offset: const Offset(0, 2),
          ),
        ],
      );
  static BoxDecoration get outlineBluegray100 => BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: main.isLight.value ? appTheme.blueGray100 : appTheme.blueGray400,
            width: 1.h,
          ),
        ),
      );
}

class BorderRadiusStyle {
  // Circle borders
  static BorderRadius get circleBorder16 => BorderRadius.circular(
        16.h,
      );
  static BorderRadius get circleBorder25 => BorderRadius.circular(
        20.h,
      );

  static BorderRadius get roundedBorder6 => BorderRadius.circular(
        6.h,
      );
  static BorderRadius get roundedBorder8 => BorderRadius.circular(
        10.h,
      );
  static BorderRadius get circleBorder22 => BorderRadius.circular(
        22.h,
      );
}

// Comment/Uncomment the below code based on your Flutter SDK version.

// For Flutter SDK Version 3.7.2 or greater.

double get strokeAlignInside => BorderSide.strokeAlignInside;

double get strokeAlignCenter => BorderSide.strokeAlignCenter;

double get strokeAlignOutside => BorderSide.strokeAlignOutside;

// For Flutter SDK Version 3.7.1 or less.

// StrokeAlign get strokeAlignInside => StrokeAlign.inside;
//
// StrokeAlign get strokeAlignCenter => StrokeAlign.center;
//
// StrokeAlign get strokeAlignOutside => StrokeAlign.outside;

