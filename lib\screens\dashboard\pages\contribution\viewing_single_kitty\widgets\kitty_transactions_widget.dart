// ignore_for_file: must_be_immutable, use_build_context_synchronously, prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:grouped_list/grouped_list.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/models/transac_kitt_model.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/see_all_transactions_screen/widgets/export_widget.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/see_all_transactions_screen/widgets/transaction_item.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/utils/size_config.dart';
import '../../../../../../utils/utils_exports.dart';

class TransactionWidget extends StatelessWidget {
  final DataController dataController = Get.find<DataController>();
  final ContributeController contributeController = Get.find();
  final KittyController controller = Get.put(KittyController());

  TransactionWidget({super.key});
  final dateformat = DateFormat('EE, dd MMMM');

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SizedBox(
        child: Container(
          margin: EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.only(top: 20.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text("Transactions",
                        style: CustomTextStyles.titleSmallGray900),
                    GestureDetector(
                      onTap: () {
                        showModalBottomSheet(
                          context: context,
                          builder: (BuildContext context) {
                            return ExportContentWidget2(
                              eventId: null,
                              singleTrans: false,
                            );
                          },
                        );
                      },
                      child: Chip(
                        label: Text(
                          "Export",
                          style: CustomTextStyles.titleSmallIndigo500,
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        Get.toNamed(NavRoutes.seeAlltranscScreen);
                      },
                      child: Chip(
                        label: Text("See all",
                            style: CustomTextStyles.titleSmallIndigo500),
                      ),
                    )
                  ],
                ),
              ),
              SizedBox(
                height: 15.h,
              ),
              GetX(
                init: KittyController(),
                initState: (state) {
                  Future.delayed(Duration.zero, () async {
                    try {
                      await state.controller?.getKittyContributions(
                        kittyId: dataController.kitty.value.kitty?.id ?? 0,
                      );
                      print("Contributions loaded successfully");
                    } catch (e) {
                      print("Error loading Contributions: $e");
                      throw e;
                    }
                  });
                },
                builder: (KittyController controller) {
                  if (controller.loadingTransactions.isTrue) {
                    return SizedBox(
                      height: SizeConfig.screenHeight * .33,
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SpinKitDualRing(
                              color: ColorUtil.blueColor,
                              lineWidth: 4.sp,
                              size: 40.0.sp,
                            ),
                            const Text(
                              "loading..",
                              style: TextStyle(
                                color: Colors.white,
                              ),
                            )
                          ],
                        ),
                      ),
                    );
                  } else if (controller.transactionsKitty.isEmpty) {
                    return Column(
                      children: [
                        const Text("You have no transactions yet"),
                        Image.asset(
                          AssetUrl.notFound,
                          height: 90.h,
                        ),
                        Obx(() => CustomKtButton(
                              isLoading:
                                  contributeController.isgetkittyloading.isTrue,
                              onPress: () async {
                                final id = dataController.kitty.value.kitty?.id;

                                final res =
                                    await contributeController.getKitty(id: id);
                                if (res) {
                                  Get.toNamed(NavRoutes.contibuteScreen);
                                }
                              },
                              width: 110.w,
                              height: 23.h,
                              btnText: "Contribute",
                            )),
                      ],
                    );
                  } else {
                    return Expanded(
                      child: GroupedListView<TransactionModel, DateTime>(
                        sort: false,
                        elements: controller.transactionsKitty,
                        groupBy: (TransactionModel element) {
                          DateTime date = element.createdAt!.toLocal();
                          return DateTime(date.year, date.month, date.day);
                        },
                        groupHeaderBuilder: (value) {
                          final date =
                              dateformat.format(value.createdAt!.toLocal());
                          return Container(
                            decoration: BoxDecoration(
                              color: appTheme.gray50,
                              borderRadius:
                                  BorderRadius.all(Radius.elliptical(5, 10)),
                            ),
                            child: Padding(
                              padding:
                                  const EdgeInsets.symmetric(vertical: 8.0),
                              child: Text(
                                date,
                                style: const TextStyle(
                                  color: Colors.black,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 15.0,
                                ),
                              ),
                            ),
                          );
                        },
                        itemBuilder: (_, TransactionModel item) {
                          return TransactionItem(item: item);
                        },
                        separator: const Padding(
                          padding: EdgeInsets.symmetric(vertical: 5),
                        ),
                      ),
                    );
                  }
                },
              ),
            ],

            //TO DO REFACTOR
          ),
        ),
      ),
    );
  }
}
