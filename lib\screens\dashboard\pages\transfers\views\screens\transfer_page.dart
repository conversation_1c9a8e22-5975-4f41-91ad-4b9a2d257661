// Unified Transfer Page
// Main transfer screen that handles all transfer types

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty/configs/country_specifics.dart';
import 'package:onekitty/configs/payment_channels.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/events_controller.dart';
import 'package:onekitty/utils/app_bar/custom_app_bar.dart';
import 'package:onekitty/utils/custom_button.dart';
import 'package:onekitty/utils/formatted_currency.dart';
import 'package:onekitty/widgets/custom_international_phone_input.dart';
import 'package:onekitty/screens/dashboard/pages/events/controllers/controllers.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/models/auth/payments_channels.dart';
import 'package:onekitty/utils/my_text_field.dart';
import 'package:onekitty/utils/show_cached_network_image.dart';
import 'package:onekitty/screens/onboarding/passwd_req_screen.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/builTabs.dart';
import '../../models/transfer_type.dart';
import '../../controllers/transfer_controller.dart';
import '../../services/transfer_service.dart';
import '../widgets/payment_method_selector.dart';

class TransferPage extends StatefulWidget {
  final TransferPageConfig config;

  const TransferPage({
    super.key,
    required this.config,
  });

  @override
  State<TransferPage> createState() => _TransferPageState();
}

class _TransferPageState extends State<TransferPage>
    with TickerProviderStateMixin {
  late final TransferController controller;
  final formKey = GlobalKey<FormState>();
  PhoneNumber number = CountryConfig.phoneNumber;
  final paymentChannel = Get.isRegistered<PaymentChannel>()
      ? Get.find<PaymentChannel>()
      : Get.put(PaymentChannel());

  @override
  void initState() {
    super.initState();
    // Create a unique controller instance for each transfer type to avoid shared state
    final controllerTag =
        '${widget.config.transferType.name}_${widget.config.entityId}';
    controller = Get.put(TransferController(), tag: controllerTag);
    controller.initialize(widget.config);
    controller.initializeTabController(this);
  }

  @override
  void dispose() {
    // Clean up the controller when the page is disposed
    final controllerTag =
        '${widget.config.transferType.name}_${widget.config.entityId}';

    // Clean up controller state before deletion
    if (Get.isRegistered<TransferController>(tag: controllerTag)) {
      final controller = Get.find<TransferController>(tag: controllerTag);
      controller.cleanupState();
    }

    // Delete the controller instance
    Get.delete<TransferController>(tag: controllerTag);

    // Also clean up any shared transfer service state
    if (Get.isRegistered<TransferService>()) {
      // The service is shared, so we don't delete it, but we can reset any cached data
      final transferService = Get.find<TransferService>();
      // Add any service cleanup if needed
    }

    super.dispose();
  }
// Update the build method - remove reason from footer
@override
Widget build(BuildContext context) {
  final screenSize = MediaQuery.of(context).size;
  return Scaffold(
    body: SafeArea(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 20),
        child: Form(
          key: formKey,
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      const RowAppBar(),
                      _buildHeader(),
                      SizedBox(height: 10.h),
                      if (widget.config.transferType == TransferType.chama)
                        _buildTransferTypeSelector(),
                      _buildAmountField(),
                      SizedBox(height: 10.h),
                      _buildTransferModeSelector(),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 10.h),
              Obx(() => CustomKtButton(
                    isLoading: controller.isTransferring.value,
                    onPress: _handleTransfer,
                    btnText: 'transfer'.tr,
                  )),
              SizedBox(height: 15.h),
            ],
          ),
        ),
      ),
    ),
  );
}

// Update _buildTransferModeSelector - remove fixed height
Widget _buildTransferModeSelector() {
  return DefaultTabController(
    length: 4,
    child: Column(
      children: [
        buildTabs(controller.tabController, context),
        SizedBox(
          height: MediaQuery.of(context).size.height * 0.6.h,
          child: TabBarView(
            controller: controller.tabController,
            children: [
              _buildMobileMoneyContent(),
              _buildPaybillContent(),
              _buildTillContent(),
              _buildBankContent(),
            ],
          ),
        ),
      ],
    ),
  );
}

// Update each content builder to include reason field at the bottom
Widget _buildMobileMoneyContent() {
  return SingleChildScrollView(
    child: Column(
      children: [
        PaymentMethodSelector(
          selectedProvider: controller.selectedProvider,
          onProviderChanged: (provider) =>
              controller.selectedProvider.value = provider,
        ),
        SizedBox(height: 16.h),
        Align(
          alignment: Alignment.topLeft,
          child: Text(
            'enter_beneficiary_phone_number'.tr,
            style: TextStyle(
              fontSize: 12.spMin,
              color: Colors.black,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        SizedBox(height: 10.h),
        CustomInternationalPhoneInput(
          onInputChanged: (PhoneNumber no) {
            number = no;
            controller.phoneController.text =
                no.phoneNumber.toString().replaceAll("+", '');
          },
          validator: (value) {
            if (controller.currentPage.value != 0) return null;
            if (value == null || value.isEmpty || value.length < 9) {
              return 'phone_number_is_required'.tr;
            }
            return null;
          },
        ),
        SizedBox(height: 16.h),
        _buildReasonField(),
      ],
    ),
  );
}

Widget _buildPaybillContent() {
  return SingleChildScrollView(
    child: Column(
      children: [
        MyTextFieldwValidator(
          keyboardType: TextInputType.text,
          controller: controller.paybillController,
          title: 'mpesa_paybill'.tr,
          allowAlphanumeric: true,
          validator: (value) {
            if (controller.currentPage.value != 1) return null;
            if (value == null || value.isEmpty) return 'paybill_is_required'.tr;
            return null;
          },
        ),
        SizedBox(height: 20.h),
        MyTextFieldwValidator(
          keyboardType: TextInputType.text,
          controller: controller.accountNumberController,
          title: 'account_number'.tr,
          allowAlphanumeric: true,
          validator: (value) {
            if (controller.currentPage.value != 1) return null;
            if (value == null || value.isEmpty)
              return 'account_number_is_required'.tr;
            return null;
          },
        ),
        SizedBox(height: 16.h),
        _buildReasonField(),
      ],
    ),
  );
}

Widget _buildTillContent() {
  return SingleChildScrollView(
    child: Column(
      children: [
        MyTextFieldwValidator(
          controller: controller.tillNumberController,
          keyboardType: TextInputType.number,
          title: 'mpesa_till_number'.tr,
          validator: (value) {
            if (controller.currentPage.value != 2) return null;
            if (value == null || value.isEmpty) return 'till_number_is_required'.tr;
            return null;
          },
        ),
        SizedBox(height: 16.h),
        _buildReasonField(),
      ],
    ),
  );
}

Widget _buildBankContent() {
  return SingleChildScrollView(
    child: Obx(() => Column(
          children: [
            GestureDetector(
              onTap: () => _showBankSelectionBottomSheet(),
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    controller.selectedBank.value != null
                        ? Row(
                            children: [
                              SizedBox(
                                height: 30,
                                width: 30,
                                child: ShowCachedNetworkImage(
                                  errorWidget:
                                      const Icon(Icons.account_balance),
                                  imageUrl:
                                      controller.selectedBank.value!.imageUrl,
                                  height: 30,
                                  width: 30,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Text(controller.selectedBank.value!.name),
                            ],
                          )
                        : Text('select_bank'.tr),
                    const Icon(Icons.arrow_drop_down),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16.h),
            if (controller.selectedBank.value != null)
              MyTextFieldwValidator(
                validator: (value) {
                  if (controller.currentPage.value != 3) return null;
                  if (controller.selectedBank.value == null) return null;
                  if (value == null || value.isEmpty) {
                    return 'please_enter_bank_account_number'.tr;
                  }
                  return null;
                },
                controller: controller.bankAccountController,
                title: 'bank_account_number'.tr,
                onChanged: (val) => controller.selectedProvider.value =
                    controller.selectedBank.value?.channelCode ??
                        CountryConfig.defaultPaymentChannel,
              ),
            SizedBox(height: 16.h),
            _buildReasonField(),
          ],
        )),
  );
}
  Widget _buildHeader() {
    return Column(
      children: [
        Text(
          'make_a_transfer'.tr,
          style: context.titleText,
        ),
        const SizedBox(height: 5),
        Text(
          _getSubtitleText(),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  String _getSubtitleText() {
    switch (widget.config.transferType) {
      case TransferType.event:
        return 'make_transactions_with_ease'.tr;
      case TransferType.chama:
        return 'transfer_funds_from_chama_account'.tr;
      case TransferType.penalty:
        return 'transfer_from_penalty_kitty'.tr;
    }
  }

  Widget _buildTransferTypeSelector() {
    return Obx(() {
      final chamaController = Get.find<ChamaController>();
      if (chamaController.penaltyKittyBalance <= 0)
        return const SizedBox.shrink();

      return Column(
        children: [
          RadioListTile(
            title: Text('transfer_from_penalty'.tr),
            value: 'penalty',
            groupValue: controller.transferData['transferType'],
            onChanged: (value) {
              controller.transferData['transferType'] = value;
            },
          ),
          RadioListTile(
            title: Text('transfer_from_chama'.tr),
            value: 'chama',
            groupValue: controller.transferData['transferType'],
            onChanged: (value) {
              controller.transferData['transferType'] = value;
            },
          ),
        ],
      );
    });
  }

  Widget _buildAmountField() {
    return CustomTextField(
      controller: controller.amountController,
      isRequired: true,
      showNoKeyboard: true,
      labelText: 'enter_amount'.tr,
      validator: (p0) {
        if (p0!.isEmpty) {
          return 'this_field_cannot_be_empty'.tr;
        }
        return null;
      },
    );
  }

 Widget _buildReasonField() {
    return Padding(
      padding: const EdgeInsets.only(top: 8.0),
      child: Column(
        children: [
          Align( 
            alignment: Alignment.topLeft,
            child: Text('Reason')),
          SizedBox(height: 4),
          CustomTextField(
            maxLength: 4,
            controller: controller.reasonController,
            labelText: 'enter_reason_for_transaction'.tr,
          ),
        ],
      ),
    );
  }

  void _showBankSelectionBottomSheet() {
    final searchController = TextEditingController();
    final banks = Get.find<GlobalControllers>()
        .paymentChannels
        .where((e) => e.category == Category.BANK)
        .toList();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          final filteredBanks = banks.where((bank) {
            final searchTerm = searchController.text.toLowerCase();
            return bank.name.toLowerCase().contains(searchTerm) ||
                bank.description.toLowerCase().contains(searchTerm);
          }).toList();

          return Container(
            height: MediaQuery.of(context).size.height * 0.7,
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: TextField(
                    controller: searchController,
                    decoration: InputDecoration(
                      hintText: 'search_bank'.tr,
                      prefixIcon: const Icon(Icons.search),
                      border: const OutlineInputBorder(),
                    ),
                    onChanged: (value) => setState(() {}),
                  ),
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: ListView.builder(
                    itemCount: filteredBanks.length,
                    itemBuilder: (context, index) {
                      final bank = filteredBanks[index];
                      return ListTile(
                        leading: SizedBox(
                          height: 30,
                          width: 30,
                          child: ShowCachedNetworkImage(
                            errorWidget: const Icon(Icons.account_balance),
                            imageUrl: bank.imageUrl,
                            height: 30,
                            width: 30,
                          ),
                        ),
                        title: Text(bank.name),
                        onTap: () {
                          controller.selectedBank.value = bank;
                          controller.selectedProvider.value =
                              bank.channelCode ??
                                  CountryConfig.defaultPaymentChannel;
                          Navigator.pop(context);
                        },
                      );
                    },
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  void _handleTransfer() async {
    // Validate amount first
    if (controller.amountController.text.isEmpty) {
      Get.snackbar('error'.tr, 'please_enter_amount'.tr,
          backgroundColor: Colors.red);
      return;
    }

    // Ensure currentPage is synchronized with actual tab index
    controller.currentPage.value = controller.tabController.index;

    // Clear data from inactive tabs to prevent cross-contamination
    _clearInactiveTabData();

    // Validate based on current transfer mode
    String? validationError = _validateCurrentMode();
    if (validationError != null) {
      Get.snackbar('error'.tr, validationError, backgroundColor: Colors.red);
      return;
    }

    // Prepare transfer data based on current tab only
    final transferData = _prepareTransferData();

    final success = await controller.initiateTransfer(
      amount: int.parse(controller.amountController.text),
      transferMode: transferData['transferMode'],
      reason: controller.reasonController.text,
      phoneNumber: transferData['phoneNumber'],
      paybill: transferData['paybill'],
      accNo: transferData['accNo'],
      till: transferData['till'],
      bankAccount: transferData['bankAccount'],
    );

    if (success) {
      _showConfirmationDialog();
    }
  }

  /// Clear data from tabs that are not currently active
  void _clearInactiveTabData() {
    final currentTab = controller.currentPage.value;

    // Clear data from all tabs except the current one
    for (int i = 0; i < 4; i++) {
      if (i != currentTab) {
        switch (i) {
          case 0: // Mobile Money
            if (currentTab != 0) {
              // Don't clear phone if we're not on mobile tab
              // The phone will be set to user's phone as fallback
            }
            break;
          case 1: // Paybill
            if (currentTab != 1) {
              controller.paybillController.clear();
              controller.accountNumberController.clear();
            }
            break;
          case 2: // Till
            if (currentTab != 2) {
              controller.tillNumberController.clear();
            }
            break;
          case 3: // Bank
            if (currentTab != 3) {
              controller.selectedBank.value = null;
              controller.bankAccountController.clear();
            }
            break;
        }
      }
    }
  }

  /// Prepare transfer data based on current tab only
  Map<String, dynamic> _prepareTransferData() {
    final currentTab = controller.currentPage.value;

    // Set default phone if empty (for mobile money or fallback)
    String phoneNumber = "";
    if (currentTab == 0 || controller.phoneController.text.isEmpty) {
      final user = controller.config.transferType == TransferType.event
          ? Get.find<Eventcontroller>().getLocalUser()
          : Get.find<ChamaController>().getLocalUser();

      if (currentTab == 0 && controller.phoneController.text.isNotEmpty) {
        phoneNumber = "${number.dialCode}${controller.phoneController.text.substring(controller.phoneController.text.length - 9)}"
            .replaceAll("+", '');
      } else {
        phoneNumber = user?.phoneNumber ?? "";
      }
    }

    // Set provider based on current tab
    if (currentTab != 0) {
      controller.selectedProvider.value = CountryConfig.defaultPaymentChannel;
    }

    return {
      'transferMode': _getTransferMode(),
      'phoneNumber': phoneNumber,
      'paybill': currentTab == 1 ? controller.paybillController.text : null,
      'accNo': currentTab == 1 ? controller.accountNumberController.text : null,
      'till': currentTab == 2 ? controller.tillNumberController.text : null,
      'bankAccount': currentTab == 3 ? controller.bankAccountController.text : null,
    };
  }

  String _getTransferMode() {
    switch (controller.currentPage.value) {
      case 0:
        return "WALLET";
      case 1:
        return "PAYBILL";
      case 2:
        return "TILL";
      case 3:
        return "BANK";
      default:
        return "";
    }
  }

  void _showConfirmationDialog() {
    final transferData = controller.transferData.value;

    if (widget.config.transferType == TransferType.event) {
      _showEventConfirmationDialog(transferData);
    } else {
      _showChamaConfirmationDialog();
    }
  }

  void _showEventConfirmationDialog(Map<String, dynamic> transferData) {
    _showUnifiedConfirmationDialog(transferData);
  }

  void _showChamaConfirmationDialog() {
    final transferData = controller.transferData.value;
    _showUnifiedConfirmationDialog(transferData);
  }

  void _showUnifiedConfirmationDialog(Map<String, dynamic> transferData) {
    final apiMessage = transferData['message'] ?? 'confirm_transfer'.tr;
    final data = transferData['data'] ?? {};
    final paymentMode = _getTransferMode();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          apiMessage,
          style: context.titleText?.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
            fontSize: 16,
          ),
        ),
        content: _buildUnifiedConfirmationContent(data, paymentMode),
        actions: [
          SizedBox(
            height: 50,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Theme.of(context).colorScheme.onSurface,
                      side: BorderSide(
                          color: Theme.of(context).colorScheme.outline),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 24, vertical: 12),
                    ),
                    child: Text('cancel'.tr),
                  ),
                ),
                Obx(() => CustomKtButton(
                      isLoading: controller.isConfirming.value,
                      width: 140.w,
                      onPress: _confirmTransfer,
                      btnText: 'confirm'.tr,
                    )),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String? _validateCurrentMode() {
    switch (controller.currentPage.value) {
      case 0: // Mobile Money
        if (controller.phoneController.text.isEmpty) {
          return 'please_enter_phone_number'.tr;
        }
        break;
      case 1: // Paybill
        if (controller.paybillController.text.isEmpty) {
          return 'please_enter_paybill_number'.tr;
        }
        if (controller.accountNumberController.text.isEmpty) {
          return 'please_enter_account_number'.tr;
        }
        break;
      case 2: // Till
        if (controller.tillNumberController.text.isEmpty) {
          return 'please_enter_till_number'.tr;
        }
        break;
      case 3: // Bank
        if (controller.selectedBank.value == null) {
          return 'please_select_a_bank'.tr;
        }
        if (controller.bankAccountController.text.isEmpty) {
          return 'please_enter_bank_account_number'.tr;
        }
        break;
    }
    return null;
  }

  Widget _buildUnifiedConfirmationContent(
      Map<String, dynamic> data, String paymentMode) {
    // Handle both event and chama data formats
    final isEventTransfer = widget.config.transferType == TransferType.event;

    // Extract values from API response data - handle different field names for events vs chama
    final amount = double.tryParse(data['amount_new']?.toString() ??
            data['amount']?.toString() ??
            data['amount_received']?.toString() ??
            '0') ??
        0.0;

    final accountName = data['account_name']?.toString() ??
        data['beneficiary_name']?.toString() ??
        '';

    final receiverAccount = data['receiver_account']?.toString() ??
        data['beneficiary_account']?.toString() ??
        '';

    final balance = double.tryParse(data['balance']?.toString() ??
            data['kitty_balance']?.toString() ??
            '0') ??
        0.0;

    final newBalance = double.tryParse(data['balance_new']?.toString() ??
            data['remaining_balance']?.toString() ??
            '0') ??
        0.0;

    final totalCharges = double.tryParse(data['charges_total']?.toString() ??
            data['charges']?.toString() ??
            '0') ??
        0.0;

    /*
         
    */

    final thirdPartyCharges =
        double.tryParse(data['third_party_charges']?.toString() ?? '0') ?? 0.0;

    final platformCharges =
        double.tryParse(data['charges']?.toString() ?? '0') ?? 0.0;

    // For events, calculate platform charges differently if needed
    final actualPlatformCharges =
        isEventTransfer ? (totalCharges - thirdPartyCharges) : platformCharges;

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Transfer Amount - Primary Focus
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Text(
                  'transfer_amount'.tr,
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context)
                        .colorScheme
                        .onPrimaryContainer
                        .withOpacity(0.8),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  FormattedCurrency.getFormattedCurrency(amount),
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onPrimaryContainer,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),

          // Recipient Details
          if (accountName.isNotEmpty || receiverAccount.isNotEmpty) ...[
            _buildSection(
              context,
              title: 'recipient_details'.tr,
              children: [
                if (accountName.isNotEmpty)
                  _buildDetailRow('to'.tr, accountName),
                if (receiverAccount.isNotEmpty)
                  _buildDetailRow('account'.tr, receiverAccount),
                if (paymentMode == "PAYBILL" &&
                    controller.accountNumberController.text.isNotEmpty)
                  _buildDetailRow(
                      'reference'.tr, controller.accountNumberController.text),
                _buildDetailRow('mode'.tr, paymentMode),
                _buildDetailRow(
                    'payment_method'.tr,
                    paymentChannel.getPaymentChannelName(
                        controller.selectedProvider.value)),
                if (controller.reasonController.text.isNotEmpty)
                  _buildDetailRow(
                      'reason'.tr, controller.reasonController.text),
              ],
            ),
            const SizedBox(height: 16),
          ],

          // Balance Summary
          if (balance > 0 || newBalance > 0) ...[
            _buildSection(
              context,
              title: 'balance_summary'.tr,
              children: [
                if (balance > 0)
                  _buildDetailRow(
                    'current_balance'.tr,
                    FormattedCurrency.getFormattedCurrency(balance),
                  ),
                if (newBalance > 0)
                  _buildDetailRow(
                    'new_balance'.tr,
                    FormattedCurrency.getFormattedCurrency(newBalance),
                  ),
              ],
            ),
            const SizedBox(height: 16),
          ],

          // Total Deduction - Important Info
          if (totalCharges > 0) ...[
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.errorContainer,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'total_deduction'.tr,
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.onErrorContainer,
                    ),
                  ),
                  Text(
                    FormattedCurrency.getFormattedCurrency(
                        amount + totalCharges),
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: Theme.of(context).colorScheme.onErrorContainer,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
          ],

          // Expandable Charges Breakdown
          if (totalCharges > 0) ...[
            Theme(
              data:
                  Theme.of(context).copyWith(dividerColor: Colors.transparent),
              child: ExpansionTile(
                tilePadding: EdgeInsets.zero,
                childrenPadding:
                    const EdgeInsets.only(left: 16, right: 16, bottom: 8),
                title: Text(
                  'view_charges_breakdown'.tr,
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withOpacity(0.7),
                  ),
                ),
                trailing: Icon(
                  Icons.keyboard_arrow_down,
                  size: 20,
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                ),
                children: [
                  if (actualPlatformCharges > 0)
                    _buildChargeRow(
                      context,
                      'platform_fees'.tr,
                      FormattedCurrency.getFormattedCurrency(
                          actualPlatformCharges),
                    ),
                  if (thirdPartyCharges > 0)
                    _buildChargeRow(
                      context,
                      'third_party_charges'.tr,
                      FormattedCurrency.getFormattedCurrency(thirdPartyCharges),
                    ),
                  const SizedBox(height: 8),
                  Container(
                    height: 1,
                    color:
                        Theme.of(context).colorScheme.outline.withOpacity(0.3),
                  ),
                  const SizedBox(height: 8),
                  _buildChargeRow(
                    context,
                    'total_charges'.tr,
                    FormattedCurrency.getFormattedCurrency(totalCharges),
                    isTotal: true,
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  // Helper Methods
  Widget _buildSection(BuildContext context,
      {required String title, required List<Widget> children}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        ...children,
      ],
    );
  }



  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 6),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.w500,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }

  void _confirmTransfer() async {
    final isAuthenticated =
        await Get.to(() => AuthPasswdScreen(), arguments: [false]);

    if (isAuthenticated == true) {
      final success = await controller.confirmTransfer();

      // If transfer was successful, reset the form for next transfer
      if (success) {
        controller.resetForNewTransfer();
      }
    }
  }

  Widget _buildChargeRow(BuildContext context, String label, String value,
      {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 14 : 13,
              fontWeight: isTotal ? FontWeight.w600 : FontWeight.normal,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.8),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: isTotal ? 14 : 13,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }
}
