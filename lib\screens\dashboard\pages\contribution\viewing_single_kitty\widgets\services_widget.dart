import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/contribution_kitties/beneficiaries_page.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/withdraw/withdraw.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/edit_kitty/end_date.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/edit_kitty/step1.dart';
import 'package:onekitty/screens/dashboard/pages/events/manage_delegates.dart';
import '../../../../../../utils/utils_exports.dart';
import '../../../events/signatory_transactions.dart';

class ServicesWidget extends StatelessWidget {
  const ServicesWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final hasSignatoryTransactions =
        Get.find<ContributeController>().hasSignatoryTransactions;
    return Column(
      children: [
        SizedBox(height: 8.h),
        Wrap(
          children: [
            ServiceCard(
                edit: AssetUrl.imgCalendar,
                color: AppColors.blueButtonColor,
                resources: "Edit End Date",
                onTap: () {
                  Navigator.pushAndRemoveUntil(
                      context,
                      MaterialPageRoute(builder: (context) => const EndDate()),
                      (route) => route.isActive);
                }),
            ServiceCard(
              edit: AssetUrl.imgEdit,
              resources: "Edit Details",
              onTap: () {
                Navigator.pushAndRemoveUntil(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const EditKittyDetails()),
                    (route) => route.isActive);
              },
            ),
            ServiceCard(
              edit: AssetUrl.imgUser,
              resources: "Withdraw",
              onTap: () {
                Navigator.pushAndRemoveUntil(
                    context,
                    MaterialPageRoute(
                        builder: (context) => const WithdrawPage()),
                    (route) => route.isActive);
              },
            ),
            ServiceCard(
                icon: Icons.group_outlined,
                resources: "Beneficiaries",
                onTap: () {
                  Navigator.pushAndRemoveUntil(
                      context,
                      MaterialPageRoute(
                          builder: (context) => const BeneficiariesPage()),
                      (route) => route.isActive);
                }),
            ServiceCard(
                icon: Icons.manage_accounts_outlined,
                resources: "Delegates",
                onTap: () {
                  Navigator.pushAndRemoveUntil(
                      context,
                      MaterialPageRoute(
                          builder: (context) => InvitePage( 
                                kittyId: Get.find<DataController>()
                                        .kitty
                                        .value
                                        .kitty
                                        ?.id ??
                                    0,
                                eventname: Get.find<DataController>()
                                        .kitty
                                        .value
                                        .kitty
                                        ?.title ??
                                    '',
                              )),
                      (route) => route.isActive);
                }),
            Obx(
              () => hasSignatoryTransactions.value
                  ? ServiceCard(
                      flash: true,
                      edit: AssetUrl.imgGroup7,
                      resources: "Signatory",
                      onTap: () {
                        Navigator.pushAndRemoveUntil(
                            context,
                            MaterialPageRoute(
                                builder: (context) => SignatoryTransactions(
                                      kittyId: Get.find<DataController>()
                                              .kitty
                                              .value
                                              .kitty
                                              ?.id ??
                                          0,
                                    )),
                            (route) => route.isActive);
                      },
                    )
                  : const SizedBox(),
            ),
          ],
        ),
      ],
    );
  }
}

class ServiceCard extends StatelessWidget {
  final String? edit;
  final IconData? icon;
  final String resources;
  final VoidCallback onTap;
  final bool? flash;
  final Color? color;

  const ServiceCard({
    super.key,
    this.edit,
    this.icon,
    required this.resources,
    required this.onTap,
    this.flash,
    this.color,
  }) : assert(
            edit != null || icon != null, "both edit and icon cannot be null");

  @override
  Widget build(BuildContext context) {
    final flashColor = color?.obs ?? AppColors.blueButtonColor.obs;
    if (flash ?? false) {
      Timer.periodic(const Duration(milliseconds: 800), (t) {
        flashColor.value =
            t.tick.isEven ? AppColors.blueButtonColor : Colors.red;
      });
    }
    return InkWell(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 2, vertical: 3),
        padding: EdgeInsets.symmetric(
          horizontal: 15.w,
          vertical: 4.h,
        ),
        decoration: AppDecoration.outlineIndigo.copyWith(
          borderRadius: BorderRadiusStyle.roundedBorder8,
        ),
        child: Obx(
          () => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              edit != null
                  ? SvgPicture.asset(edit!,
                      height: 30.h,
                      width: 30.w,
                      colorFilter:
                          ColorFilter.mode(flashColor.value, BlendMode.srcIn))
                  : Icon(icon, size: 30.spMin, color: flashColor.value),
              // CustomImageView(
              //   color: color,
              //   imagePath: edit,
              //   height: 30.h,
              //   width: 30.w,
              // ),
              SizedBox(height: 2.h),
              Text(
                resources,
                style: theme.textTheme.labelLarge!.copyWith(
                  color: flashColor.value,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
