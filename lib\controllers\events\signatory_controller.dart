import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage_pro/get_storage_pro.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/services/http_service.dart';
import 'package:onekitty/utils/cache_keys.dart';

import '../../models/events/signatory_transaction_model.dart';
import '../../services/api_urls.dart';
import 'events_controller.dart';

class SignatoryTransactionController extends GetxController
    implements GetxService {
  final HttpService apiProvider = Get.find();
  final logger = Get.find<Logger>();
  RxBool isLoading = false.obs;
  RxBool isProcessing = false.obs;
  RxBool comment = true.obs;
  RxList<SignatoryTransaction> signatoryTransactions =
      <SignatoryTransaction>[].obs;
  final GetStorage box = Get.find();
  Future processTransaction(
      {required bool isApproved,
      required String comment,
      required SignatoryTransaction signatoryTransaction}) async {
    isProcessing(true);
    try {
      final eventController = Get.find<Eventcontroller>();

      var response = await apiProvider.request(
          url:
              "${ApiUrls.PROCESSSIGNATORYTRANSACTIONS}?kitty_id=${signatoryTransaction.kittyId}",
          method: Method.POST,
          params: {
            "signatory_transaction_id": signatoryTransaction.id,
            "delegate_phone": eventController.getLocalUser()?.phoneNumber,
            "checkout_request_id": "",
            "is_approved": isApproved,
            "comment": comment,
            "user_id": eventController.getLocalUser()?.id,
            "kitty_id": signatoryTransaction.kittyId,
            "latitude": box.read(CacheKeys.lat),
            "longitude": box.read(CacheKeys.long),
            "device_id": box.read(CacheKeys.deviceId),
            "device_model": box.read(CacheKeys.deviceModel)
          });
      if (response.data['status'] ?? false) {
        isProcessing(false);
        Get.snackbar('Success', '', backgroundColor: Colors.green);
      } else {
        Get.snackbar('error', '${response.data['message']}', backgroundColor: Colors.red);
        
      }
    } catch (e) {
      
      Get.snackbar('error', 'An error occured', backgroundColor: Colors.red);
      logger.e('$e');
    } finally {
      isProcessing(false);
    }
  }

  Future fetchSignatoryTransactions(int kittyId) async {
    isLoading(true);
    try {
      var response = await apiProvider.request(
          url: "${ApiUrls.SIGNATORYTRANSACTIONS}?kitty_id=$kittyId",
          method: Method.GET,
          params: {"kitty_id": kittyId});
      if (response.data['status'] ?? false) {
        final returnedData = response.data['data']['items'] as List;
        signatoryTransactions.value = returnedData
            .map((item) => SignatoryTransaction.fromJson(item))
            .toList();
      } else {
        Get.snackbar(" an error occured", "${response.data['message']}", backgroundColor: Colors.red);
      }
    } catch (e) {
      logger.e(e);
    } finally {
      isLoading(false);
    }
  }
}
