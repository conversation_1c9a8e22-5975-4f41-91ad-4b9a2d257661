import 'package:flutter/material.dart'; 
import 'package:onekitty/utils/responsive_constants.dart';

class TablePaginationFooter extends StatelessWidget {
  final int currentPage;
  final int totalPages;
  final int rowsPerPage;
  final List<int> availableRowsPerPage;
  final Function(int) onRowsPerPageChanged;
  final VoidCallback onRefresh;
  final VoidCallback onPreviousPage;
  final VoidCallback onNextPage;
  final double height;
  final TextStyle? textStyle;

  const TablePaginationFooter({
    super.key,
    required this.currentPage, 
    required this.totalPages,
    required this.rowsPerPage,
    this.availableRowsPerPage = const [5, 10, 50, 100, 200],
    required this.onRowsPerPageChanged,
    required this.onRefresh,
    required this.onPreviousPage,
    required this.onNextPage,
    this.height = 56,
    this.textStyle,
  });

  @override
  Widget build(BuildContext context) {
    final isDesktop = MediaQuery.of(context).size.width >= ResponsiveBreakpoints.desktop;
    
    return Container(
      height: height,
      padding: ResponsiveSpacing.paddingHorizontal,
      child: Row(
        children: [
          IconButton(
            icon: Icon(
              Icons.refresh,
              size: ResponsiveSize.iconSize,
            ),
            onPressed: onRefresh,
            tooltip: 'Refresh',
          ),
          const Spacer(),
          if (isDesktop) ...[
            Text(
              'Rows per page:',
              style: textStyle ?? ResponsiveTextStyles.caption,
            ),
            SizedBox(width: ResponsiveSpacing.sm),
            DropdownButton<int>(
              value: rowsPerPage,
              underline: Container(),
              items: availableRowsPerPage.map((int value) {
                return DropdownMenuItem<int>(
                  value: value,
                  child: Text(
                    value.toString(),
                    style: textStyle ?? ResponsiveTextStyles.body,
                  ),
                );
              }).toList(),
              onChanged: (int? newValue) {
                if (newValue != null) {
                  onRowsPerPageChanged(newValue);
                }
              },
              itemHeight: ResponsiveSize.buttonHeight,
              style: textStyle ?? ResponsiveTextStyles.body,
            ),
            SizedBox(width: ResponsiveSpacing.md),
          ],
          Text(
            'Page $currentPage of $totalPages',
            style: textStyle ?? ResponsiveTextStyles.caption,
          ),
          SizedBox(width: ResponsiveSpacing.sm),
          IconButton(
            icon: Icon(
              Icons.chevron_left,
              size: ResponsiveSize.iconSize,
            ),
            onPressed: currentPage > 1 ? onPreviousPage : null,
          ),
          IconButton(
            icon: Icon(
              Icons.chevron_right,
              size: ResponsiveSize.iconSize,
            ),
            onPressed: currentPage < totalPages ? onNextPage : null,
          ),
        ],
      ),
    );
  }
}
