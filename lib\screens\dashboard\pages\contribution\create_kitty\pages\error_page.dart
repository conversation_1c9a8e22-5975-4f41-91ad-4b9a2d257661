import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage_pro/get_storage_pro.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/helpers/outlined_button.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/screens/bottom_navbar_screens/botton_navigation_section/bottom_nav_section.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/widgets/row_widget.dart';
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/utils/common_strings.dart';
import 'package:onekitty/utils/custom_button.dart';
import 'package:onekitty/utils/size_config.dart';
import 'package:url_launcher/url_launcher.dart';

class ErrorStepperPage extends StatefulWidget {
  const ErrorStepperPage({super.key});

  @override
  State<ErrorStepperPage> createState() => _ErrorStepperPageState();
}

class _ErrorStepperPageState extends State<ErrorStepperPage> {
  final box = GetStorage();
  TextEditingController linkController = TextEditingController();
  TextEditingController whatsappNoController = TextEditingController();
  final formkey = GlobalKey<FormState>();
  final formkey2 = GlobalKey<FormState>();
  final ContributeController singleKitty = Get.find();
  final KittyController kittyController = Get.find();

  @override
  void initState() {
    box.read("whatsapplink");
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 30),
      child: Form(
        key: formkey,
        child: SingleChildScrollView(
          child: Column(
            children: [
              Image.asset("assets/images/Error states.png"),
              Text(
                "Oops! WhatsApp link trouble",
                style: context.titleText,
                textAlign: TextAlign.center,
              ),
              const SizedBox(
                height: 10,
              ),
              const Text(
                "Don't worry, we can fix it! Here are some solutions:",
                textAlign: TextAlign.center,
              ),
              const SizedBox(
                height: 10,
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "Whatsapp group link(optional)",
                    style: Theme.of(context)
                        .textTheme
                        .titleLarge
                        ?.copyWith(fontWeight: FontWeight.bold, fontSize: 18),
                  ),
                  const SizedBox(
                    height: 15,
                  ),
                  CustomTextField(
                    controller: linkController,
                    hintText: box.read("whatsapplink"),
                    validator: (value) {
                      // RegExp regex = RegExp(
                      //     r'(^(https?:\/\/)?chat\.whatsapp\.com\/(?:invite\/)?([a-zA-Z0-9_-]{22})$)');
                      if (value!.isEmpty) {
                        return "Enter whatsapp invite link";
                      }
                      // else if (regex.hasMatch(value)) {
                      //   return "Enter a valid whatsapp link";
                      // }
                      else {
                        return null;
                      }
                    },
                  ),
                  const Text("Confirm or enter a new group link above"),
                ],
              ),
              const SizedBox(
                height: 15,
              ),
              Obx(
                () => CustomKtButton(
                  btnText: "Connect to WhatsApp",
                  isLoading: kittyController.isLinkloading.isTrue,
                  onPress: () async {
                    if (formkey.currentState!.validate()) {
                      var res = await kittyController.joinGroup(
                          id: kittyController.kittCreated.value.iD ?? 0,
                          context: context,
                          link: linkController.text.trim());
                      if (res) {
                        Get.offNamed(NavRoutes.myKittiescontribtionScreen);
                      }
                    }
                  },
                  text: '',
                  onPressed: () {},
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              Row(
                children: [
                  Expanded(
                      child: Divider(
                    thickness: 1,
                    color: AppColors.slate.withOpacity(1),
                  )),
                  Text(
                    " OR ",
                    style: context.labelLarge?.copyWith(
                        color: AppColors.greyTextColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 17),
                  ),
                  Expanded(
                      child: Divider(
                    thickness: 1,
                    color: AppColors.slate.withOpacity(1),
                  ))
                ],
              ),
              SingleLineRow(
                text: "Contact OneKitty support",
                popup: KtStrings.whatsAppJoinMessage,
              ),
              const Text(
                "Send a WhatsApp message with the link for an update",
              ),
              const SizedBox(
                height: 15,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextButton(
                      onPressed: () {
                        showModalBottomSheet(
                            context: context,
                            builder: (context) => buildBottom());
                      },
                      child: const Text("Skip")),
                  FullWidthOutlinedButton(
                    buttonText: "Send Message",
                    press: () async {
                      if (formkey.currentState!.validate()) {
                        final Uri url = Uri.parse(
                            "https://wa.me/${kittyController.whatsappnumber.value}?text=Please attach my kitty with this group link: ${linkController.text.trim()}\nKitty link:${singleKitty.urlKit.string}");
                        if (!await launchUrl(url)) {
                          ToastUtils.showErrorToast(
                              context, "Could not launch the url", "Error");
                        }
                      }
                    },
                    borderRadius: 25,
                  ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget buildBottom() {
    //final kittyCreated = singleKitty.kittGoten;
    return Container(
      height: SizeConfig.screenHeight * 0.4,
      margin: const EdgeInsets.symmetric(vertical: 20, horizontal: 20),
      decoration: const BoxDecoration(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20), topRight: Radius.circular(20))),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          Row(
            children: [
              Text(
                "Title:",
                style: context.titleText?.copyWith(color: AppColors.stackBlue),
              ),
              const SizedBox(
                width: 4,
              ),
              Text(
                kittyController.kittCreated.value.title ?? "",
                //kittyCreated.value.title ?? "",
                style: context.dividerTextLarge,
              )
            ],
          ),
          const SizedBox(
            height: 12,
          ),
          Row(
            children: [
              Text(
                "Description:",
                style: context.titleText?.copyWith(color: AppColors.stackBlue),
              ),
              const SizedBox(
                width: 4,
              ),
              Expanded(
                child: Text(
                  kittyController.kittCreated.value.description ?? "",
                  overflow: TextOverflow.fade,
                  //kittyCreated.value.description ?? "",
                  style: context.dividerTextLarge,
                ),
              )
            ],
          ),
          Row(
            children: [
              Text(
                "Kitty Url:",
                style: context.titleText?.copyWith(color: AppColors.stackBlue),
              ),
              const SizedBox(
                width: 4,
              ),
              SelectableText(
                kittyController.urlKit.string,
                showCursor: true,
              )
            ],
          ),
          //Add Kitty Url that can be copied
          const SizedBox(
            height: 10,
          ),
          ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                Get.to(() => BottomNavSection());
              },
              child: const Text("Go to Home"))
        ],
      ),
    );
  }
}
