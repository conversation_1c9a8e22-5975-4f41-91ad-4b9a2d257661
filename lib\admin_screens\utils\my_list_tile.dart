import 'package:flutter/material.dart';

class MyListTile extends StatelessWidget {
  final String? title;
  final String? subtitle;
  final IconData? leading;
  final Widget? trailing;
  final Function()? onTap;
  const MyListTile(
      {super.key,
      this.title,
      this.subtitle,
      this.leading,
      this.trailing,
      this.onTap});

  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: onTap,
      leading: Icon(leading),
      trailing: trailing,
      title: Text(title ?? ''),
      subtitle: Text(subtitle ?? ''),
    );
  }
}
