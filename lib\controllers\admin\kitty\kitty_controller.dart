import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/custom_logger.dart';
import 'package:onekitty/services/http_service.dart';

import '../../../models/user_kitties_model.dart';


class KittyAdminController extends GetxController implements GetxService {
  final kitties = <Kitty>[].obs;
  final logger = Logger(filter: CustomLogFilter());
  final HttpService apiProvider = Get.find();
  final RxBool isLoading = false.obs;

  final currentPage = 0.obs;
  final size = 15.obs;
  final chamaId = ''.obs;
  final frequency = ''.obs;
  final kittyId = ''.obs;
  final search = ''.obs;
  final startDate = ''.obs;
  final endDate = ''.obs;

  Future fetchKitties(int page) async {
    try {
      isLoading(true);
      var response = await apiProvider.request(
        method: Method.GET,
        url:
            "${ApiUrls.getAllKitties}?page=$page&size=${size.value}&search=${search.value}&chama_id=${chamaId.value}&frequency=${frequency.value}&kitty_id=${kittyId.value}&start_date=${startDate.value}&end_date=${endDate.value}",
      );
      if (response.data['status'] ?? false) {
        kitties((response.data['data']['results']['items'] as List)
            .map((e) => Kitty.fromJson(e['kitty']))
            .toList());

        //   currentPage.value = response.data['data']['page'];
        //   maxPage.value = response.data['data']['total_pages'];
        //   totalPages.value = response.data['data']['total_pages'];
        //   isLast.value = response.data['data']['last'];
        //   isFirst.value = response.data['data']['first'];
        // } else {
        throw Exception('Failed to load Kitties');
      }
      print(response.data);
    } catch (e) {
      logger.e('Error fetching Kitties: $e');
    } finally {
      isLoading(false);
    }
  }
}
