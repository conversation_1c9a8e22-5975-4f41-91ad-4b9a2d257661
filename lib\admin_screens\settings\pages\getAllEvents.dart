import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/admin/events/events_admin_controller.dart';
import 'package:onekitty/controllers/events/view_single_event.dart';
import 'package:onekitty/models/events/events_model.dart';
import 'package:onekitty/screens/dashboard/pages/events/view_single_event_organizer.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';

class GetAllEvents extends StatefulWidget {
  const GetAllEvents({super.key});

  @override
  State<GetAllEvents> createState() => _GetAllEventsState();
}

class _GetAllEventsState extends State<GetAllEvents> {
final controller = Get.put(EventsAdminController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('All Events'), actions: [
        Obx(() => controller.isGeneratingExcel.value
            ? const CircularProgressIndicator()
            : IconButton(
                onPressed: () => controller.generateExcel(),
                icon: const FaIcon(FontAwesomeIcons.fileExcel)))
      ]),
      body: const AllEvents(),
    );
  }
}

Future<Uint8List> generateEventPdf(Event event) async {
  final pdf = pw.Document();
  final font = await PdfGoogleFonts.notoSansRegular();
  pdf.addPage(pw.Page(
      pageFormat: PdfPageFormat.a4,
      build: (pw.Context context) {
        return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
                pw.Center(
                child: pw.Text(
                  event.title,
                  style: pw.TextStyle(
                  font: font,
                  fontSize: 24,
                  fontWeight: pw.FontWeight.bold,
                  decoration: pw.TextDecoration.underline
                  ),
                ),
                ),
              
              pw.SizedBox(height: 12),
              pw.Text('Description: ${event.description}', style: pw.TextStyle(font: font)),
              pw.SizedBox(height: 8),
              pw.Text('Description: ${event.description}'),
              pw.SizedBox(height: 8),
              pw.Text('Start Date: ${event.startDate?.toLocal() ?? 'N/A'}'),
              pw.SizedBox(height: 8),
              pw.Text('End Date: ${event.endDate?.toLocal() ?? 'N/A'}'),
              pw.SizedBox(height: 8),
              pw.Text('Location: ${event.latitude},${event.longitude}'),
              pw.SizedBox(height: 8),
              pw.Text('Location Tip: ${event.locationTip}'),
              pw.SizedBox(height: 8),
              pw.Text('Status: ${event.status.toString()}'),
              pw.SizedBox(height: 8),
              pw.Text('Category: ${event.category?.title ?? 'N/A'}'),
              pw.SizedBox(height: 8),
              pw.Text('Order Number: ${event.orderNum ?? 'N/A'}'),
            ]);
      }));

  return pdf.save();
}


class AllEvents extends StatefulWidget {
  const AllEvents({super.key});

  @override
  State<AllEvents> createState() => _AllEventsState();
}

class _AllEventsState extends State<AllEvents> {
  final controller = Get.isRegistered<EventsAdminController>()
      ? Get.find<EventsAdminController>()
      : Get.put(EventsAdminController());
  @override
  void initState() {
    super.initState();
    controller.fetchAllEventsWithoutPagination();
  }

 @override
  Widget build(BuildContext context) {
      

  final Rx<int?> selectedIndex = Rx<int?>(null);

  void _onReorder(int oldIndex, int newIndex) {
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final item = controller.allEvents.removeAt(oldIndex);
      controller.allEvents.insert(newIndex, item);
      final event = controller.allEvents[newIndex];
      event.orderNum = newIndex;
      controller.editEvent(event.toJson());
    });
  }

    return Row(
        children: [
          Expanded(child: Obx(() {
            if (controller.error.value != '') {
              return const Center(child: Text('Something went wrong'));
            }
            if (controller.isLoading.value) {
              return Center(
                  child: Column(mainAxisSize: MainAxisSize.min, children: [
                const CircularProgressIndicator(),
                Text(
                    'Loading Events... ${controller.steps} / ${controller.totalSteps}')
              ]));
            }

            if (controller.allEvents.isEmpty) {
              return const Center(child: Text('No Chamas found'));
            }

            return ReorderableListView(onReorder: _onReorder, children: [
              for (int index = 0; index < controller.allEvents.length; index++)
                Card(
                    key: ValueKey(controller.allEvents[index]),
                    margin:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: ListTile(
                        title: Text(
                            controller.allEvents[index].toJson()['title'] ??
                                'Unnamed Event'),
                        subtitle: Text(
                            'Members: ${controller.allEvents[index].toJson()['memberCount'] ?? 0}'),
                        onTap: () {
                          if (MediaQuery.sizeOf(context).width > 600) {
                            selectedIndex.value = index;
                          } else {
                            showDialog(
                              context: context,
                              builder: (context) => Dialog(
                                child: Container(
                                  width: MediaQuery.sizeOf(context).width * 0.9,
                                  height:
                                      MediaQuery.sizeOf(context).height * 0.8,
                                  padding: const EdgeInsets.all(16),
                                  color: Colors.white,
                                  child: PdfPreview(
                                    build: (format) => generateEventPdf(
                                        controller.allEvents[index]),
                                    allowPrinting: true,
                                    allowSharing: true,
                                    initialPageFormat: PdfPageFormat.a4,
                                    pdfFileName:
                                        '${controller.allEvents[index].title}.pdf',
                                  ),
                                ),
                              ),
                            );
                          }
                        }))
            ]);
          })),
          if (MediaQuery.sizeOf(context).width > 600)
            Expanded(
              child: Column(
                children: [
                   Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [ selectedIndex.value == null ? const SizedBox() : IconButton( 
                      onPressed: (){
                           Get.put(ViewSingleEventController())
                .event(controller.allEvents[selectedIndex.value ?? 0]);
            Get.to(
                () => ViewSingleEventOrganizer(
                      eventmodel: MyEventsModel(
                          event: controller
                              .allEvents[selectedIndex.value ?? 0]),
                    ),
                transition: Transition.rightToLeft);
     
                      },
                      icon: const Icon(Icons.settings))],
                  ),
                  Expanded(
                    child: Obx(
                      () => selectedIndex.value == null
                          ? Container()
                          : PdfPreview(
                              build: (format) => generateEventPdf(controller
                                  .allEvents[selectedIndex.value ?? 0]),
                              initialPageFormat: PdfPageFormat.a4,
                              pdfFileName:
                                  '${controller.allEvents[selectedIndex.value ?? 0].title}.pdf',
                              allowPrinting: true,
                              allowSharing: true,
                            ),
                    ),
                  ),
                ],
              ),
            )
        ],
      );
  }
}