import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:get_storage_pro/get_storage_pro.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/models/admin/reconciliation.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/http_service.dart';

class ReconController extends GetxController implements GetxService {
  final HttpService apiProvider = Get.find();
  final box = Get.find<GetStorage>();
  final logger = Get.find<Logger>();
  RxString apiMessage = ''.obs;
  RxBool isloading = false.obs;
  RxBool getStatus = false.obs;
  RxList<Reconciliation> reconciliation = <Reconciliation>[].obs;

  getReconciliationRecords(
      {int? page, int? size, DateTime? from, DateTime? to}) async {
    isloading(true);
    update();
    try {
      var res = await apiProvider.request(
        url:
            "${ApiUrls.getReconciliationRecords}?page=$page&size=$size&start-date=${from ?? ''}&end-date=${to ?? ''}",
        method: Method.GET,
      );
      if (res.data["items"] != null) {
        for (var element in res.data["items"]) {
          reconciliation.add(Reconciliation.fromJson(element));
        }
      }
      update();
      return true;
    } catch (e) {
      logger.e(e);
      isloading(false);
      return false;
    }
  }
}
