// To parse this JSON data, do
//
//     final reconciliation = reconciliationFromJson(jsonString);

import 'dart:convert';

Reconciliation reconciliationFromJson(String str) =>
    Reconciliation.fromJson(json.decode(str));

String reconciliationToJson(Reconciliation data) => json.encode(data.toJson());

class Reconciliation {
  int? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;
  TransactionStaged? transactionStaged;
  int? transactionStagedId;
  Transaction? transaction;
  int? transactionId;
  double? transactionAmount;
  String? typeInOut;
  double? ecosystemInternalBalance;
  double? totalActualBalance;
  double? actualMinusEcosystem;
  double? tanda220429;
  double? tanda220429Working;
  double? tanda220429Utility;
  double? sasapay5055;
  double? sasapay5055Working;
  num? sasapay5055Utility;
  double? sasapay555550;
  double? sasapay555550Working;
  num? sasapay555550Utility;
  num? bankDeclared;
  String? description;
  String? errorMessage;

  Reconciliation({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.transactionStaged,
    this.transactionStagedId,
    this.transaction,
    this.transactionId,
    this.transactionAmount,
    this.typeInOut,
    this.ecosystemInternalBalance,
    this.totalActualBalance,
    this.actualMinusEcosystem,
    this.tanda220429,
    this.tanda220429Working,
    this.tanda220429Utility,
    this.sasapay5055,
    this.sasapay5055Working,
    this.sasapay5055Utility,
    this.sasapay555550,
    this.sasapay555550Working,
    this.sasapay555550Utility,
    this.bankDeclared,
    this.description,
    this.errorMessage,
  });

  Reconciliation copyWith({
    int? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    TransactionStaged? transactionStaged,
    int? transactionStagedId,
    Transaction? transaction,
    int? transactionId,
    double? transactionAmount,
    String? typeInOut,
    double? ecosystemInternalBalance,
    double? totalActualBalance,
    double? actualMinusEcosystem,
    double? tanda220429,
    double? tanda220429Working,
    double? tanda220429Utility,
    double? sasapay5055,
    double? sasapay5055Working,
    num? sasapay5055Utility,
    double? sasapay555550,
    double? sasapay555550Working,
    num? sasapay555550Utility,
    num? bankDeclared,
    String? description,
    String? errorMessage,
  }) =>
      Reconciliation(
        id: id ?? this.id,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        deletedAt: deletedAt ?? this.deletedAt,
        transactionStaged: transactionStaged ?? this.transactionStaged,
        transactionStagedId: transactionStagedId ?? this.transactionStagedId,
        transaction: transaction ?? this.transaction,
        transactionId: transactionId ?? this.transactionId,
        transactionAmount: transactionAmount ?? this.transactionAmount,
        typeInOut: typeInOut ?? this.typeInOut,
        ecosystemInternalBalance:
            ecosystemInternalBalance ?? this.ecosystemInternalBalance,
        totalActualBalance: totalActualBalance ?? this.totalActualBalance,
        actualMinusEcosystem: actualMinusEcosystem ?? this.actualMinusEcosystem,
        tanda220429: tanda220429 ?? this.tanda220429,
        tanda220429Working: tanda220429Working ?? this.tanda220429Working,
        tanda220429Utility: tanda220429Utility ?? this.tanda220429Utility,
        sasapay5055: sasapay5055 ?? this.sasapay5055,
        sasapay5055Working: sasapay5055Working ?? this.sasapay5055Working,
        sasapay5055Utility: sasapay5055Utility ?? this.sasapay5055Utility,
        sasapay555550: sasapay555550 ?? this.sasapay555550,
        sasapay555550Working: sasapay555550Working ?? this.sasapay555550Working,
        sasapay555550Utility: sasapay555550Utility ?? this.sasapay555550Utility,
        bankDeclared: bankDeclared ?? this.bankDeclared,
        description: description ?? this.description,
        errorMessage: errorMessage ?? this.errorMessage,
      );

  factory Reconciliation.fromJson(Map<String, dynamic> json) => Reconciliation(
        id: json["ID"],
        createdAt: json["CreatedAt"] == null
            ? null
            : DateTime.parse(json["CreatedAt"]),
        updatedAt: json["UpdatedAt"] == null
            ? null
            : DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        transactionStaged: json["transaction_staged"] == null
            ? null
            : TransactionStaged.fromJson(json["transaction_staged"]),
        transactionStagedId: json["transaction_staged_id"],
        transaction: json["transaction"] == null
            ? null
            : Transaction.fromJson(json["transaction"]),
        transactionId: json["transaction_id"],
        transactionAmount: json["transaction_amount"]?.toDouble(),
        typeInOut: json["type_in_out"],
        ecosystemInternalBalance:
            json["ecosystem_internal_balance"]?.toDouble(),
        totalActualBalance: json["total_actual_balance"]?.toDouble(),
        actualMinusEcosystem: json["actual_minus_ecosystem"]?.toDouble(),
        tanda220429: json["tanda_220429"]?.toDouble(),
        tanda220429Working: json["tanda_220429_working"]?.toDouble(),
        tanda220429Utility: json["tanda_220429_utility"]?.toDouble(),
        sasapay5055: json["sasapay_5055"]?.toDouble(),
        sasapay5055Working: json["sasapay_5055_working"]?.toDouble(),
        sasapay5055Utility: json["sasapay_5055_utility"],
        sasapay555550: json["sasapay_555550"]?.toDouble(),
        sasapay555550Working: json["sasapay_555550_working"]?.toDouble(),
        sasapay555550Utility: json["sasapay_555550_utility"],
        bankDeclared: json["bank_declared"],
        description: json["description"],
        errorMessage: json["error_message"],
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "transaction_staged": transactionStaged?.toJson(),
        "transaction_staged_id": transactionStagedId,
        "transaction": transaction?.toJson(),
        "transaction_id": transactionId,
        "transaction_amount": transactionAmount,
        "type_in_out": typeInOut,
        "ecosystem_internal_balance": ecosystemInternalBalance,
        "total_actual_balance": totalActualBalance,
        "actual_minus_ecosystem": actualMinusEcosystem,
        "tanda_220429": tanda220429,
        "tanda_220429_working": tanda220429Working,
        "tanda_220429_utility": tanda220429Utility,
        "sasapay_5055": sasapay5055,
        "sasapay_5055_working": sasapay5055Working,
        "sasapay_5055_utility": sasapay5055Utility,
        "sasapay_555550": sasapay555550,
        "sasapay_555550_working": sasapay555550Working,
        "sasapay_555550_utility": sasapay555550Utility,
        "bank_declared": bankDeclared,
        "description": description,
        "error_message": errorMessage,
      };
}

class Transaction {
  int? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;
  String? merchantCode;
  String? accountNumber;
  String? accountNumberRef;
  String? paymentRef;
  String? firstName;
  String? secondName;
  String? fullName;
  String? transactionCode;
  String? transactionCodeOther;
  String? internalId;
  String? phoneNumber;
  int? kittyId;
  num? kittyBalance;
  String? currencyCode;
  String? checkoutRequestId;
  String? channelCode;
  String? transactionDate;
  num? amount;
  String? transAmount;
  String? typeInOut;
  String? status;
  String? extra;
  String? transactionType;
  String? transactionCategory;
  String? description;
  String? product;
  int? metadataId;
  bool? showNumber;
  bool? showNames;

  Transaction({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.merchantCode,
    this.accountNumber,
    this.accountNumberRef,
    this.paymentRef,
    this.firstName,
    this.secondName,
    this.fullName,
    this.transactionCode,
    this.transactionCodeOther,
    this.internalId,
    this.phoneNumber,
    this.kittyId,
    this.kittyBalance,
    this.currencyCode,
    this.checkoutRequestId,
    this.channelCode,
    this.transactionDate,
    this.amount,
    this.transAmount,
    this.typeInOut,
    this.status,
    this.extra,
    this.transactionType,
    this.transactionCategory,
    this.description,
    this.product,
    this.metadataId,
    this.showNumber,
    this.showNames,
  });

  Transaction copyWith({
    int? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? merchantCode,
    String? accountNumber,
    String? accountNumberRef,
    String? paymentRef,
    String? firstName,
    String? secondName,
    String? fullName,
    String? transactionCode,
    String? transactionCodeOther,
    String? internalId,
    String? phoneNumber,
    int? kittyId,
    num? kittyBalance,
    String? currencyCode,
    String? checkoutRequestId,
    String? channelCode,
    String? transactionDate,
    num? amount,
    String? transAmount,
    String? typeInOut,
    String? status,
    String? extra,
    String? transactionType,
    String? transactionCategory,
    String? description,
    String? product,
    int? metadataId,
    bool? showNumber,
    bool? showNames,
  }) =>
      Transaction(
        id: id ?? this.id,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        deletedAt: deletedAt ?? this.deletedAt,
        merchantCode: merchantCode ?? this.merchantCode,
        accountNumber: accountNumber ?? this.accountNumber,
        accountNumberRef: accountNumberRef ?? this.accountNumberRef,
        paymentRef: paymentRef ?? this.paymentRef,
        firstName: firstName ?? this.firstName,
        secondName: secondName ?? this.secondName,
        fullName: fullName ?? this.fullName,
        transactionCode: transactionCode ?? this.transactionCode,
        transactionCodeOther: transactionCodeOther ?? this.transactionCodeOther,
        internalId: internalId ?? this.internalId,
        phoneNumber: phoneNumber ?? this.phoneNumber,
        kittyId: kittyId ?? this.kittyId,
        kittyBalance: kittyBalance ?? this.kittyBalance,
        currencyCode: currencyCode ?? this.currencyCode,
        checkoutRequestId: checkoutRequestId ?? this.checkoutRequestId,
        channelCode: channelCode ?? this.channelCode,
        transactionDate: transactionDate ?? this.transactionDate,
        amount: amount ?? this.amount,
        transAmount: transAmount ?? this.transAmount,
        typeInOut: typeInOut ?? this.typeInOut,
        status: status ?? this.status,
        extra: extra ?? this.extra,
        transactionType: transactionType ?? this.transactionType,
        transactionCategory: transactionCategory ?? this.transactionCategory,
        description: description ?? this.description,
        product: product ?? this.product,
        metadataId: metadataId ?? this.metadataId,
        showNumber: showNumber ?? this.showNumber,
        showNames: showNames ?? this.showNames,
      );

  factory Transaction.fromJson(Map<String, dynamic> json) => Transaction(
        id: json["ID"],
        createdAt: json["CreatedAt"] == null
            ? null
            : DateTime.parse(json["CreatedAt"]),
        updatedAt: json["UpdatedAt"] == null
            ? null
            : DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        merchantCode: json["merchant_code"],
        accountNumber: json["account_number"],
        accountNumberRef: json["account_number_ref"],
        paymentRef: json["payment_ref"],
        firstName: json["first_name"],
        secondName: json["second_name"],
        fullName: json["full_name"],
        transactionCode: json["transaction_code"],
        transactionCodeOther: json["transaction_code_other"],
        internalId: json["internal_id"],
        phoneNumber: json["phone_number"],
        kittyId: json["kitty_id"],
        kittyBalance: json["kitty_balance"],
        currencyCode: json["currency_code"],
        checkoutRequestId: json["checkout_request_id"],
        channelCode: json["channel_code"],
        transactionDate: json["transaction_date"],
        amount: json["amount"],
        transAmount: json["trans_amount"],
        typeInOut: json["type_in_out"],
        status: json["status"],
        extra: json["extra"],
        transactionType: json["transaction_type"],
        transactionCategory: json["transaction_category"],
        description: json["description"],
        product: json["product"],
        metadataId: json["metadata_id"],
        showNumber: json["show_number"],
        showNames: json["show_names"],
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "merchant_code": merchantCode,
        "account_number": accountNumber,
        "account_number_ref": accountNumberRef,
        "payment_ref": paymentRef,
        "first_name": firstName,
        "second_name": secondName,
        "full_name": fullName,
        "transaction_code": transactionCode,
        "transaction_code_other": transactionCodeOther,
        "internal_id": internalId,
        "phone_number": phoneNumber,
        "kitty_id": kittyId,
        "kitty_balance": kittyBalance,
        "currency_code": currencyCode,
        "checkout_request_id": checkoutRequestId,
        "channel_code": channelCode,
        "transaction_date": transactionDate,
        "amount": amount,
        "trans_amount": transAmount,
        "type_in_out": typeInOut,
        "status": status,
        "extra": extra,
        "transaction_type": transactionType,
        "transaction_category": transactionCategory,
        "description": description,
        "product": product,
        "metadata_id": metadataId,
        "show_number": showNumber,
        "show_names": showNames,
      };
}

class TransactionStaged {
  int? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  dynamic deletedAt;
  String? merchantCode;
  String? accountNumber;
  String? accountNumberRef;
  String? description;
  String? payerEmail;
  String? firstName;
  String? secondName;
  String? fullName;
  String? transactionDate;
  String? transactionCode;
  String? transactionCodeOther;
  String? apiTransactionRef;
  String? internalId;
  String? phoneNumber;
  int? kittyId;
  num? kittyBalance;
  int? userId;
  String? paymentRequestId;
  String? checkoutRequestId;
  String? extra;
  String? resultCode;
  String? resultDesc;
  String? responseCode;
  String? responseDescription;
  String? channelCode;
  num? amount;
  String? transAmount;
  String? paymentRef;
  num? totalCharges;
  num? thirdPartyCharges;
  num? orgCharges;
  num? tarrifRate;
  String? currencyCode;
  String? status;
  String? bankConfirmationStatus;
  String? transactionType;
  String? transactionCategory;
  String? product;
  String? typeInOut;
  int? metadataId;
  bool? showNumber;
  bool? showNames;

  TransactionStaged({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.merchantCode,
    this.accountNumber,
    this.accountNumberRef,
    this.description,
    this.payerEmail,
    this.firstName,
    this.secondName,
    this.fullName,
    this.transactionDate,
    this.transactionCode,
    this.transactionCodeOther,
    this.apiTransactionRef,
    this.internalId,
    this.phoneNumber,
    this.kittyId,
    this.kittyBalance,
    this.userId,
    this.paymentRequestId,
    this.checkoutRequestId,
    this.extra,
    this.resultCode,
    this.resultDesc,
    this.responseCode,
    this.responseDescription,
    this.channelCode,
    this.amount,
    this.transAmount,
    this.paymentRef,
    this.totalCharges,
    this.thirdPartyCharges,
    this.orgCharges,
    this.tarrifRate,
    this.currencyCode,
    this.status,
    this.bankConfirmationStatus,
    this.transactionType,
    this.transactionCategory,
    this.product,
    this.typeInOut,
    this.metadataId,
    this.showNumber,
    this.showNames,
  });

  TransactionStaged copyWith({
    int? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    dynamic deletedAt,
    String? merchantCode,
    String? accountNumber,
    String? accountNumberRef,
    String? description,
    String? payerEmail,
    String? firstName,
    String? secondName,
    String? fullName,
    String? transactionDate,
    String? transactionCode,
    String? transactionCodeOther,
    String? apiTransactionRef,
    String? internalId,
    String? phoneNumber,
    int? kittyId,
    num? kittyBalance,
    int? userId,
    String? paymentRequestId,
    String? checkoutRequestId,
    String? extra,
    String? resultCode,
    String? resultDesc,
    String? responseCode,
    String? responseDescription,
    String? channelCode,
    num? amount,
    String? transAmount,
    String? paymentRef,
    num? totalCharges,
    num? thirdPartyCharges,
    num? orgCharges,
    num? tarrifRate,
    String? currencyCode,
    String? status,
    String? bankConfirmationStatus,
    String? transactionType,
    String? transactionCategory,
    String? product,
    String? typeInOut,
    int? metadataId,
    bool? showNumber,
    bool? showNames,
  }) =>
      TransactionStaged(
        id: id ?? this.id,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        deletedAt: deletedAt ?? this.deletedAt,
        merchantCode: merchantCode ?? this.merchantCode,
        accountNumber: accountNumber ?? this.accountNumber,
        accountNumberRef: accountNumberRef ?? this.accountNumberRef,
        description: description ?? this.description,
        payerEmail: payerEmail ?? this.payerEmail,
        firstName: firstName ?? this.firstName,
        secondName: secondName ?? this.secondName,
        fullName: fullName ?? this.fullName,
        transactionDate: transactionDate ?? this.transactionDate,
        transactionCode: transactionCode ?? this.transactionCode,
        transactionCodeOther: transactionCodeOther ?? this.transactionCodeOther,
        apiTransactionRef: apiTransactionRef ?? this.apiTransactionRef,
        internalId: internalId ?? this.internalId,
        phoneNumber: phoneNumber ?? this.phoneNumber,
        kittyId: kittyId ?? this.kittyId,
        kittyBalance: kittyBalance ?? this.kittyBalance,
        userId: userId ?? this.userId,
        paymentRequestId: paymentRequestId ?? this.paymentRequestId,
        checkoutRequestId: checkoutRequestId ?? this.checkoutRequestId,
        extra: extra ?? this.extra,
        resultCode: resultCode ?? this.resultCode,
        resultDesc: resultDesc ?? this.resultDesc,
        responseCode: responseCode ?? this.responseCode,
        responseDescription: responseDescription ?? this.responseDescription,
        channelCode: channelCode ?? this.channelCode,
        amount: amount ?? this.amount,
        transAmount: transAmount ?? this.transAmount,
        paymentRef: paymentRef ?? this.paymentRef,
        totalCharges: totalCharges ?? this.totalCharges,
        thirdPartyCharges: thirdPartyCharges ?? this.thirdPartyCharges,
        orgCharges: orgCharges ?? this.orgCharges,
        tarrifRate: tarrifRate ?? this.tarrifRate,
        currencyCode: currencyCode ?? this.currencyCode,
        status: status ?? this.status,
        bankConfirmationStatus:
            bankConfirmationStatus ?? this.bankConfirmationStatus,
        transactionType: transactionType ?? this.transactionType,
        transactionCategory: transactionCategory ?? this.transactionCategory,
        product: product ?? this.product,
        typeInOut: typeInOut ?? this.typeInOut,
        metadataId: metadataId ?? this.metadataId,
        showNumber: showNumber ?? this.showNumber,
        showNames: showNames ?? this.showNames,
      );

  factory TransactionStaged.fromJson(Map<String, dynamic> json) =>
      TransactionStaged(
        id: json["ID"],
        createdAt: json["CreatedAt"] == null
            ? null
            : DateTime.parse(json["CreatedAt"]),
        updatedAt: json["UpdatedAt"] == null
            ? null
            : DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        merchantCode: json["merchant_code"],
        accountNumber: json["account_number"],
        accountNumberRef: json["account_number_ref"],
        description: json["description"],
        payerEmail: json["payer_email"],
        firstName: json["first_name"],
        secondName: json["second_name"],
        fullName: json["full_name"],
        transactionDate: json["transaction_date"],
        transactionCode: json["transaction_code"],
        transactionCodeOther: json["transaction_code_other"],
        apiTransactionRef: json["api_transaction_ref"],
        internalId: json["internal_id"],
        phoneNumber: json["phone_number"],
        kittyId: json["kitty_id"],
        kittyBalance: json["kitty_balance"],
        userId: json["user_id"],
        paymentRequestId: json["payment_request_id"],
        checkoutRequestId: json["checkout_request_id"],
        extra: json["extra"],
        resultCode: json["result_code"],
        resultDesc: json["result_desc"],
        responseCode: json["response_code"],
        responseDescription: json["response_description"],
        channelCode: json["channel_code"],
        amount: json["amount"],
        transAmount: json["trans_amount"],
        paymentRef: json["payment_ref"],
        totalCharges: json["total_charges"],
        thirdPartyCharges: json["third_party_charges"],
        orgCharges: json["org_charges"],
        tarrifRate: json["tarrif_rate"],
        currencyCode: json["currency_code"],
        status: json["status"],
        bankConfirmationStatus: json["bank_confirmation_status"],
        transactionType: json["transaction_type"],
        transactionCategory: json["transaction_category"],
        product: json["product"],
        typeInOut: json["type_in_out"],
        metadataId: json["metadata_id"],
        showNumber: json["show_number"],
        showNames: json["show_names"],
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "merchant_code": merchantCode,
        "account_number": accountNumber,
        "account_number_ref": accountNumberRef,
        "description": description,
        "payer_email": payerEmail,
        "first_name": firstName,
        "second_name": secondName,
        "full_name": fullName,
        "transaction_date": transactionDate,
        "transaction_code": transactionCode,
        "transaction_code_other": transactionCodeOther,
        "api_transaction_ref": apiTransactionRef,
        "internal_id": internalId,
        "phone_number": phoneNumber,
        "kitty_id": kittyId,
        "kitty_balance": kittyBalance,
        "user_id": userId,
        "payment_request_id": paymentRequestId,
        "checkout_request_id": checkoutRequestId,
        "extra": extra,
        "result_code": resultCode,
        "result_desc": resultDesc,
        "response_code": responseCode,
        "response_description": responseDescription,
        "channel_code": channelCode,
        "amount": amount,
        "trans_amount": transAmount,
        "payment_ref": paymentRef,
        "total_charges": totalCharges,
        "third_party_charges": thirdPartyCharges,
        "org_charges": orgCharges,
        "tarrif_rate": tarrifRate,
        "currency_code": currencyCode,
        "status": status,
        "bank_confirmation_status": bankConfirmationStatus,
        "transaction_type": transactionType,
        "transaction_category": transactionCategory,
        "product": product,
        "type_in_out": typeInOut,
        "metadata_id": metadataId,
        "show_number": showNumber,
        "show_names": showNames,
      };
}
