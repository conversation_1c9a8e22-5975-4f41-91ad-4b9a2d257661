import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:logger/logger.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:onekitty/configs/payment_channels.dart';
import 'package:onekitty/screens/dashboard/pages/transfers/controllers/transfer_controller.dart';
import 'package:onekitty/screens/dashboard/pages/transfers/models/transfer_type.dart';
import 'package:onekitty/screens/dashboard/pages/transfers/services/transfer_service.dart';
import 'package:onekitty/screens/dashboard/pages/transfers/views/screens/transfer_page.dart';

import 'transfer_page_integration_test.mocks.dart';

@GenerateMocks([TransferService, GetStorage, Logger])
void main() {
  group('Transfer Page Integration Tests', () {
    late MockTransferService mockTransferService;
    late MockGetStorage mockStorage;
    late MockLogger mockLogger;

    setUp(() {
      // Initialize GetX
      Get.testMode = true;
      
      // Create mocks
      mockTransferService = MockTransferService();
      mockStorage = MockGetStorage();
      mockLogger = MockLogger();
      
      // Register mocks with GetX
      Get.put<TransferService>(mockTransferService);
      Get.put<GetStorage>(mockStorage);
      Get.put<Logger>(mockLogger);
      Get.put<PaymentChannel>(PaymentChannel());
      
      // Setup mock responses
      when(mockStorage.read(any)).thenReturn(null);
    });

    tearDown(() {
      Get.reset();
    });

    testWidgets('should switch tabs and clear previous data', (WidgetTester tester) async {
      // Arrange
      final config = TransferPageConfig(
        transferType: TransferType.event,
        entityId: 1,
      );

      await tester.pumpWidget(
        GetMaterialApp(
          home: TransferPage(config: config),
        ),
      );

      await tester.pumpAndSettle();

      // Find the controller
      final controller = Get.find<TransferController>(
        tag: '${config.transferType.name}_${config.entityId}',
      );

      // Act - Enter data in mobile money tab
      final phoneField = find.byType(TextFormField).first;
      await tester.enterText(phoneField, '**********');
      await tester.pumpAndSettle();

      // Verify data is entered
      expect(controller.phoneController.text, contains('**********'));

      // Switch to paybill tab
      final paybillTab = find.text('Paybill');
      await tester.tap(paybillTab);
      await tester.pumpAndSettle();

      // Assert - Phone data should be cleared
      expect(controller.phoneController.text, isEmpty);
      expect(controller.currentPage.value, equals(1));
    });

    testWidgets('should validate current tab data only', (WidgetTester tester) async {
      // Arrange
      final config = TransferPageConfig(
        transferType: TransferType.event,
        entityId: 1,
      );

      await tester.pumpWidget(
        GetMaterialApp(
          home: TransferPage(config: config),
        ),
      );

      await tester.pumpAndSettle();

      // Switch to paybill tab
      final paybillTab = find.text('Paybill');
      await tester.tap(paybillTab);
      await tester.pumpAndSettle();

      // Try to transfer without filling paybill data
      final transferButton = find.text('transfer');
      await tester.tap(transferButton);
      await tester.pumpAndSettle();

      // Should show validation error for paybill
      expect(find.text('please_enter_paybill_number'), findsOneWidget);
    });

    testWidgets('should handle transfer with correct tab data', (WidgetTester tester) async {
      // Arrange
      final config = TransferPageConfig(
        transferType: TransferType.event,
        entityId: 1,
      );

      // Mock successful transfer
      when(mockTransferService.initiateTransfer(
        config: anyNamed('config'),
        request: anyNamed('request'),
      )).thenAnswer((_) async => TransferResult(
        success: true,
        message: 'Transfer initiated',
        data: {'amount': 1000},
      ));

      await tester.pumpWidget(
        GetMaterialApp(
          home: TransferPage(config: config),
        ),
      );

      await tester.pumpAndSettle();

      // Fill amount
      final amountField = find.byType(TextFormField).first;
      await tester.enterText(amountField, '1000');
      await tester.pumpAndSettle();

      // Fill phone number in mobile money tab
      final phoneField = find.byType(TextFormField).last;
      await tester.enterText(phoneField, '**********');
      await tester.pumpAndSettle();

      // Tap transfer button
      final transferButton = find.text('transfer');
      await tester.tap(transferButton);
      await tester.pumpAndSettle();

      // Verify transfer service was called
      verify(mockTransferService.initiateTransfer(
        config: anyNamed('config'),
        request: anyNamed('request'),
      )).called(1);
    });

    testWidgets('should reset form after successful transfer', (WidgetTester tester) async {
      // Arrange
      final config = TransferPageConfig(
        transferType: TransferType.event,
        entityId: 1,
      );

      // Mock successful transfer and confirmation
      when(mockTransferService.initiateTransfer(
        config: anyNamed('config'),
        request: anyNamed('request'),
      )).thenAnswer((_) async => TransferResult(
        success: true,
        message: 'Transfer initiated',
        data: {'amount': 1000},
      ));

      when(mockTransferService.confirmTransfer(
        config: anyNamed('config'),
        transferData: anyNamed('transferData'),
      )).thenAnswer((_) async => TransferResult(
        success: true,
        message: 'Transfer confirmed',
        data: {},
      ));

      await tester.pumpWidget(
        GetMaterialApp(
          home: TransferPage(config: config),
        ),
      );

      await tester.pumpAndSettle();

      final controller = Get.find<TransferController>(
        tag: '${config.transferType.name}_${config.entityId}',
      );

      // Fill form data
      controller.amountController.text = '1000';
      controller.phoneController.text = '**********';
      controller.reasonController.text = 'Test transfer';

      // Simulate successful confirmation
      await controller.confirmTransfer();

      // Assert - Form should be reset
      expect(controller.amountController.text, isEmpty);
      expect(controller.phoneController.text, isEmpty);
      expect(controller.reasonController.text, isEmpty);
      expect(controller.currentPage.value, equals(0));
    });

    testWidgets('should prevent cross-tab data contamination', (WidgetTester tester) async {
      // Arrange
      final config = TransferPageConfig(
        transferType: TransferType.event,
        entityId: 1,
      );

      await tester.pumpWidget(
        GetMaterialApp(
          home: TransferPage(config: config),
        ),
      );

      await tester.pumpAndSettle();

      final controller = Get.find<TransferController>(
        tag: '${config.transferType.name}_${config.entityId}',
      );

      // Set data in mobile money tab
      controller.phoneController.text = '**********';
      
      // Switch to paybill tab and set data
      controller.tabController.animateTo(1);
      await tester.pumpAndSettle();
      controller.paybillController.text = '123456';
      controller.accountNumberController.text = 'ACC123';
      
      // Switch to till tab and set data
      controller.tabController.animateTo(2);
      await tester.pumpAndSettle();
      controller.tillNumberController.text = '789012';

      // Switch back to mobile money tab
      controller.tabController.animateTo(0);
      await tester.pumpAndSettle();

      // Assert - Only mobile money data should be preserved
      expect(controller.phoneController.text, isEmpty); // Cleared when switched away
      expect(controller.paybillController.text, isEmpty); // Cleared when switched away
      expect(controller.accountNumberController.text, isEmpty);
      expect(controller.tillNumberController.text, isEmpty);
    });
  });
}

class TransferPageConfig {
  final TransferType transferType;
  final int entityId;
  final bool isPenaltyTransfer;

  TransferPageConfig({
    required this.transferType,
    required this.entityId,
    this.isPenaltyTransfer = false,
  });
}

class TransferResult {
  final bool success;
  final String message;
  final Map<String, dynamic> data;

  TransferResult({
    required this.success,
    required this.message,
    required this.data,
  });
}
