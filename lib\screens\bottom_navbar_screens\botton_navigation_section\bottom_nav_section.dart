import 'package:flutter/material.dart'; 
import 'package:onekitty/admin_screens/admin_screen.dart'; 

// ignore: must_be_immutable
class BottomNavSection extends StatelessWidget {
  BottomNavSection({super.key});

  final GlobalKey<NavigatorState> navigatorKey = GlobalKey();

  @override
  @override
  Widget build(BuildContext context) {
    return  const AdminScreen();
      /* Obx(() {
        final currentIndex =
            Get.find<BottomNavController>().currentIndex.value;
        return IndexedStack(
          index: currentIndex,
          children: [
            const HomeScreen(),
            const MyKittiesScreen(),
            // CrtChama(),
            // CommingSoon(),
            const AllChamaScreen(),
            const EventsPage(),
          ],
        );
      }),*/
      // bottomNavigationBar: _buildBottomBar(context),
    
  }

}
