import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/admin/dashboard_controller.dart';
import 'package:onekitty/utils/asset_urls.dart';

class AdminDrawer extends StatelessWidget {
  const AdminDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(DashboardController());
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundImage: AssetImage(AssetUrl.logo3),
                ),
                const SizedBox(height: 10),
                if (MediaQuery.of(context).size.width < 600 ||
                    MediaQuery.of(context).size.width > 720)
                  const Text(
                    'Admin Panel',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                    ),
                  ),
              ],
            ),
          ),
          MyDrawerListTile(
            icon: (Icons.dashboard),
            label: ('Chama Dashboard'),
            onTap: () {
              controller.pageNo(0);
            },
          ),
          MyDrawerListTile(
            icon: Icons.people,
            label: 'Events Dashboard',
            onTap: () {
              controller.pageNo(1);
            },
          ),
          MyDrawerListTile(
            icon: Icons.attach_money,
            label: 'Kitty Dashboard',
            onTap: () {
              controller.pageNo(3);
            },
          ),
          MyDrawerListTile(
            icon: Icons.settings,
            label: 'Settings',
            onTap: () {
              controller.pageNo(2);
            },
          ),
          const Divider(),
          MyDrawerListTile(
            icon: Icons.exit_to_app,
            label: 'Logout',
            onTap: () {
              controller.logOutUser();
            },
          ),
        ],
      ),
    );
  }
}

class MyDrawerListTile extends StatelessWidget {
  final String label;
  final IconData icon;
  final Function()? onTap;
  const MyDrawerListTile(
      {super.key, required this.label, required this.icon, this.onTap});

  @override
  Widget build(BuildContext context) {
    if (MediaQuery.of(context).size.width > 600 &&
        MediaQuery.of(context).size.width < 720) {
      return Tooltip(
        message: label,
        child: ListTile(leading: Icon(icon), onTap: onTap),
      );
    } else if (MediaQuery.of(context).size.width >= 720) {
      return ListTile(leading: Icon(icon), title: Text(label), onTap: onTap);
    } else if (MediaQuery.of(context).size.width <= 600) {
      return ListTile(leading: Icon(icon), title: Text(label), onTap: onTap);
    } else {
      return const SizedBox.shrink();
    }
  }
}
