// ignore_for_file: must_be_immutable
import 'package:fl_country_code_picker/fl_country_code_picker.dart';
import 'package:flutter/material.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/widgets/date_picker.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/widgets/row_widget.dart';
import 'package:onekitty/screens/widgets/payment_radio.dart';
import 'package:onekitty/utils/common_strings.dart';

class MobileMobile extends StatefulWidget {
  String benPhone;
  final TextEditingController date;
  final TextEditingController time;
  final Function(PhoneNumber)? onInputChanged;
  final PaymentChannelsBuilder paymentChannelsBuilder;
  MobileMobile(
      {super.key,
      this.benPhone = "",
      required this.date,
      required this.time,
      required this.paymentChannelsBuilder,
      required this.onInputChanged});

  @override
  State<MobileMobile> createState() => _MobileMobileState();
}

class _MobileMobileState extends State<MobileMobile> {
  int selectedValue = 0;
  final countryPicker = const FlCountryCodePicker();
  TextEditingController phoneController = TextEditingController();
  CountryCode? countryCode;

  PhoneNumber num = PhoneNumber(isoCode: 'KE');

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          widget.paymentChannelsBuilder,
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Text("Beneficiary Phone Number",
                style: context.titleText
                    ?.copyWith(fontWeight: FontWeight.bold, fontSize: 15)),
          ),
          InternationalPhoneNumberInput(
            onInputChanged: widget.onInputChanged,
            onInputValidated: (bool value) {},
            selectorConfig: const SelectorConfig(
              selectorType: PhoneInputSelectorType.BOTTOM_SHEET,
              useBottomSheetSafeArea: true,
            ),
            ignoreBlank: false,
            autoValidateMode: AutovalidateMode.disabled,
            selectorTextStyle: const TextStyle(color: Colors.black),
            initialValue: num,
            textFieldController: phoneController,
            formatInput: true,
            keyboardType: const TextInputType.numberWithOptions(
                signed: true, decimal: true),
            inputBorder: const OutlineInputBorder(),
            onSaved: (PhoneNumber number) {},
          ),
          const Text("Who should receive the contribution at the end"),
          SingleLineRow(
            text: "Expected contribution end date",
            popup: KtStrings.endDateInfo,
          ),
          DatePicker(
            date: widget.date,
            time: widget.time,
            isAllow: true,
          ),
        ],
      ),
    );
  }

  Widget buildRadio(String image, String text, int value) {
    return Column(
      children: [
        Radio(
            value: value,
            groupValue: selectedValue,
            onChanged: (value) {
              setState(() {
                selectedValue = 1;
              });
            }),
        Image.asset(image),
        Text(text)
      ],
    );
  }
}
