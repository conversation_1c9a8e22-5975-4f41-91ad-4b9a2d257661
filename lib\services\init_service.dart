import 'package:fast_cached_network_image/fast_cached_network_image.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/instance_manager.dart';
import 'package:get_storage_pro/get_storage_pro.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/controllers/admin-transactions/reconciliations_controller.dart';
import 'package:onekitty/controllers/admin/chama/chama_admin_controller.dart';
import 'package:onekitty/controllers/auth_controller.dart';
import 'package:onekitty/controllers/events/controllers.dart';
import 'package:onekitty/controllers/events/create_event_controller.dart';
import 'package:onekitty/controllers/events/edit_event_controller.dart';
import 'package:onekitty/controllers/events/edit_ticket_controller.dart';
import 'package:onekitty/controllers/events/events_controller.dart';
import 'package:onekitty/controllers/events/invite_page_controller.dart';
import 'package:onekitty/controllers/events/invite_users_controller.dart';
import 'package:onekitty/controllers/events/payments_page_controller.dart';
import 'package:onekitty/controllers/events/signatory_controller.dart';
import 'package:onekitty/controllers/events/transfer_page_controller.dart';
import 'package:onekitty/controllers/events/verify_ticket_controller.dart';
import 'package:onekitty/controllers/events/view_single_event.dart';
import 'package:onekitty/controllers/events/vieweventcontroller.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/helpers/loader_easy.dart';
import 'package:onekitty/screens/dashboard/pages/events/create_event/time_and_location.dart';
import 'package:onekitty/services/analytics.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/custom_logger.dart';
import 'package:onekitty/services/http_service.dart';
import 'package:onekitty/utils/cache_keys.dart';
import 'package:path_provider/path_provider.dart';

servicesInitialize() async {
  await GetStorage.init();
  // await Firebase.initializeApp();
  await AnalyticsEngine.init();
  await getBaseUrl();
  Loader.configLoader();
  Get.lazyPut(() => GetStorage(), fenix: true);
  await initializeFastCachedNetworkImage();
  Get.lazyPut(() => Logger(filter: CustomLogFilter()), fenix: true);
  Get.put(
    HttpService(),
  );
  HttpService httpService = Get.find();
  // httpService.();
  Get.put(AuthenticationController());
  Get.lazyPut(() => UserKittyController(), fenix: true);
  // Get.put(ContributeController());
  // Get.put(BulkSMSController());
  // Get.put(BottomNavController());
  // Get.put(KittyController());
  // Get.put(ChamaController());
  Get.put(() => GlobalControllers());
  Get.lazyPut(() => KittyController(), fenix: true);
  Get.put(() => Eventcontroller());
  Get.lazyPut(() => CreateEventController(), fenix: true);
  Get.lazyPut(() => TimeAndLocationController(), fenix: true);
  Get.lazyPut(() => EditTicketController(), fenix: true);
  Get.lazyPut(() => EditEventController(), fenix: true);
  Get.lazyPut(() => ViewSingleEventController(), fenix: true);
  Get.lazyPut(() => PaymentsController(), fenix: true);
  Get.lazyPut(() => InvitePageController(), fenix: true);
  Get.lazyPut(() => VerifyTicketController(), fenix: true);
  Get.lazyPut(() => TransferPageController(), fenix: true);
  Get.lazyPut(() => SignatoryTransactionController(), fenix: true);
  Get.lazyPut(() => ViewEventController(), fenix: true);
  Get.lazyPut(() => InviteUsersController(), fenix: true);
  Get.put(() => ChamaAdminController());
  Get.lazyPut(() => Eventcontroller());
  Get.lazyPut(() => DataController());
  Get.lazyPut(() => ReconController());
  initInappWebview();
}

initializeFastCachedNetworkImage() async {
  if (kIsWeb) {
    await FastCachedImageConfig.init(clearCacheAfter: const Duration(days: 7));
  } else {
    String storageLocation = (await getApplicationDocumentsDirectory()).path;
    await FastCachedImageConfig.init(
        subDir: storageLocation, clearCacheAfter: const Duration(days: 30));
  }
}

Future getBaseUrl() async {
  debugPrint("------Getting url-------");
  bool maintenance = false;
  final remoteConfig = FirebaseRemoteConfig.instance;
  await remoteConfig.setDefaults({
    "base_url_dev": ApiUrls.BASE_URL_DEV,
    "base_url": ApiUrls.BASE_URL_LIVE,
    "maintainance": maintenance
  });
  await remoteConfig.setConfigSettings(RemoteConfigSettings(
    fetchTimeout: const Duration(minutes: 1),
    minimumFetchInterval: const Duration(minutes: 15),
  ));
  // Fetch remote config values
  await remoteConfig.fetch();
  await remoteConfig.activate();
  String baseurlDev = remoteConfig.getString("base_url_dev");
  String baseurlProd = remoteConfig.getString("base_url");
  ApiUrls.BASE_URL_DEV = baseurlDev;
  ApiUrls.BASE_URL_LIVE = baseurlProd;
  maintenance = remoteConfig.getBool("maintainance");
  GetStorage().write(CacheKeys.maintainance, maintenance);
  debugPrint("===========URL FETCHED============");
}

initInappWebview() async {
  if (!kIsWeb && defaultTargetPlatform == TargetPlatform.android) {
    await InAppWebViewController.setWebContentsDebuggingEnabled(kDebugMode);
  }
}
