import 'package:fl_country_code_picker/fl_country_code_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage_pro/get_storage_pro.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/models/kitty_payload.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/create_kitty/pages/error_page.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/create_kitty/pages/mobile.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/create_kitty/pages/paybill.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/create_kitty/pages/till_page.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/widgets/row_widget.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/widgets/success.dart';
import 'package:onekitty/screens/widgets/payment_radio.dart';
import 'package:flutter_quill/flutter_quill.dart' as q;
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/services/analytics.dart';
import 'package:onekitty/utils/common_strings.dart';
import 'package:onekitty/utils/custom_button.dart';
import 'package:onekitty/utils/datetime/combined_datetime.dart';

class StepOne extends StatefulWidget {
  const StepOne({super.key});

  @override
  State<StepOne> createState() => _StepOneState();
}

class _StepOneState extends State<StepOne> with TickerProviderStateMixin {
  final formKey = GlobalKey<FormState>();
  //q.QuillController descrpcontroller = q.QuillController.basic();
  TextEditingController beneficiaryController = TextEditingController();
  String benPhone = "";
  TextEditingController linkController = TextEditingController();
  TextEditingController kittyNameController = TextEditingController();
  TextEditingController desController = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  TextEditingController accountController = TextEditingController();
  TextEditingController accountrefController = TextEditingController();
  TextEditingController dateController = TextEditingController();
  TextEditingController timeController = TextEditingController();
  TextEditingController refererController = TextEditingController();
  PhoneNumber num = PhoneNumber(isoCode: 'KE');
  String myPhone = "";

  late TabController tabController;
  UserKittyController userkitty = Get.put(UserKittyController());
  q.QuillController quillController = q.QuillController.basic();
  int currentStep = 0;
  bool isCompleted = false;
  bool showEditor = false;
  bool showAddLink = false;
  final countryPicker = const FlCountryCodePicker();
  CountryCode? countryCode;
  List<String> socials = [
    "assets/images/insta.png",
    "assets/images/whatsapp.jpg",
    "assets/images/twitter.png",
    "assets/images/solar_link-bold.png"
  ];
  bool showMoreSettings = false;
  String? selectedChannel = "M-Pesa";
  String? endDate;
  int? kittyId;
  var arg = Get.arguments;
  var params = Get.parameters;
  List tillPaybil = ["Mpesa Paybill", "Buy Goods(Till)"];
  String selectedtillPaybil = "Mpesa Paybill";
  KittyController kittyController = Get.put(KittyController());
  final box = GetStorage();

  setValues() async {
    if (kDebugMode) {}
    if (arg != null) {
      if (arg["isEdit"] == true) {
        String? phone = userkitty.getLocalUser()?.phoneNumber?.substring(3);
        final kittyCreated = kittyController.kittCreated.value;
        String? title = kittyCreated.title;
        kittyId = kittyCreated.iD;
        String? desc = kittyCreated.description;
        DateTime? endDateGot = kittyCreated.endDate;
        String? channelBenef = kittyCreated.beneficiaryChannel;
        String? benef = kittyCreated.beneficiaryAccount;
        kittyNameController.text = title ?? "";
        beneficiaryController.text = benef ?? "";
        selectedChannel = channelBenef?.toLowerCase();
        phoneController.text = phone ?? "";
        endDate = endDateGot!.toIso8601String();
        desController.text = desc ?? "";
        setState(() {});
      }
    }
  }

  @override
  void initState() {
    tabController = TabController(initialIndex: 0, length: 3, vsync: this);

    if (kDebugMode) {}
    setValues();
    phoneController.text =
        userkitty.getLocalUser()?.phoneNumber?.substring(3) ?? "";
    "";

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        minimum: const EdgeInsets.symmetric(vertical: 20),
        child: Column(
          children: [
            Text("Create a Kitty",
                style: Theme.of(context)
                    .textTheme
                    .titleLarge
                    ?.copyWith(fontWeight: FontWeight.bold, fontSize: 22)),
            const SizedBox(
              height: 20,
            ),
            Expanded(
              child: Theme(
                data: Theme.of(context).copyWith(
                    colorScheme:
                        const ColorScheme.light(primary: AppColors.primary)),
                child: Form(
                  key: formKey,
                  child: Stepper(
                    elevation: 0,
                    currentStep: currentStep,
                    type: StepperType.horizontal,
                    steps: getSteps(),
                    onStepContinue: () {
                      final isLastStep = currentStep == getSteps().length - 1;
                      if (isLastStep) {
                      } else {
                        setState(() {
                          currentStep += 1;
                        });
                      }
                    },
                    // onStepTapped: (step) => setState(() {
                    //   currentStep = step;
                    // }),
                    onStepCancel: currentStep == 0
                        ? null
                        : () {
                            setState(() {
                              currentStep -= 1;
                            });
                          },
                    controlsBuilder: (context, details) {
                      final isLastStep = currentStep == getSteps().length - 2;
                      final isLastStep2 = currentStep == getSteps().length - 1;
                      return isLastStep2
                          ? const SizedBox()
                          : Container(
                              height: 50,
                              margin: const EdgeInsets.only(top: 7),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  if (currentStep != 0)
                                    SizedBox(
                                      height: 40,
                                      child: OutlinedButton(
                                          onPressed: details.onStepCancel,
                                          child: const Padding(
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 8.0),
                                            child: Text("Back"),
                                          )),
                                    ),
                                  Obx(
                                    () => CustomKtButton(
                                        width: 100,
                                        height: 40,
                                        isLoading:
                                            kittyController.isloading.isTrue,
                                        onPress: isLastStep
                                            ? () async {
                                                AnalyticsEngine
                                                    .userCreatesKitty();
                                                AnalyticsEngine.analytics
                                                    .setAnalyticsCollectionEnabled(
                                                        true);
                                                await createKitty();
                                              }
                                            : details.onStepContinue,
                                        btnText:
                                            isLastStep ? "Submit" : "Next"),
                                  ),
                                ],
                              ),
                            );
                    },
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Step> getSteps() => [
        Step(
            state: currentStep > 0 ? StepState.complete : StepState.indexed,
            title: const Divider(),
            label: const Text("Kitty"),
            content: buildTextField(context),
            isActive: currentStep >= 0),
        Step(
            state: currentStep > 1 ? StepState.complete : StepState.indexed,
            title: const Divider(),
            label: const Text("Financial"),
            content: buildStepTwo(),
            isActive: currentStep >= 1),
        Step(
            state: currentStep == 2 ? StepState.complete : StepState.indexed,
            title: const Divider(),
            label: const Text("Complete"),
            content: const SucessPage(
              text: 'created',
            ),
            isActive: currentStep >= 2),
      ];

  Widget buildTextField(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Whatsapp group link(optional)",
          style: Theme.of(context)
              .textTheme
              .titleMedium
              ?.copyWith(fontWeight: FontWeight.bold),
        ),
        CustomTextField(
          labelText: "",
          controller: linkController,
          // validator: (p0) {
          //   if (p0!.isEmpty) {
          //     return "Filed cannot be empty";
          //   }
          //   return p0;
          // },
        ),
        Text("Kitty Name",
            style: Theme.of(context)
                .textTheme
                .titleMedium
                ?.copyWith(fontWeight: FontWeight.bold)),
        CustomTextField(
          controller: kittyNameController,
          hintText: "e.g Wedding Contribution",
          labelText: "Title",
          validator: (p0) {
            if (p0!.isEmpty) {
              return "Filed cannot be empty";
            } else if (p0.length < 5) {
              return "Kitty Name must be between 5 and 300";
            }
            return null;
          },
        ),
        Text("Kitty Description",
            style: Theme.of(context)
                .textTheme
                .titleMedium
                ?.copyWith(fontWeight: FontWeight.bold)),
        CustomTextField(
          controller: desController,
          hintText: "e.g purpose of contribution",
          labelText: "Description",
          validator: (p0) {
            // if (p0!.isEmpty) {
            //   return "This field cannot be empty";
            // }
            return null;
          },
        ),
        Text("Referral code(optional)",
            style: Theme.of(context)
                .textTheme
                .titleMedium
                ?.copyWith(fontWeight: FontWeight.bold)),
        CustomTextField(
          controller: refererController,
          hintText: "e.g 55",
          labelText: "",
        ),
        /*
        Text("Kitty Banner (optional)",
            style: Theme.of(context)
                .textTheme
                .titleMedium
                ?.copyWith(fontWeight: FontWeight.bold)),

        //Add banner
        GestureDetector(
          onTap: () async {
            if (!_eventsController.isUploading.value) {
              final result = await FilePicker.platform.pickFiles(
                allowMultiple: false,
                type: FileType.image,
              );

              if (result != null && result.files.isNotEmpty) {
                final file = result.xFiles.first.path;
                kittyController.bannerList.add({"name": file});
                final url = await _eventsController.uploadFile(
                    path: file,
                    fileName:
                        "${DateTime.now().millisecondsSinceEpoch}_${file.split(RegExp(r'[/\\]')).last}");
                kittyController.kittyMedia.add({'url': url, 'type': "image"});
              } else {
                ToastUtils.showToast('Nothing picked');
              }
            } else {
              ToastUtils.showToast(
                  'Please wait for the file to upload before uploading another!');
            }
          },
          child: Container(
            height: 55.h,
            padding: const EdgeInsets.all(8),
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.white70,
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Upload Kitty Banner',
                  style: TextStyle(fontSize: 14.spMin),
                ),
                SizedBox(width: 8.w),
                const Icon(Icons.upload_file),
              ],
            ),
          ),
        ),
        SizedBox(
            height: 100,
            child: Obx(
              () => ListView.builder(
                  shrinkWrap: true,
                  scrollDirection: Axis.horizontal,
                  itemCount: kittyController.bannerList.length,
                  itemBuilder: (context, index) {
                    return Obx(
                      () => Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            Image.file(
                              File(
                                  "${kittyController.bannerList[index]['name']}"),
                              height: 80,
                              width: 80,
                              fit: BoxFit.cover,
                            ),
                            index == kittyController.bannerList.length - 1
                                ? _eventsController.isUploading.value
                                    ? const CircularProgressIndicator.adaptive()
                                    : Positioned(
                                        child: IconButton(
                                        icon: const Icon(Icons.delete_outline),
                                        onPressed: () {
                                          kittyController.bannerList
                                              .removeAt(index);
                                        },
                                      ))
                                : Positioned(
                                    child: IconButton(
                                    icon: const Icon(Icons.delete_outline),
                                    onPressed: () {
                                      kittyController.bannerList
                                          .removeAt(index);
                                    },
                                  ))
                          ],
                        ),
                      ),
                    );
                  }),
            )),
    */
      ],
    );
  }

  Widget buildStepTwo() {
    final screenSize = MediaQuery.of(context).size;
    return Column(
      children: [
        SingleLineRow(
          text: "Phone Number",
          popup: KtStrings.phone,
        ),
        InternationalPhoneNumberInput(
          onInputChanged: (
            num,
          ) {
            setState(() {
              myPhone = num.phoneNumber!;
            });
          },
          onInputValidated: (bool value) {},
          selectorConfig: const SelectorConfig(
            selectorType: PhoneInputSelectorType.BOTTOM_SHEET,
            useBottomSheetSafeArea: true,
          ),
          ignoreBlank: false,
          autoValidateMode: AutovalidateMode.disabled,
          selectorTextStyle: const TextStyle(color: Colors.black),
          initialValue: num,
          textFieldController: phoneController,
          formatInput: true,
          keyboardType: const TextInputType.numberWithOptions(
              signed: true, decimal: true),
          inputBorder: const OutlineInputBorder(),
          onSaved: (PhoneNumber number) {},
        ),
        SingleLineRow(
          text: "Beneficiary payment channel",
          popup: KtStrings.benfChannel,
        ),
        DefaultTabController(
            length: 3,
            child: Column(
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 2, vertical: 5),
                  decoration: BoxDecoration(
                    color: AppColors.slate,
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: TabBar(
                      controller: tabController,
                      physics: const ClampingScrollPhysics(),
                      padding: const EdgeInsets.only(left: 5, right: 5),
                      unselectedLabelColor: Colors.black,
                      labelColor: Theme.of(context).primaryColor,
                      indicatorSize: TabBarIndicatorSize.tab,
                      dividerColor: Colors.transparent,
                      indicator: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          color: Colors.white),
                      tabs: const [
                        Tab(
                          child: Text("Mobile Money"),
                        ),
                        Tab(
                          child: Text("Paybill"),
                        ),
                        Tab(
                          child: Text("Till number"),
                        ),
                      ]),
                ),
                SizedBox(
                  height: screenSize.height * 0.32.h,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Expanded(
                        child: TabBarView(
                          controller: tabController,
                          children: [
                            MobileMobile(
                              benPhone: benPhone,
                              date: dateController,
                              time: timeController,
                              paymentChannelsBuilder: PaymentChannelsBuilder(
                                selectedChannel: selectedChannel ?? "",
                                onChange: (String? value) {
                                  setState(() {
                                    selectedChannel = value;
                                  });
                                },
                              ),
                              onInputChanged: (num) {
                                setState(() {
                                  benPhone = num.phoneNumber!;
                                });
                              },
                            ),
                            PayBill(
                              paybillController: accountController,
                              accountController: accountrefController,
                              dateController: dateController,
                              timeController: timeController,
                            ),
                            TillPage(
                              tillController: accountController,
                              date: dateController,
                              time: timeController,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ))
      ],
    );
  }

  createKitty() async {
    if (formKey.currentState!.validate()) {
      if (dateController.text.trim().isEmpty &&
          timeController.text.trim().isEmpty) {
        ToastUtils.showErrorToast(context, "Kindly pick an end date", "Error");
      }
      int settlementType = 0;
      String account = '';
      String accountref = '';
      String beneficiaryChannel = '63902';
      String beneficiaryPhoneNumber = '';
      String? userId = kittyController.user.value.iD?.toString();
      //var description = jsonEncode(quillController.document.toDelta().toJson());
      if (tabController.index == 1) {
        settlementType = 2;
        if (accountController.text.trim().isEmpty ||
            accountrefController.text.trim().isEmpty) {
          ToastUtils.showErrorToast(
              context, "Enter all required fields for Paybill", "Error");
          return;
        } else {
          account = accountController.text.trim();
          accountref = accountrefController.text.trim();
          beneficiaryPhoneNumber = myPhone.substring(1);
          beneficiaryChannel = beneficiaryChannel;
        }
      } else if (tabController.index == 2) {
        settlementType = 1;
        if (accountController.text.trim().isEmpty) {
          ToastUtils.showErrorToast(context, "Enter Till number", "Error");
          return;
        } else {
          account = accountController.text.trim();
          beneficiaryPhoneNumber = myPhone.substring(1);
          beneficiaryChannel = beneficiaryChannel;
        }
      } else {
        settlementType = 0;
        account = benPhone.substring(1);
        beneficiaryPhoneNumber = benPhone.substring(1);
      }

      // Get the date and time from the text fields
      final date = dateController.text;
      final time = timeController.text;

      // Combine the date and time into a single DateTime object
      final combinedDateTime = combineDateTime(date, time);

      // Format the combined DateTime object according to the backend's expected format
      final formattedDateTime = formatDateTime(combinedDateTime);
      final dateTime = DateTime.tryParse(formattedDateTime);
      // Now you can send the formattedDateTime to the backend
      // Example:
      // backendService.sendData(formattedDateTime);
      CreateKitPayload request = CreateKitPayload(
          id: kittyId,
          whatsappLink: linkController.text.trim(),
          title: kittyNameController.text.trim(),
          //description: description.toString(),
          description: desController.text.trim(),
          beneficiaryChannel: kittyController
              .getNetworkCode(networkTitle: selectedChannel ?? "")
              .toString(),
          beneficiaryAccount: account,
          beneficiaryAccountRef: accountref,
          beneficiaryPhoneNumber: beneficiaryPhoneNumber,
          phoneNumber: myPhone.substring(1),
          endDate: dateTime,
          refererMerchantCode: int.tryParse(refererController.text.trim()),
          userId: userId,
          settlementType: settlementType,
          media: Get.find<ContributeController>()
              .kittyMedia
              .map((e) => Media(url: e.url, type: e.type))
              .toList());

      if (request.description?.isEmpty ?? true) {
        request.description = request.title;
      }
      bool res = await kittyController.createKitty(payload: request);

      if (res) {
        if (!mounted) return;
        Snack.show(res, kittyController.apiMessage.string);
        if (kittyController.whtsappStatus.isFalse) {
          Snack.show(res, kittyController.whatsappApiMessage.string);
          Get.offAndToNamed(NavRoutes.whatsappErrorPage);
        } else if (kittyController.whtsappStatus.isTrue) {
          Snack.show(res, kittyController.whatsappApiMessage.string);
          Navigator.pushAndRemoveUntil(
              context,
              MaterialPageRoute(
                  builder: (context) => SucessPage(
                        text: "created",
                        kittyName: kittyNameController.text.trim(),
                      )),
              (route) => route.isFirst);
        }
      } else {
        if (!mounted) return;
        Snack.show(res, kittyController.apiMessage.string);
      }
    } else {
      ToastUtils.showErrorToast(context, "Fill in all values", "Error");
    }
    box.write("whatsapplink", linkController.text);
    box.write("phoneNumber", phoneController.text);
  }
}

getWhatsappStatus(String whatsapp_status, String text) {
  switch (whatsapp_status.toLowerCase()) {
    case "false":
      return const ErrorStepperPage();
    case "true":
      return SucessPage(text: text);
    default:
      return SucessPage(text: text);
  }
}
