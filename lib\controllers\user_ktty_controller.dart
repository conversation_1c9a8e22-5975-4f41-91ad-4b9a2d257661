import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage_pro/get_storage_pro.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/models/kitty/media_model.dart';
import 'package:onekitty/models/transaction_model.dart';
import 'package:onekitty/models/user_kitties_model.dart';
import 'package:onekitty/models/auth/user_model.dart';
import 'package:onekitty/models/user_transaction_model.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/http_service.dart';

import '../utils/cache_keys.dart';

class UserKittyController extends GetxController implements GetxService {
  final HttpService apiProvider = Get.put(
    HttpService(),
  );
//getting user kitties
  RxBool kittiesLoading = false.obs;
  Rx<UserModelLatest> user = UserModelLatest().obs;

  final box = Get.find<GetStorage>();
  RxString apiMessage = ''.obs;
  final logger = Get.find<Logger>();
  RxBool status = false.obs;

  Rx<UserModelLatest> usermodel = UserModelLatest().obs;
  Rx<MerchantModel> usermerchant = MerchantModel().obs;

  RxList<UserKitty> kitties = <UserKitty>[].obs;
  RxList<Kitty> kittyList = <Kitty>[].obs;
  RxList<MediaModel> media = <MediaModel>[].obs;

//
  RxBool topUploading = false.obs;
  RxMap topUpData = {}.obs;
  RxString apiMessageTopup = ''.obs;

//get user_kitty transactions
  RxBool loadingTransactions = false.obs;
  RxList<Item> alltransactions = <Item>[].obs;
  Rx<UserTransactionModel> results = UserTransactionModel().obs;
  RxBool loadingfiltrTransactions = false.obs;
  RxList<Item> filtrtransactions = <Item>[].obs;
  RxBool isloadingUser = false.obs;
  RxBool loadingMore = false.obs;
  //referKitties
  RxList<Kitty> referkitties = <Kitty>[].obs;
  RxInt totalKitties = 0.obs;
  RxInt totalRefers = 0.obs;
  RxString kittyType = ''.obs;
  Rx<transData> resultsDts = transData().obs;
  RxList<transItem> merchtransactions = <transItem>[].obs;

  RxBool loading = false.obs;

  int per_page = 10;
  RxBool scrollEnd = false.obs;

//get user_all transctions

  @override
  void onInit() {
    getLocalUser();
    controller.addListener(_scrollListener);
    super.onInit();
  }

  ScrollController controller = ScrollController();
  void _scrollListener() async {
    loadingMore(true);
    if (controller.position.pixels >=
        controller.position.maxScrollExtent * 0.9) {
      // if (controller.position.atEdge && controller.position.pixels != 0) {
      await loadMoreKitties();
    }
    loadingMore(false);
  }

  void reset() {
    per_page = 10;
    scrollEnd = false.obs;
  }

  UserModelLatest? getLocalUser() {
    final usr = box.read(CacheKeys.user);
    if (usr != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        user(UserModelLatest.fromJson(usr));
      });
      return user.value;
    } else {
      return null;
    }
  }

  UserKitty? findKittyById(int id) {
    return kitties.firstWhere(
      (kitty) => kitty.kitty?.id == id,
    );
  }

  getUser() async {
    isloadingUser(true);
    try {
      var resp = await apiProvider.request(
          url: "${ApiUrls.getUser}?phone_number=${user.value.phoneNumber}",
          method: Method.GET);

      if (resp.data["status"]) {
        final u = resp.data["data"]["user"];
        usermodel(UserModelLatest.fromJson(u));
        // balance=resp.data["data"]["user"]["balance"];
        logger.log(Level.debug, resp.data);
        if (resp.data["data"]["merchant"] != null) {
          final m = resp.data["data"]["merchant"];
          usermerchant(MerchantModel.fromJson(m));
        }
        box.write(CacheKeys.user, u);
      }
      isloadingUser(false);
      update();
    } catch (e) {
      logger.e(e);
      apiMessage('An error occured');
      update();
      isloadingUser(false);
      throw e;
    }
  }

  getUserkitties({int? page = 0, int? size = 10}) async {
    update();

    try {
      var resp = await apiProvider.request(
          url:
              "${ApiUrls.getUserKitties}?phone_number=${user.value.phoneNumber}&page=$page&size=$size",
          method: Method.GET);
      status(resp.data["status"]);
      apiMessage(resp.data["message"]);
      if (resp.data["status"]) {
        kitties([]);
        totalKitties(resp.data["data"]["kitties_count"]);
        for (var element in resp.data["data"]["user_kitties"] ?? []) {
          kitties.add(UserKitty.fromJson(element));
        }

        if (resp.data["data"]["media"] != null) {
          media([]);
          for (var element in resp.data["data"]["media"]) {
            media.add(MediaModel.fromJson(element));
          }
        }
      }
      kittiesLoading(false);
      update();
    } catch (e) {
      kittiesLoading(false);
      logger.e(e);
      apiMessage('An error occured');
      update();

      throw e;
    }
  }

  Future<void> loadMoreKitties() async {
    if (per_page < totalKitties.value) {
      per_page = (per_page + 10 > totalKitties.value)
          ? totalKitties.value
          : per_page + 10;
      await getUserkitties(
        size: per_page,
      );
      if (per_page == totalKitties.value) {
        scrollEnd.value = true;
      }
    }
  }

  getReferkitties({int? page = 0, int? size = 10}) async {
    kittiesLoading(true);
    update();

    try {
      var resp = await apiProvider.request(
          url:
              "${ApiUrls.getReferKitties}?code=${usermerchant.value.merchantCode}&page=$page&size=$size",
          method: Method.GET);
      status(resp.data["status"]);
      apiMessage(resp.data["message"]);
      if (resp.data["status"]) {
        referkitties([]);
        for (var element in resp.data["data"]["items"] ?? []) {
          referkitties.add(Kitty.fromJson(element));
        }
        if (referkitties.isNotEmpty) {
          totalRefers.value = resp.data["data"]["total"];
        }
      }
      kittiesLoading(false);
      update();
    } catch (e) {
      kittiesLoading(false);
      logger.e(e);
      apiMessage('An error occured');
      update();

      throw e;
    }
  }

  getUserTransactions(
      {required String phoneNo,
      int? page = 0,
      int? size = 20,
      int? kittId}) async {
    loadingTransactions(true);
    update();
    try {
      var resp = await apiProvider.request(
        url:
            "${ApiUrls.getUserAllTransactions}?phone_number=$phoneNo&page=$page&size=$size",
        method: Method.GET,
      );
      if (resp.statusCode == 200) {
        UserTransactionModel Results = UserTransactionModel.fromJson(resp.data);

        alltransactions([]);
        for (var items in resp.data["items"] ?? []) {
          alltransactions.add(Item.fromJson(items));
        }

        Results.items = alltransactions;

        results.value = Results;
      } else {
        alltransactions([]);
      }
      loadingTransactions(false);
      update();
    } catch (e) {
      logger.e(e);
      apiMessage('An error occured');
      loadingTransactions(false);
      update();
    }
  }

  getUserFiltrContributions({
    required String phoneNo,
    int? page = 0,
    int? size = 100,
    String? startDate,
    String? endDate,
    String? code,
    int? kittId,
  }) async {
    update();
    loadingfiltrTransactions(true);
    try {
      update();
      // Common base at this point
      String url = "${ApiUrls.getUserAllTransactions}?phone_number=$phoneNo";

      // Checking if startDate and endDate are both provided
      if (!(startDate?.isEmpty ?? true) && !(endDate?.isEmpty ?? true)) {
        url += "&start-date=$startDate&end-date=$endDate";
      } else if (kittId != null) {
        url += "&kitty_id=$kittId";
      } else if (!(code?.isEmpty ?? true)) {
        url += "&transaction_code=$code";
      }
      var resp = await apiProvider.request(
        url: url,
        method: Method.GET,
      );

      if (resp.statusCode == 200) {
        filtrtransactions([]);

        for (var element in resp.data["items"]) {
          filtrtransactions.add(Item.fromJson(element));
        }
      } else {}
      loadingfiltrTransactions(false);
    } catch (e) {
      loadingfiltrTransactions(false);
      throw e;
    }
  }

  getMerchantTransactions(
      {required int code, int? page = 0, int size = 100, int? kittId}) async {
    loadingTransactions(true);
    update();
    // try {
    var resp = await apiProvider.request(
      url: kittId != null
          ? "${ApiUrls.getMerTransac}?code=$code&page=$page&size=$size&kitty_id=$kittId"
          : "${ApiUrls.getMerTransac}?code=$code&page=$page&size=$size",
      method: Method.GET,
    );
    if (resp.statusCode == 200) {
      Transac Results = Transac.fromJson(resp.data);

      merchtransactions([]);
      for (var items in resp.data["data"]["items"] ?? []) {
        merchtransactions.add(transItem.fromJson(items));
      }
      Results.data = resultsDts.value;
    } else {
      merchtransactions([]);
    }
    loadingTransactions(false);
    update();
    // } catch (e) {
    //   logger.e(e);
    //   apiMessage('An error occured');
    //   loadingTransactions(false);
    //   update();
    // }
  }

  Future<bool> setRefererCode({required SetMrchtDto request}) async {
    loading(true);
    update();

    try {
      var res = await apiProvider.request(
          url: ApiUrls.getCode, method: Method.POST, params: request.toJson());
      apiMessage(res.data["message"]);
      if (res.data["status"]) {
        apiMessage(res.data["message"]);
      } else {
        Get.snackbar(
          "error",
          res.data["message"],
          backgroundColor: Colors.red,
        );
      }
      loading(false);
      return res.data["status"];
    } catch (e) {
      logger.e(e);
      Get.snackbar(
        "error",
        "$e",
        backgroundColor: Colors.amber,
      );
      loading(false);
      apiMessage("An error occured");
      return false;
    }
  }

  String maskString(String input) {
    if (input.length > 9) {
      final int startLength = (input.length - 5) ~/ 2;
      final int endLength = input.length - startLength - 5;
      final String start = input.substring(0, startLength);
      final String end = input.substring(input.length - endLength);
      final String masked = '*' * 5;
      return '$start$masked$end';
    } else {
      return input;
    }
  }

  Future<bool> topup({
    required String phoneNumber,
    required int amount,
    required int channel,
    required int userId,
    String? email,
  }) async {
    topUploading(true);
    try {
      var res = await apiProvider.request(
        url: ApiUrls.topUp,
        method: Method.POST,
        params: {
          "amount": amount,
          "phone_number": phoneNumber,
          "channel_code": channel,
          "user_id": userId,
          "payer_email": email,
        },
      );
      topUploading(false);
      if (res.data["status"]) {
        topUpData(res.data["data"]);
        apiMessageTopup(res.data["message"]);

        return true;
      } else {
        apiMessageTopup(res.data["message"]);

        return false;
      }
    } catch (e) {
      logger.e(e);
      topUploading(false);
      apiMessageTopup('An error occured');

      return false;
    }
  }
}

class DataController extends GetxController {
  Rx<UserKitty> kitty = UserKitty().obs;
}
