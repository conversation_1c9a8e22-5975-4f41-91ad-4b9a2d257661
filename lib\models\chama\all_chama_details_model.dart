// import 'dart:convert';

// AllChamaModel allChamaModelFromJson(String str) =>
//     AllChamaModel.fromJson(json.decode(str));

// String allChamaModelToJson(AllChamaModel data) => json.encode(data.toJson());

// class AllChamaModel {
//   bool? status;
//   String? message;
//   Data? data;

//   AllChamaModel({
//     this.status,
//     this.message,
//     this.data,
//   });

//   factory AllChamaModel.fromJson(Map<String, dynamic> json) => AllChamaModel(
//         status: json["status"],
//         message: json["message"],
//         data: Data.fromJson(json["data"]),
//       );

//   Map<String, dynamic> toJson() => {
//         "status": status,
//         "message": message,
//         "data": data!.toJson(),
//       };
// }

// class Data {
//   AllChama? chama;
//   List<Member>? members;
//   int? membersCount;
//   List<NextBeneficiary>? nextBeneficiaries;
//   List<Notification>? notification;
//   int? penaltiesCount;
//   Settings? settings;

//   Data({
//     this.chama,
//     this.members,
//     this.membersCount,
//     this.nextBeneficiaries,
//     this.notification,
//     this.penaltiesCount,
//     this.settings,
//   });

//   factory Data.fromJson(Map<String, dynamic> json) => Data(
//         chama: AllChama.fromJson(json["chama"]),
//         members:
//             List<Member>.from(json["members"].map((x) => Member.fromJson(x))),
//         membersCount: json["members_count"],
//         nextBeneficiaries: List<NextBeneficiary>.from(
//             json["next_beneficiaries"].map((x) => NextBeneficiary.fromJson(x))),
//         notification: List<Notification>.from(
//             json["notification"].map((x) => Notification.fromJson(x))),
//         penaltiesCount: json["penalties_count"],
//         settings: Settings.fromJson(json["settings"]),
//       );

//   Map<String, dynamic> toJson() => {
//         "chama": chama!.toJson(),
//         "members": List<dynamic>.from(members!.map((x) => x.toJson())),
//         "members_count": membersCount,
//         "next_beneficiaries":
//             List<dynamic>.from(nextBeneficiaries!.map((x) => x.toJson())),
//         "notification":
//             List<dynamic>.from(notification!.map((x) => x.toJson())),
//         "penalties_count": penaltiesCount,
//         "settings": settings?.toJson(),
//       };
// }

// class AllChama {
//   int? id;
//   DateTime? createdAt;
//   DateTime? updatedAt;
//   dynamic deletedAt;
//   int? kittyId;
//   String? title;
//   String? username;
//   String? description;
//   String? phoneNumber;
//   String? accountNumber;
//   String? accountNumberRef;
//   int? channel;
//   String? transferMode;
//   int? balance;
//   int? amount;
//   String? email;
//   int? refererCode;
//   DateTime? nextOccurrence;
//   String? frequency;
//   String? status;
//   int? lastReceivedMemberId;
//   dynamic lastReceivedOrder;

//   AllChama({
//     this.id,
//     this.createdAt,
//     this.updatedAt,
//     this.deletedAt,
//     this.kittyId,
//     this.title,
//     this.username,
//     this.description,
//     this.phoneNumber,
//     this.accountNumber,
//     this.accountNumberRef,
//     this.channel,
//     this.transferMode,
//     this.balance,
//     this.amount,
//     this.email,
//     this.refererCode,
//     this.nextOccurrence,
//     this.frequency,
//     this.status,
//     this.lastReceivedMemberId,
//     this.lastReceivedOrder,
//   });

//   factory AllChama.fromJson(Map<String, dynamic> json) => AllChama(
//         id: json["ID"],
//         createdAt: DateTime.parse(json["CreatedAt"]),
//         updatedAt: DateTime.parse(json["UpdatedAt"]),
//         deletedAt: json["DeletedAt"],
//         kittyId: json["kitty_id"],
//         title: json["title"],
//         username: json["username"],
//         description: json["description"],
//         phoneNumber: json["phone_number"],
//         accountNumber: json["account_number"],
//         accountNumberRef: json["account_number_ref"],
//         channel: json["channel"],
//         transferMode: json["transfer_mode"],
//         balance: json["balance"],
//         amount: json["amount"],
//         email: json["email"],
//         refererCode: json["referer_code"],
//         nextOccurrence: DateTime.parse(json["next_occurrence"]),
//         frequency: json["frequency"],
//         status: json["status"],
//         lastReceivedMemberId: json["last_received_member_id"],
//         lastReceivedOrder: json["last_received_order"],
//       );

//   Map<String, dynamic> toJson() => {
//         "ID": id,
//         "CreatedAt": createdAt!.toIso8601String(),
//         "UpdatedAt": updatedAt!.toIso8601String(),
//         "DeletedAt": deletedAt,
//         "kitty_id": kittyId,
//         "title": title,
//         "username": username,
//         "description": description,
//         "phone_number": phoneNumber,
//         "account_number": accountNumber,
//         "account_number_ref": accountNumberRef,
//         "channel": channel,
//         "transfer_mode": transferMode,
//         "balance": balance,
//         "amount": amount,
//         "email": email,
//         "referer_code": refererCode,
//         "next_occurrence": nextOccurrence?.toIso8601String(),
//         "frequency": frequency,
//         "status": status,
//         "last_received_member_id": lastReceivedMemberId,
//         "last_received_order": lastReceivedOrder,
//       };
// }

// class Member {
//   int? id;
//   DateTime? createdAt;
//   DateTime? updatedAt;
//   dynamic deletedAt;
//   dynamic userId;
//   String? profileUrl;
//   String? phoneNumber;
//   String? countryCode;
//   String? firstName;
//   String? secondName;
//   String? role;
//   dynamic beneficiary;
//   int? chamaId;
//   int? receivingOrder;
//   String? status;

//   Member({
//     this.id,
//     this.createdAt,
//     this.updatedAt,
//     this.deletedAt,
//     this.userId,
//     this.profileUrl,
//     this.phoneNumber,
//     this.countryCode,
//     this.firstName,
//     this.secondName,
//     this.role,
//     this.beneficiary,
//     this.chamaId,
//     this.receivingOrder,
//     this.status,
//   });

//   factory Member.fromJson(Map<String, dynamic> json) => Member(
//         id: json["ID"],
//         createdAt: DateTime.parse(json["CreatedAt"]),
//         updatedAt: DateTime.parse(json["UpdatedAt"]),
//         deletedAt: json["DeletedAt"],
//         userId: json["user_id"],
//         profileUrl: json["profile_url"],
//         phoneNumber: json["phone_number"],
//         countryCode: json["country_code"],
//         firstName: json["first_name"],
//         secondName: json["second_name"],
//         role: json["role"],
//         beneficiary: json["beneficiary"],
//         chamaId: json["chama_id"],
//         receivingOrder: json["receiving_order"],
//         status: json["status"],
//       );

//   Map<String, dynamic> toJson() => {
//         "ID": id,
//         "CreatedAt": createdAt!.toIso8601String(),
//         "UpdatedAt": updatedAt!.toIso8601String(),
//         "DeletedAt": deletedAt,
//         "user_id": userId,
//         "profile_url": profileUrl,
//         "phone_number": phoneNumber,
//         "country_code": countryCode,
//         "first_name": firstName,
//         "second_name": secondName,
//         "role": role,
//         "beneficiary": beneficiary,
//         "chama_id": chamaId,
//         "receiving_order": receivingOrder,
//         "status": status,
//       };
// }

// class NextBeneficiary {
//   int? chamaPercentage;
//   Member? member;
//   List<Beneficiary>? beneficiaries;

//   NextBeneficiary({
//     this.chamaPercentage,
//     this.member,
//     this.beneficiaries,
//   });

//   factory NextBeneficiary.fromJson(Map<String, dynamic> json) =>
//       NextBeneficiary(
//         chamaPercentage: json["chama_percentage"],
//         member: Member.fromJson(json["member"]),
//         beneficiaries: List<Beneficiary>.from(
//             json["beneficiaries"].map((x) => Beneficiary.fromJson(x))),
//       );

//   Map<String, dynamic> toJson() => {
//         "chama_percentage": chamaPercentage,
//         "member": member!.toJson(),
//         "beneficiaries":
//             List<dynamic>.from(beneficiaries!.map((x) => x.toJson())),
//       };
// }

// class Beneficiary {
//   int? id;
//   DateTime? createdAt;
//   DateTime? updatedAt;
//   dynamic deletedAt;
//   dynamic userId;
//   int? chamaId;
//   int? memberId;
//   String? transferMode;
//   int? percentage;
//   String? beneficiaryType;
//   String? channelName;
//   int? channel;
//   String? status;
//   String? accountNumber;
//   String? accountNumberRef;

//   Beneficiary({
//     this.id,
//     this.createdAt,
//     this.updatedAt,
//     this.deletedAt,
//     this.userId,
//     this.chamaId,
//     this.memberId,
//     this.transferMode,
//     this.percentage,
//     this.beneficiaryType,
//     this.channelName,
//     this.channel,
//     this.status,
//     this.accountNumber,
//     this.accountNumberRef,
//   });

//   factory Beneficiary.fromJson(Map<String, dynamic> json) => Beneficiary(
//         id: json["ID"],
//         createdAt: DateTime.parse(json["CreatedAt"]),
//         updatedAt: DateTime.parse(json["UpdatedAt"]),
//         deletedAt: json["DeletedAt"],
//         userId: json["user_id"],
//         chamaId: json["chama_id"],
//         memberId: json["member_id"],
//         transferMode: json["transfer_mode"],
//         percentage: json["percentage"],
//         beneficiaryType: json["beneficiary_type"],
//         channelName: json["channel_name"],
//         channel: json["channel"],
//         status: json["status"],
//         accountNumber: json["account_number"],
//         accountNumberRef: json["account_number_ref"],
//       );

//   Map<String, dynamic> toJson() => {
//         "ID": id,
//         "CreatedAt": createdAt!.toIso8601String(),
//         "UpdatedAt": updatedAt!.toIso8601String(),
//         "DeletedAt": deletedAt,
//         "user_id": userId,
//         "chama_id": chamaId,
//         "member_id": memberId,
//         "transfer_mode": transferMode,
//         "percentage": percentage,
//         "beneficiary_type": beneficiaryType,
//         "channel_name": channelName,
//         "channel": channel,
//         "status": status,
//         "account_number": accountNumber,
//         "account_number_ref": accountNumberRef,
//       };
// }

// class Notification {
//   int? id;
//   DateTime? createdAt;
//   DateTime? updatedAt;
//   dynamic deletedAt;
//   int? chamaId;
//   String? phoneNumber;
//   String? jid;
//   String? jidUser;
//   int? jidAgent;
//   int? jidDevice;
//   bool? jidAd;
//   String? jidServer;
//   String? whatsAppLink;
//   String? whatsappGroupName;
//   String? email;
//   String? whatsappStatus;
//   String? whatsappProfile;
//   String? whatsappNumber;

//   Notification({
//     this.id,
//     this.createdAt,
//     this.updatedAt,
//     this.deletedAt,
//     this.chamaId,
//     this.phoneNumber,
//     this.jid,
//     this.jidUser,
//     this.jidAgent,
//     this.jidDevice,
//     this.jidAd,
//     this.jidServer,
//     this.whatsAppLink,
//     this.whatsappGroupName,
//     this.email,
//     this.whatsappStatus,
//     this.whatsappProfile,
//     this.whatsappNumber,
//   });

//   factory Notification.fromJson(Map<String, dynamic> json) => Notification(
//         id: json["ID"],
//         createdAt: DateTime.parse(json["CreatedAt"]),
//         updatedAt: DateTime.parse(json["UpdatedAt"]),
//         deletedAt: json["DeletedAt"],
//         chamaId: json["chama_id"],
//         phoneNumber: json["phone_number"],
//         jid: json["jid"],
//         jidUser: json["jid_user"],
//         jidAgent: json["jid_agent"],
//         jidDevice: json["jid_device"],
//         jidAd: json["jid_ad"],
//         jidServer: json["jid_server"],
//         whatsAppLink: json["whats_app_link"],
//         whatsappGroupName: json["whatsapp_group_name"],
//         email: json["email"],
//         whatsappStatus: json["whatsapp_status"],
//         whatsappProfile: json["whatsapp_profile"],
//         whatsappNumber: json["whatsapp_number"],
//       );

//   Map<String, dynamic> toJson() => {
//         "ID": id,
//         "CreatedAt": createdAt!.toIso8601String(),
//         "UpdatedAt": updatedAt!.toIso8601String(),
//         "DeletedAt": deletedAt,
//         "chama_id": chamaId,
//         "phone_number": phoneNumber,
//         "jid": jid,
//         "jid_user": jidUser,
//         "jid_agent": jidAgent,
//         "jid_device": jidDevice,
//         "jid_ad": jidAd,
//         "jid_server": jidServer,
//         "whats_app_link": whatsAppLink,
//         "whatsapp_group_name": whatsappGroupName,
//         "email": email,
//         "whatsapp_status": whatsappStatus,
//         "whatsapp_profile": whatsappProfile,
//         "whatsapp_number": whatsappNumber,
//       };
// }

// class Settings {
//   int? id;
//   DateTime? createdAt;
//   DateTime? updatedAt;
//   dynamic deletedAt;
//   dynamic signatories;
//   int? chamaId;
//   int? beneficiariesPerCycle;
//   int? beneficiaryPercentage;
//   int? signatureThreshold;
//   dynamic penaltyKittyId;

//   Settings({
//     this.id,
//     this.createdAt,
//     this.updatedAt,
//     this.deletedAt,
//     this.signatories,
//     this.chamaId,
//     this.beneficiariesPerCycle,
//     this.beneficiaryPercentage,
//     this.signatureThreshold,
//     this.penaltyKittyId,
//   });

//   factory Settings.fromJson(Map<String, dynamic> json) => Settings(
//         id: json["ID"],
//         createdAt: DateTime.parse(json["CreatedAt"]),
//         updatedAt: DateTime.parse(json["UpdatedAt"]),
//         deletedAt: json["DeletedAt"],
//         signatories: json["signatories"],
//         chamaId: json["chama_id"],
//         beneficiariesPerCycle: json["beneficiaries_per_cycle"],
//         beneficiaryPercentage: json["beneficiary_percentage"],
//         signatureThreshold: json["signature_threshold"],
//         penaltyKittyId: json["penalty_kitty_id"],
//       );

//   Map<String, dynamic> toJson() => {
//         "ID": id,
//         "CreatedAt": createdAt!.toIso8601String(),
//         "UpdatedAt": updatedAt!.toIso8601String(),
//         "DeletedAt": deletedAt,
//         "signatories": signatories,
//         "chama_id": chamaId,
//         "beneficiaries_per_cycle": beneficiariesPerCycle,
//         "beneficiary_percentage": beneficiaryPercentage,
//         "signature_threshold": signatureThreshold,
//         "penalty_kitty_id": penaltyKittyId,
//       };
// }
