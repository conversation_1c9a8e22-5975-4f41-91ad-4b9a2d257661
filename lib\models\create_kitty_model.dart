// class KittyModel {
//   bool? status;
//   String? message;
//   Data? data;

//   KittyModel({this.status, this.message, this.data});

//   KittyModel.fromJson(Map<String, dynamic> json) {
//     status = json['status'];
//     message = json['message'];
//     data = json['data'] != null ? Data.fromJson(json['data']) : null;
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['status'] = status;
//     data['message'] = message;
//     if (this.data != null) {
//       data['data'] = this.data!.toJson();
//     }
//     return data;
//   }
// }

// class Data {
//   Kitty? kitty;
//   Merchant? merchant;
//   String? url;

//   Data({this.kitty, this.merchant, this.url});

//   Data.fromJson(Map<String, dynamic> json) {
//     kitty = json['kitty'] != null ? Kitty.fromJson(json['kitty']) : null;
//     merchant =
//         json['merchant'] != null ? Merchant.fromJson(json['merchant']) : null;
//     url = json['url'];
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     if (kitty != null) {
//       data['kitty'] = kitty!.toJson();
//     }
//     if (merchant != null) {
//       data['merchant'] = merchant!.toJson();
//     }
//     data['url'] = url;
//     return data;
//   }
// }

// class Kitty {
//   int? iD;
//   String? createdAt;
//   String? updatedAt;
//   String? deletedAt;
//   String? title;
//   String? description;
//   String? beneficiaryAccount;
//   String? beneficiaryChannel;
//   String? beneficiaryPhoneNumber;
//   String? endDate;
//   dynamic limit;
//   int? refererMerchantCode;
//   String? phoneNumber;
//   int? settlementType;

//   Kitty(
//       {this.iD,
//       this.createdAt,
//       this.updatedAt,
//       this.deletedAt,
//       this.title,
//       this.description,
//       this.beneficiaryAccount,
//       this.beneficiaryChannel,
//       this.beneficiaryPhoneNumber,
//       this.endDate,
//       this.limit,
//       this.refererMerchantCode,
//       this.settlementType,
//       this.phoneNumber});

//   Kitty.fromJson(Map<String, dynamic> json) {
//     iD = json['ID'];
//     createdAt = json['CreatedAt'];
//     updatedAt = json['UpdatedAt'];
//     deletedAt = json['DeletedAt'];
//     title = json['title'];
//     description = json['description'];
//     beneficiaryAccount = json['beneficiary_account'];
//     beneficiaryChannel = json['beneficiary_channel'];
//     beneficiaryPhoneNumber = json['beneficiary_phone_number'];
//     endDate = json['end_date'];
//     limit = json['limit'];
//     refererMerchantCode = json['referer_merchant_code'];
//     settlementType = json['settlement_type'];
//     phoneNumber = json['phone_number'];
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['ID'] = iD;
//     data['CreatedAt'] = createdAt;
//     data['UpdatedAt'] = updatedAt;
//     data['DeletedAt'] = deletedAt;
//     data['title'] = title;
//     data['description'] = description;
//     data['beneficiary_account'] = beneficiaryAccount;
//     data['beneficiary_channel'] = beneficiaryChannel;
//     data['beneficiary_phone_number'] = beneficiaryPhoneNumber;
//     data['end_date'] = endDate;
//     data['limit'] = limit;
//     data['referer_merchant_code'] = refererMerchantCode;
//     data['phone_number'] = phoneNumber;
//     data['settlement_type'] = settlementType;
//     return data;
//   }
// }

// class Merchant {
//   int? iD;
//   String? createdAt;
//   String? updatedAt;
//   String? deletedAt;
//   int? userID;
//   String? merchantName;
//   int? merchantCode;
//   double? merchantPercent;

//   Merchant(
//       {this.iD,
//       this.createdAt,
//       this.updatedAt,
//       this.deletedAt,
//       this.userID,
//       this.merchantName,
//       this.merchantCode,
//       this.merchantPercent});

//   Merchant.fromJson(Map<String, dynamic> json) {
//     iD = json['ID'];
//     createdAt = json['CreatedAt'];
//     updatedAt = json['UpdatedAt'];
//     deletedAt = json['DeletedAt'];
//     userID = json['UserID'];
//     merchantName = json['merchant_name'];
//     merchantCode = json['merchant_code'];
//     merchantPercent = json['merchant_percent'];
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['ID'] = iD;
//     data['CreatedAt'] = createdAt;
//     data['UpdatedAt'] = updatedAt;
//     data['DeletedAt'] = deletedAt;
//     data['UserID'] = userID;
//     data['merchant_name'] = merchantName;
//     data['merchant_code'] = merchantCode;
//     data['merchant_percent'] = merchantPercent;
//     return data;
//   }
// }
