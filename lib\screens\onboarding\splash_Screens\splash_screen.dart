import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onekitty/services/auth_manager.dart';
import 'package:get/get.dart';
import '../login_screen.dart' ;
import '../passwd_req_screen.dart' ;

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});
  
  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  // Initialize the AuthenticationManager using GetX.
  final AuthenticationManager _authManager = Get.put(AuthenticationManager());

  @override
  void initState() {
    super.initState();
    
  }
  
  /// This method checks the login status, waits for a short delay, and then
  /// navigates to either the password screen (if logged in) or the login screen.
 Future<bool> _initializeAndNavigate() async {
  _authManager.checkLoginStatus();
  await Future.delayed(const Duration(seconds: 1));  
  return _authManager.isLogged.value;
  // if (_authManager.isLogged.value) {
  //   await passwd_req_screen.loadLibrary();
  //   if (!mounted) return;
  //   Navigator.of(context).pushReplacement(
  //     MaterialPageRoute(
  //       builder: (_) => 
  //     ),
  //   );
  // } else {
  //   await login_screen.loadLibrary();
  //   if (!mounted) return;
  //   Navigator.of(context).pushReplacement(
  //     MaterialPageRoute(
  //       builder: (_) => login_screen.LoginScreen(),
  //     ),
  //   );
  // }
}
 
  @override
  Widget build(BuildContext context) {
    // While initializing, show a centered progress indicator and a message.
    return FutureBuilder(
        future: _initializeAndNavigate(),
        builder: (context, snapshot) {
          if(snapshot.connectionState == ConnectionState.done){
            final _data = snapshot.data!;
            if(_data){
              return const AuthPasswdScreen();
            }else{
              return const LoginScreen();
            }
          }
          return Scaffold(
            body:  Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(child: Image.asset(
                    height: 150, width: 150,
                    'assets/images/logo8.png'),),

                  const CircularProgressIndicator(),
                   SizedBox(height: 20.h),
                  const Text("loading..."),
                   SizedBox(height: 100.h)
                ],
              ),
            ),
          );
        }
      );
    
  }
}
