importScripts('https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js');
importScripts('https://www.gstatic.com/firebasejs/8.10.0/firebase-messaging.js');

// firebase.initializeApp({
//   apiKey: 'YOUR_WEB_API_KEY',
//   authDomain: 'YOUR_AUTH_DOMAIN',
//   projectId: 'YOUR_PROJECT_ID',
//   messagingSenderId: 'YOUR_MESSAGING_SENDER_ID',
//   appId: 'YOUR_WEB_APP_ID',
// });

firebase.initializeApp({
  apiKey: "AIzaSyCRY5TrzLBjnKhL6uBaRqscrsgQmqdf6_E",
  authDomain: "onekitty-345a1.firebaseapp.com",
  projectId: "onekitty-345a1",
  storageBucket: "onekitty-345a1.appspot.com",
  messagingSenderId: "470354413031",
  appId: "1:470354413031:web:e33b18a7f57803160fe942",
  measurementId: "G-KVK6GQ1WFK"
});

const messaging = firebase.messaging();

messaging.onBackgroundMessage((payload) => {
  console.log('Received background message', payload);
});
