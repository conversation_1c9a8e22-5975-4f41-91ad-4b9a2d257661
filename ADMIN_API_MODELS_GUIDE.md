# Admin API Endpoints and Models Guide

## API Endpoints Structure

### Base Configuration
```dart
// lib/services/api_urls.dart
class ApiUrls {
  static const String baseUrl = 'https://your-api-base-url.com/api/';
  
  // Chama Admin Endpoints
  static const String getAllChamasAdmin = 'admin/chamas';
  static const String getChamaDetails = 'admin/chamas/{id}';
  static const String getChamaMembers = 'admin/chamas/{id}/members';
  static const String getChamaTransactions = 'admin/chamas/{id}/transactions';
  static const String getChamaSettings = 'admin/chamas/{id}/settings';
  static const String updateChamaSettings = 'admin/chamas/{id}/settings';
  static const String addBeneficiaryAccount = 'admin/chamas/beneficiary-account';
  
  // Events Admin Endpoints
  static const String GETALLEVENTS = 'admin/events';
  static const String getEventTransactions = 'admin/events/{id}/transactions';
  static const String blockEvent = 'admin/events/{id}/block';
  static const String unblockEvent = 'admin/events/{id}/unblock';
  
  // Kitty Admin Endpoints
  static const String getAllKittiesAdmin = 'admin/kitties';
  static const String getKittyDetails = 'admin/kitties/{id}';
}
```

## Model Structures

### 1. Chama Model
```dart
// lib/models/chama/chama_model.dart
class Chama {
  final int? id;
  final String? name;
  final String? description;
  final double? balance;
  final String? frequency;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final List<ChamaMembers>? members;
  final ChamaSetting? settings;

  Chama({
    this.id,
    this.name,
    this.description,
    this.balance,
    this.frequency,
    this.createdAt,
    this.updatedAt,
    this.members,
    this.settings,
  });

  factory Chama.fromJson(Map<String, dynamic> json) {
    return Chama(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      balance: json['balance']?.toDouble(),
      frequency: json['frequency'],
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at']) 
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.parse(json['updated_at']) 
          : null,
      members: json['members'] != null
          ? (json['members'] as List)
              .map((e) => ChamaMembers.fromJson(e))
              .toList()
          : null,
      settings: json['settings'] != null
          ? ChamaSetting.fromJson(json['settings'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'balance': balance,
      'frequency': frequency,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'members': members?.map((e) => e.toJson()).toList(),
      'settings': settings?.toJson(),
    };
  }
}
```

### 2. Chama Members Model
```dart
// lib/models/chama/chama_members_model.dart
class ChamaMembers {
  final int? id;
  final String? name;
  final String? email;
  final String? phoneNumber;
  final String? status;
  final String? role;
  final double? contribution;
  final DateTime? joinedAt;

  ChamaMembers({
    this.id,
    this.name,
    this.email,
    this.phoneNumber,
    this.status,
    this.role,
    this.contribution,
    this.joinedAt,
  });

  factory ChamaMembers.fromJson(Map<String, dynamic> json) {
    return ChamaMembers(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      phoneNumber: json['phone_number'],
      status: json['status'],
      role: json['role'],
      contribution: json['contribution']?.toDouble(),
      joinedAt: json['joined_at'] != null 
          ? DateTime.parse(json['joined_at']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone_number': phoneNumber,
      'status': status,
      'role': role,
      'contribution': contribution,
      'joined_at': joinedAt?.toIso8601String(),
    };
  }
}
```

### 3. Event Model
```dart
// lib/models/events/events_model.dart
class Event {
  final int? id;
  final String? title;
  final String? description;
  final DateTime? eventDate;
  final String? location;
  final double? ticketPrice;
  final String? status;
  final int? organizerId;
  final DateTime? createdAt;

  Event({
    this.id,
    this.title,
    this.description,
    this.eventDate,
    this.location,
    this.ticketPrice,
    this.status,
    this.organizerId,
    this.createdAt,
  });

  factory Event.fromJson(Map<String, dynamic> json) {
    return Event(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      eventDate: json['event_date'] != null 
          ? DateTime.parse(json['event_date']) 
          : null,
      location: json['location'],
      ticketPrice: json['ticket_price']?.toDouble(),
      status: json['status'],
      organizerId: json['organizer_id'],
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'event_date': eventDate?.toIso8601String(),
      'location': location,
      'ticket_price': ticketPrice,
      'status': status,
      'organizer_id': organizerId,
      'created_at': createdAt?.toIso8601String(),
    };
  }
}
```

### 4. Kitty Model
```dart
// lib/models/user_kitties_model.dart
class Kitty {
  final int? id;
  final String? name;
  final double? targetAmount;
  final double? currentAmount;
  final String? frequency;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? status;

  Kitty({
    this.id,
    this.name,
    this.targetAmount,
    this.currentAmount,
    this.frequency,
    this.startDate,
    this.endDate,
    this.status,
  });

  factory Kitty.fromJson(Map<String, dynamic> json) {
    return Kitty(
      id: json['id'],
      name: json['name'],
      targetAmount: json['target_amount']?.toDouble(),
      currentAmount: json['current_amount']?.toDouble(),
      frequency: json['frequency'],
      startDate: json['start_date'] != null 
          ? DateTime.parse(json['start_date']) 
          : null,
      endDate: json['end_date'] != null 
          ? DateTime.parse(json['end_date']) 
          : null,
      status: json['status'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'target_amount': targetAmount,
      'current_amount': currentAmount,
      'frequency': frequency,
      'start_date': startDate?.toIso8601String(),
      'end_date': endDate?.toIso8601String(),
      'status': status,
    };
  }
}
```

## API Response Structure

### Standard Response Format
```dart
// lib/models/api_response.dart
class ApiResponse<T> {
  final bool status;
  final String message;
  final T? data;
  final Map<String, dynamic>? errors;

  ApiResponse({
    required this.status,
    required this.message,
    this.data,
    this.errors,
  });

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic)? fromJsonT,
  ) {
    return ApiResponse<T>(
      status: json['status'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null && fromJsonT != null 
          ? fromJsonT(json['data']) 
          : json['data'],
      errors: json['errors'],
    );
  }
}
```

### Paginated Response Format
```dart
// lib/models/paginated_response.dart
class PaginatedResponse<T> {
  final List<T> items;
  final int currentPage;
  final int totalPages;
  final int totalItems;
  final bool isFirst;
  final bool isLast;
  final int size;

  PaginatedResponse({
    required this.items,
    required this.currentPage,
    required this.totalPages,
    required this.totalItems,
    required this.isFirst,
    required this.isLast,
    required this.size,
  });

  factory PaginatedResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    return PaginatedResponse<T>(
      items: (json['items'] as List)
          .map((e) => fromJsonT(e as Map<String, dynamic>))
          .toList(),
      currentPage: json['current_page'] ?? 0,
      totalPages: json['total_pages'] ?? 0,
      totalItems: json['total_items'] ?? 0,
      isFirst: json['first'] ?? true,
      isLast: json['last'] ?? false,
      size: json['size'] ?? 15,
    );
  }
}
```

## Error Handling

### Custom Exception Classes
```dart
// lib/services/exceptions.dart
class ApiException implements Exception {
  final String message;
  final int? statusCode;
  final Map<String, dynamic>? errors;

  ApiException({
    required this.message,
    this.statusCode,
    this.errors,
  });

  @override
  String toString() => 'ApiException: $message';
}

class NetworkException implements Exception {
  final String message;

  NetworkException(this.message);

  @override
  String toString() => 'NetworkException: $message';
}
```

This guide provides the complete API structure and model definitions needed to implement the admin system with proper data handling and error management.
