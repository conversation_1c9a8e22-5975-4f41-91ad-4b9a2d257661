import 'dart:convert';

class ChamaBeneficiaryModel {
  final int id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;
  final String title;
  final dynamic userId;
  final int chamaId;
  final int memberId;
  final String transferMode;
  final int percentage;
  final String beneficiaryType;
  final String channelName;

  ChamaBeneficiaryModel({
    this.id = 0,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.title = '',
    this.userId,
    this.chamaId = 0,
    this.memberId = 0,
    this.transferMode = '',
    this.percentage = 0,
    this.beneficiaryType = '',
    this.channelName = '',
  });

  factory ChamaBeneficiaryModel.fromRawJson(String str) =>
      ChamaBeneficiaryModel.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ChamaBeneficiaryModel.fromJson(Map<String, dynamic> json) =>
      ChamaBeneficiaryModel(
        id: json["ID"],
        createdAt: json['CreatedAt'] == null
            ? null
            : DateTime.parse(json["CreatedAt"]),
        updatedAt: json['UpdatedAt'] == null
            ? null
            : DateTime.parse(json["UpdatedAt"]),
        deletedAt: json["DeletedAt"],
        title: json["title"],
        userId: json["user_id"],
        chamaId: json["chama_id"],
        memberId: json["member_id"],
        transferMode: json["transfer_mode"],
        percentage: json["percentage"],
        beneficiaryType: json["beneficiary_type"],
        channelName: json["channel_name"],
      );

  Map<String, dynamic> toJson() => {
        "ID": id,
        "CreatedAt": createdAt?.toIso8601String(),
        "UpdatedAt": updatedAt?.toIso8601String(),
        "DeletedAt": deletedAt,
        "title": title,
        "user_id": userId,
        "chama_id": chamaId,
        "member_id": memberId,
        "transfer_mode": transferMode,
        "percentage": percentage,
        "beneficiary_type": beneficiaryType,
        "channel_name": channelName,
      };
}
