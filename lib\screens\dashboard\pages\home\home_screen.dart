import 'package:fast_cached_network_image/fast_cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/controllers/config.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/connectivity_wrapper.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/models/auth/user_model.dart';
import 'package:onekitty/screens/dashboard/pages/chama/widgets/create_tabs.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/contribution_kitties/widgets/single_kitty_card.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/withdraw/withdraw.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/transactions.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/in_app_browser.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/video_player_widget.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/create_kitty/pages/create_kitty.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/widgets/row_widget.dart';
import 'package:onekitty/utils/size_config.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:upgrader/upgrader.dart';

import '../../../../utils/utils_exports.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  bool hideBal = false;
  String greeting = getGreeting();
  DataController dataController = Get.put(DataController());
  TextEditingController amountController = TextEditingController();
  ContributeController getKittyController = Get.put(ContributeController());
  Logger logger = Get.find();

  final UserKittyController userController = Get.find<UserKittyController>();
  int activeIndex = 0;
  int activeIndex2 = 0;
  final PageController _pageController =
      PageController(initialPage: 1, viewportFraction: 0.6);
  final PageController _page2Controller = PageController(initialPage: 0);
  final RefreshController _refreshController =
      RefreshController(initialRefresh: true);
  void _onRefresh() async {
    try {
      userController.getLocalUser();
      await userController.getUserkitties();
      await userController.getUser();
      configController.getConfig2();

      //verifyVersion();
      _refreshController.refreshCompleted();
    } catch (e) {
      logger.e(e);
      _refreshController.refreshCompleted();
    }
  }

  // verifyVersion() async {
  //   await AppVersionUpdate.checkForUpdates(
  //     appleId: '12212121',
  //     playStoreId: 'ke.co.onekitty',
  //     country: 'KE',
  //   ).then((result) async {
  //     if (result.canUpdate! && mounted) {
  //       GetStorage().write(CacheKeys.canUpdate, true);
  //       // await AppVersionUpdate.showBottomSheetUpdate(context: context, appVersionResult: appVersionResult)
  //       // await AppVersionUpdate.showPageUpdate(context: context, appVersionResult: appVersionResult)
  //       // or use your own widget with information received from AppVersionResult

  //       //##############################################################################################

  //       await AppVersionUpdate.showAlertUpdate(
  //           appVersionResult: result,
  //           context: context,
  //           backgroundColor: Colors.grey[200],
  //           title: 'A newer version is available.',
  //           titleTextStyle: const TextStyle(
  //               color: Colors.black,
  //               fontWeight: FontWeight.w600,
  //               fontSize: 24.0),
  //           content:
  //               'For Better Experience update your Onekitty app to the latest version',
  //           contentTextStyle: const TextStyle(
  //             color: Colors.black,
  //             fontWeight: FontWeight.w400,
  //           ),
  //           updateButtonText: 'UPDATE',
  //           mandatory: false,
  //           cancelButtonText: 'LATER',
  //           cancelButtonStyle: ButtonStyle(
  //               backgroundColor: MaterialStatePropertyAll(AppColors.slate)));
  //     }
  //   });
  // }

  ConfigController configController = Get.put(ConfigController());

  @override
  void initState() {
    _onRefresh();
    configController.getConfig2();
    //verifyVersion();
    super.initState();
  }
  //  void _scrollListener() {
  //   if (_pageController.page != _page2Controller.page) {
  //     _page2Controller.jumpTo(_pageController.position.pixels);
  //   }
  // }
  // @override
  // void initState() {
  //   super.initState();
  //   WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
  //     double minCrollExtent1 = _pageController.position.minScrollExtent;
  //     double maxCrollExtent1 = _pageController.position.maxScrollExtent;
  //     animateToMaxMin(maxCrollExtent1, minCrollExtent1, maxCrollExtent1, 10,
  //         _pageController);
  //   });
  // }

  animateToMaxMin(double max, double min, double direction, int seconds,
      ScrollController scrollController) {
    if (_pageController.hasClients) {
      scrollController
          .animateTo(direction,
              duration: Duration(seconds: seconds), curve: Curves.linear)
          .then((value) {
        direction = direction == max ? min : max;
        animateToMaxMin(max, min, direction, seconds, scrollController);
      });
    }
  }

  UserModelLatest user = UserModelLatest();
  @override
  void dispose() {
    _pageController.dispose();
    _page2Controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var phoneNumber =
        userController.getLocalUser()?.phoneNumber?.replaceRange(6, 9, "***");
    final screenSize = MediaQuery.of(context).size;
    SizeConfig().init(context);
    return UpgradeAlert(
      showIgnore: false,
      showLater: false,
      dialogStyle: UpgradeDialogStyle.cupertino,
      child: ConnectivityCheck(
        child: Scaffold(
          appBar: buildAppBarWithImage(context),
          body: SmartRefresher(
            onRefresh: _onRefresh,
            controller: _refreshController,
            child: Container(
              margin: const EdgeInsets.symmetric(vertical: 7),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Container(
                      margin: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 20),
                      //padding: EdgeInsets.only(right: 5, bottom: 5),
                      decoration: BoxDecoration(
                          color: AppColors.mainPurple,
                          borderRadius: BorderRadius.circular(12),
                          gradient: const LinearGradient(
                              colors: [
                                AppColors.dark,
                                AppColors.blueButtonColor,
                              ],
                              begin: Alignment.topRight,
                              end: Alignment.bottomLeft)),

                      child: Stack(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              children: [
                                const Text(
                                  "Balance",
                                  style: TextStyle(color: AppColors.neutral),
                                ),
                                GetBuilder<UserKittyController>(
                                  builder: (userController) {
                                    if (userController.isloadingUser.isTrue) {
                                      return (const Text("..."));
                                    }
                                    return Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          hideBal
                                              ? FormattedCurrency()
                                                  .getFormattedCurrency(
                                                  userController
                                                      .getLocalUser()
                                                      ?.balance
                                                      ?.toStringAsFixed(2),
                                                )
                                              : "****",
                                          style: context.dividerTextSmall
                                              ?.copyWith(
                                                  fontSize: 20,
                                                  color: Colors.white,
                                                  fontWeight: FontWeight.bold),
                                        ),
                                        IconButton(
                                          onPressed: () {
                                            setState(() {
                                              hideBal = !hideBal;
                                            });
                                          },
                                          icon: Icon(
                                              hideBal
                                                  ? Icons
                                                      .visibility_off_outlined
                                                  : Icons.remove_red_eye,
                                              color: Colors.white
                                                  .withOpacity(0.7)),
                                        )
                                      ],
                                    );
                                  },
                                ),
                                Row(
                                  children: [
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          "${userController.getLocalUser()?.firstName} ${userController.getLocalUser()?.secondName}",
                                          style: context.dividerTextSmall
                                              ?.copyWith(
                                                  color: Colors.white,
                                                  fontWeight: FontWeight.bold),
                                        ),
                                        Text(phoneNumber ?? '',
                                            //"${userController.getLocalUser()?.phoneNumber}",
                                            style: context.dividerTextSmall
                                                ?.copyWith(
                                              color: Colors.white,
                                            ))
                                      ],
                                    ),
                                    const SizedBox(
                                      width: 12,
                                    ),
                                    Visibility(
                                      visible: false,
                                      child: OutlinedButton.icon(
                                          style: OutlinedButton.styleFrom(
                                              side: const BorderSide(
                                                  color: Colors.white)),
                                          onPressed: () {
                                            Get.to(() => const WithdrawPage());
                                          },
                                          icon: const Icon(
                                            Icons.arrow_upward_rounded,
                                            color: Colors.white,
                                          ),
                                          label: Text(
                                            "Withdraw",
                                            style: context.dividerTextLarge
                                                ?.copyWith(color: Colors.white),
                                          )),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          Positioned(
                            bottom: -12,
                            right: -8,
                            child: InkWell(
                              onTap: () {
                                Get.toNamed(NavRoutes.topup);
                              },
                              child: Container(
                                padding: const EdgeInsets.all(15),
                                height: 80,
                                decoration: const BoxDecoration(
                                  color: AppColors.stackBlue,
                                  shape: BoxShape.circle,
                                ),
                                child: Image.asset(
                                  AssetUrl.launcher,
                                  //width: 40,
                                  color: Colors.white.withOpacity(0.8),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    GetBuilder(
                      init: userController,
                      builder: (controller) {
                        if (userController.kittiesLoading.isTrue) {
                          return SizedBox(
                            height: SizeConfig.screenHeight * 0.1,
                            child: Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SpinKitDualRing(
                                    color: ColorUtil.blueColor,
                                    lineWidth: 4.sp,
                                    size: 40.0.sp,
                                  ),
                                  const Text(
                                    "loading..",
                                    style: TextStyle(
                                      color: Colors.white,
                                    ),
                                  )
                                ],
                              ),
                            ),
                          );
                        } else if (userController.media.isEmpty) {
                          return SizedBox(
                            height: 100,
                            child: Column(
                              children: [
                                Container(
                                  margin: const EdgeInsets.symmetric(
                                      horizontal: 12),
                                  decoration: BoxDecoration(
                                    color: AppColors.mainPurple,
                                    borderRadius: BorderRadius.circular(16),
                                    image: DecorationImage(
                                      fit: BoxFit.cover,
                                      image: AssetImage(AssetUrl.img1),
                                    ),
                                  ),
                                )
                              ],
                            ),
                          );
                        } else {
                          final medias = userController.media;
                          return Stack(
                            clipBehavior: Clip.none,
                            children: [
                              SizedBox(
                                height: 120,
                                child: PageView.builder(
                                    controller: _pageController,
                                    itemCount: medias.length,
                                    onPageChanged: (value) {
                                      setState(() {
                                        activeIndex = value;
                                      });
                                    },
                                    itemBuilder: (context, index) {
                                      final media = medias[index];
                                      if (media.type == "VIDEO") {
                                        return VideoWidget(
                                          url: media.mediaUrl ?? "",
                                        );
                                      } else {
                                        return InkWell(
                                          onTap: () {
                                            Get.to(
                                              () => ViewMedia(
                                                media: media,
                                              ),
                                            );
                                          },
                                          child: Container(
                                            margin: const EdgeInsets.symmetric(
                                                horizontal: 12),
                                            decoration: BoxDecoration(
                                              color: AppColors.mainPurple,
                                              borderRadius:
                                                  BorderRadius.circular(16),
                                              image: DecorationImage(
                                                fit: BoxFit.cover,
                                                image: FastCachedImageProvider(
                                                  media.mediaUrl ?? "",
                                                ),
                                              ),
                                            ),
                                          ),
                                        );
                                      }
                                    }),
                              ),
                              Positioned(
                                  left: 0,
                                  right: 0,
                                  bottom: -20,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: List<Widget>.generate(
                                        medias.length,
                                        (index) => Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 3.0),
                                              child: InkWell(
                                                onTap: () {
                                                  _pageController.animateToPage(
                                                      index,
                                                      duration: const Duration(
                                                          milliseconds: 300),
                                                      curve: Curves.easeIn);
                                                },
                                                child: CircleAvatar(
                                                  radius: 5,
                                                  backgroundColor:
                                                      activeIndex == index
                                                          ? AppColors.primary
                                                          : AppColors.neutral,
                                                ),
                                              ),
                                            )),
                                  ))
                            ],
                          );
                        }
                      },
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 20),
                      child: Column(
                        children: [
                          SingleLineRow(
                            text: "My Recent Kitty",
                            widget: TextButton(
                                onPressed: () {
                                  Navigator.pushNamed(context,
                                      NavRoutes.myKittiescontribtionScreen);
                                },
                                child: const Text("See all kitties")),
                          ),
                          SizedBox(
                            height: 220,
                            child: GetBuilder(
                                init: userController,
                                initState: (state) {
                                  Future.delayed(
                                    const Duration(seconds: 2),
                                    () async {
                                      try {
                                        await state.controller!
                                            .getUserkitties();
                                      } catch (e) {
                                        Logger().e(e);
                                      }
                                    },
                                  );
                                },
                                builder: (UserKittyController userController) {
                                  if (userController.kittiesLoading.isTrue) {
                                    return SizedBox(
                                      height: SizeConfig.screenHeight * 0.1,
                                      child: Center(
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            SpinKitDualRing(
                                              color: ColorUtil.blueColor,
                                              lineWidth: 4.sp,
                                              size: 40.0.sp,
                                            ),
                                            const Text(
                                              "loading..",
                                              style: TextStyle(
                                                color: Colors.white,
                                              ),
                                            )
                                          ],
                                        ),
                                      ),
                                    );
                                  } else if (userController.kitties.isEmpty) {
                                    return SizedBox(
                                      //height: 60,
                                      child: Column(
                                        children: [
                                          //Text("You dont have any kitties", style: context.titleLarge,),
                                          Image.asset(
                                            AssetUrl.notFound,
                                            height: 130.h,
                                          ),
                                          CustomKtButton(
                                            height: 40.h,
                                            btnText: "Create a kitty",
                                            onPress: () {
                                              showGeneralDialog(
                                                context: context,
                                                barrierDismissible: true,
                                                barrierLabel: "Close",
                                                barrierColor: Colors.black
                                                    .withOpacity(0.5),
                                                transitionDuration:
                                                    const Duration(
                                                        milliseconds: 200),
                                                pageBuilder: (BuildContext
                                                        context,
                                                    Animation animation,
                                                    Animation
                                                        secondaryAnimation) {
                                                  return const CreateTabs();
                                                },
                                                transitionBuilder:
                                                    (BuildContext context,
                                                        Animation<double>
                                                            animation,
                                                        Animation<double>
                                                            secondaryAnimation,
                                                        Widget child) {
                                                  return ScaleTransition(
                                                    scale: animation,
                                                    child: child,
                                                  );
                                                },
                                              );
                                            },
                                          )
                                        ],
                                      ),
                                    );
                                  } else if (userController
                                      .kitties.isNotEmpty) {
                                    return SizedBox(
                                      height: 200,
                                      child: PageView.builder(
                                        scrollDirection: Axis.horizontal,
                                        controller: _page2Controller,
                                        onPageChanged: (value) {
                                          setState(() {
                                            activeIndex2 = value;
                                          });
                                          print(
                                              "Kitties index is: $activeIndex2");
                                        },
                                        itemCount:
                                            userController.kitties.length < 5
                                                ? userController.kitties.length
                                                : userController.kitties
                                                    .sublist(0, 5)
                                                    .length,
                                        itemBuilder: (context, index) {
                                          final kitty =
                                              userController.kitties[index];
                                          return Padding(
                                            padding: const EdgeInsets.all(8.0),
                                            child: ContributionKittyWidget(
                                              kitty: kitty,
                                            ),
                                          );
                                        },
                                      ),
                                    );
                                  }
                                  return SizedBox(
                                    child: Column(
                                      children: [
                                        const Text("You dont have any kitties"),
                                        Image.asset(
                                          AssetUrl.notFound,
                                          height: 150.h,
                                        ),
                                        CustomKtButton(
                                          height: 50,
                                          btnText: "Create a kitty",
                                          onPress: () {
                                            Get.to(() => const StepOne());
                                          },
                                        )
                                      ],
                                    ),
                                  );
                                }),
                          ),
                          const SizedBox(
                            height: 20,
                          ),
                          userController.kitties.isEmpty
                              ? const SizedBox()
                              : Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: List<Widget>.generate(
                                      userController.kitties.length < 5
                                          ? userController.kitties.length
                                          : userController.kitties
                                              .sublist(0, 5)
                                              .length,
                                      //userController.kitties.length,
                                      (index) => Padding(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 3.0),
                                            child: InkWell(
                                              onTap: () {
                                                _page2Controller.animateToPage(
                                                    index,
                                                    duration: const Duration(
                                                        milliseconds: 600),
                                                    curve: Curves.easeIn);
                                              },
                                              child: CircleAvatar(
                                                radius: 5,
                                                backgroundColor:
                                                    activeIndex2 == index
                                                        ? AppColors.primary
                                                        : AppColors.neutral,
                                              ),
                                            ),
                                          )),
                                ),
                          const SizedBox(
                            height: 20,
                          ),
                        ],
                      ),
                    ),
                    DefaultTabController(
                      length: 2,
                      child: Column(
                        children: [
                          Container(
                            padding: EdgeInsets.all(5.h),
                            margin: const EdgeInsets.symmetric(horizontal: 20),
                            decoration: AppDecoration.fillSlate.copyWith(
                              borderRadius: BorderRadiusStyle.roundedBorder6,
                            ),
                            child: TabBar(
                              physics: const ClampingScrollPhysics(),
                              //padding: const EdgeInsets.only(left: 5, right: 5),
                              unselectedLabelColor: Colors.black,
                              labelColor: Theme.of(context).primaryColor,
                              indicatorSize: TabBarIndicatorSize.tab,
                              dividerColor: Colors.transparent,
                              indicator: BoxDecoration(
                                  borderRadius: BorderRadius.circular(5),
                                  color: Colors.white),
                              tabs: [
                                Tab(
                                  child: Padding(
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 1.w),
                                    child: const Text("Services"),
                                  ),
                                ),
                                Tab(
                                  child: Padding(
                                    padding:
                                        EdgeInsets.symmetric(horizontal: 1.w),
                                    child: const Text("My Transactions"),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                            height: screenSize.height * 0.35,
                            child: Column(
                              children: [
                                Expanded(
                                  child: TabBarView(
                                    children: [
                                      HomeServices(),
                                      UserTransactionWidget()
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          )
                        ],
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

buildPages(String img) {
  return Image.asset(
    img,
    fit: BoxFit.cover,
  );
}
