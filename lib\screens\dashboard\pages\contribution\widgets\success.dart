import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/show_toast.dart';
import 'package:onekitty/models/kitty_model.dart'; 
import 'package:onekitty/utils/size_config.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

class SucessPage extends StatefulWidget {
  final String? text;
  final String? kittyName;
  final SocialMesages? socialMessages;
  const SucessPage({super.key, this.text, this.kittyName, this.socialMessages});

  @override
  State<SucessPage> createState() => _SucessPageState();
}

class _SucessPageState extends State<SucessPage> { 
  List<String> socials = [
    "assets/images/insta.png",
    "assets/images/whatsapp.jpg",
    "assets/images/twitter.png",
    "assets/images/solar_link-bold.png",
    "assets/images/solar_link-bold.png",
  ];
  final KittyController kittyController = Get.put(KittyController());
  @override
  void initState() {
    super.initState(); 
  }

  @override
  void dispose() { 

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 30),
      child: Column(
        children: [
          Row(
            children: [
              IconButton(
                onPressed: () {
                  Navigator.popUntil(context, (route) => route.isFirst);
                },
                icon: const Icon(Icons.arrow_back),
              ),
              const Text("Back")
            ],
          ),
          SizedBox(
            height: SizeConfig.screenHeight * 0.15,
          ),
          Column(
            children: [ 
              Image.asset("assets/images/Vector.png"),
              const SizedBox(
                height: 30,
              ),
              RichText(
                  textAlign: TextAlign.center,
                  text: TextSpan(children: [
                    TextSpan(
                      text: widget.kittyName,
                      style: Theme.of(context)
                          .textTheme
                          .titleLarge
                          ?.copyWith(fontWeight: FontWeight.bold, fontSize: 18),
                    ),
                    TextSpan(
                      text: " was successfully ${widget.text}",
                      style: Theme.of(context)
                          .textTheme
                          .titleLarge
                          ?.copyWith(fontSize: 18),
                    )
                  ])),
              const SizedBox(
                height: 15,
              ),
              const Text(
                "Share the kitty contribution link to invite friends to start contributing",
                textAlign: TextAlign.center,
              ),
              const SizedBox(
                height: 15,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: InkWell(
                        onTap: () async {
                          await Share.share(
                              kittyController.socials["instagram"].toString());
                        },
                        child: Image.asset("assets/images/insta.png")),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: InkWell(
                        onTap: () async {
                          await Share.share(
                              kittyController.socials["tiktok"].toString());
                        },
                        child: SvgPicture.asset(
                            "assets/images/tiktok-outline.svg",
                            colorFilter: const ColorFilter.mode(
                                AppColors.primary, BlendMode.srcIn))),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: InkWell(
                        onTap: () async {
                          final Uri url = Uri.parse(
                              "https://twitter.com/intent/tweet?text=${kittyController.socials["twitter"].toString()}");
                          if (!await launchUrl(url)) {
                            ToastUtils.showErrorToast(
                                context, "Could not launch the url", "Error");
                          }
                        },
                        child: Image.asset("assets/images/twitter.png")),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: InkWell(
                        onTap: () async {
                          final Uri url = Uri.parse(
                              "https://www.facebook.com/sharer/sharer.php?quote=${kittyController.socials["facebook"]}");
                          if (!await launchUrl(url)) {
                            ToastUtils.showErrorToast(
                                context, "Could not launch the url", "Error");
                          }
                        },
                        child: Image.asset(
                          "assets/images/Vector (7).png",
                          //color: AppColors.blueButtonColor
                        )),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: InkWell(
                        onTap: () async {
                          await Share.share(
                              kittyController.socials["youtube"].toString());
                        },
                        child: Image.asset(
                          "assets/images/Vector (6).png",
                          //color: AppColors.blueButtonColor,
                        )),
                  ),
                ],
              )
              // Wrap(
              //     alignment: WrapAlignment.center,
              //     children: List.generate(socials.length, (index) {
              //       return Padding(
              //         padding: const EdgeInsets.all(16.0),
              //         child: InkWell(
              //             onTap: () {
              //               showModalBottomSheet(
              //                   context: context,
              //                   builder: (context) {
              //                     return Container(
              //                       child: Text(kittyController.socials["twitter"]),
              //                     );
              //                   });
              //             },
              //             child: Image.asset(socials.elementAt(index))),
              //       );
              //     })),
            ],
          )
        ],
      ),
    );
  }
}
