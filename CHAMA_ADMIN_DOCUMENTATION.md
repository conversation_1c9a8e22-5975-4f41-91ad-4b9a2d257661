# Chama Admin Documentation

## Overview
The Chama Admin module provides comprehensive administrative functionality for managing chamas (rotating savings and credit associations) in the OneKitty application. This documentation covers data fetching, table display, filtering, chama management, and detailed chama views with extensive administrative functions.

## 1. Chama Admin Screen Structure

### Main Components
- **ChamaAdmin** (`lib/admin_screens/chama/chama_admin.dart`)
- **ChamaAdminController** (`lib/controllers/admin/chama/chama_admin_controller.dart`)
- **ChamaDataTable** (Data grid component)
- **ChamaFilterWidget** (Filtering component)
- **ChamaDetailsPage** (Admin view with extensive actions)

## 2. Data Fetching

### API Endpoints
- **Main Chamas**: `chama/admin/chamas/`
- **Chama Details**: `chama/chama/chama-details/`
- **Chama Transactions**: `chama/chama/transactions/`
- **Chama Settings**: `chama/chama/settings/`
- **Send Funds**: `chama/admin/send-funds-beneficiaries/`
- **Add Penalty**: `chama/admin/add-general-penalty/`
- **Invoices**: `chama/chama/invoices/`
- **Occurrence**: `chama/chama/chama-occurrence/`

### Request Parameters
```
?page={page}&size={size}&search={search}&chama_id={chamaId}&frequency={frequency}&kitty_id={kittyId}&start_date={startDate}&end_date={endDate}
```

| Parameter | Type | Description |
|-----------|------|-------------|
| `page` | int | Page number for pagination (default: 0) |
| `size` | int | Number of items per page (default: 15) |
| `search` | string | Search term for chama names |
| `chama_id` | string | Filter by specific chama ID |
| `frequency` | string | Filter by frequency (WEEKLY, MONTHLY, etc.) |
| `kitty_id` | string | Filter by associated kitty ID |
| `start_date` | string | Filter by start date (ISO format) |
| `end_date` | string | Filter by end date (ISO format) |

### Controller Implementation
```dart
Future fetchAllChamas(int page) async {
  try {
    isLoading(true);
    final String url = "${ApiUrls.getAllChamasAdmin}?page=$page&size=${size.value}&search=${search.value}&chama_id=${chamaId.value}&frequency=${frequency.value}&kitty_id=${kittyId.value}&start_date=${startDate.value}&end_date=${endDate.value}";
    
    var response = await apiProvider.request(method: Method.GET, url: url);
    
    if (response.statusCode == 200 && response.data != null) {
      chamas((response.data['data']['items'] as List)
          .map((e) => Chama.fromJson(e))
          .toList());

      currentPage.value = response.data['data']['page'];
      maxPage.value = response.data['data']['total_pages'];
      totalPages.value = response.data['data']['total_pages'];
      isLast.value = response.data['data']['last'];
      isFirst.value = response.data['data']['first'];
    }
  } catch (e) {
    logger.e("Error fetching chamas: $e");
  } finally {
    isLoading(false);
  }
}
```

## 3. Table Display Values

### Data Grid Columns
The chama table displays the following columns:

| Column | Data Source | Format | Description |
|--------|-------------|--------|-------------|
| **ID** | `chama.id` | String | Chama unique identifier |
| **Created At** | `chama.createdAt` | `yyyy-MM-dd HH:mm` | Chama creation timestamp |
| **Title** | `chama.title` | String | Chama name/title |
| **Username** | `chama.username` | String | Chama organizer username |
| **Kitty ID** | `chama.kittyId` | String | Associated kitty identifier |
| **Email** | `chama.email` | String | Organizer email address |
| **Phone** | `chama.phoneNumber` | String | Organizer phone number |
| **Amount** | `chama.amount` | Currency (KES) | Contribution amount per cycle |
| **Balance** | `chama.balance` | Currency (KES) | Current chama balance |
| **Next Occurrence** | `chama.nextOccurrence` | `yyyy-MM-dd HH:mm` | Next contribution date |
| **Status** | `chama.status` | Color-coded | Chama status with color coding |

### Data Source Implementation
```dart
class ChamaDataSource extends DataGridSource {
  ChamaDataSource({required List<Chama> chamaData}) {
    _chamaData = chamaData.map<DataGridRow>((chama) => DataGridRow(cells: [
      DataGridCell<String>(columnName: 'id', value: chama.id.toString()),
      DataGridCell<String>(columnName: 'created_at', 
          value: DateFormat('yyyy-MM-dd HH:mm').format(chama.createdAt ?? DateTime.now())),
      DataGridCell<String>(columnName: 'title', value: chama.title ?? ''),
      DataGridCell<String>(columnName: 'username', value: chama.username ?? ''),
      DataGridCell<String>(columnName: 'kittyId', value: chama.kittyId.toString()),
      DataGridCell<String>(columnName: 'email', value: chama.email ?? ''),
      DataGridCell<String>(columnName: 'phone', value: chama.phoneNumber ?? ''),
      DataGridCell<String>(columnName: 'amount', 
          value: FormattedCurrency().getFormattedCurrency(chama.amount).toString()),
      DataGridCell<String>(columnName: 'balance', 
          value: FormattedCurrency().getFormattedCurrency(chama.balance).toString()),
      DataGridCell<String>(columnName: 'next_occurence', 
          value: DateFormat('yyyy-MM-dd HH:mm').format(chama.nextOccurrence ?? DateTime.now())),
      DataGridCell<String>(columnName: 'status', value: chama.status ?? ''),
    ])).toList();
  }
}
```

## 4. Filtering Functionality

### Filter Widget Components
The `ChamaFilterWidget` provides the following filters:

#### Date Range Filters
- **Start Date**: Date picker for filtering from a specific date
- **End Date**: Date picker for filtering to a specific date
- Both dates are stored in ISO format and can be cleared

#### Search Filters
- **Kitty ID Filter**: Text input with 1-second debounce
- **Phone Number Filter**: Text input with 1-second debounce  
- **Search by Name**: Text input with 1-second debounce

## 5. Chama Click Navigation & Actions

### On Cell Tap Event
When a user clicks on any cell in the chama table:

```dart
onCellTap: (details) {
  final singleChamaController = Get.put(ChamaDataController());
  singleChamaController.chama.value = UserChama(
      chama: controller.chamas[details.rowColumnIndex.rowIndex - 1]);
  singleChamaController.singleChamaDts.value = 
      controller.chamas[details.rowColumnIndex.rowIndex - 1];
  Get.to(() => const ViewingSingleChama());
}
```

### Navigation Flow
1. **Click Detection**: Any cell click triggers the `onCellTap` callback
2. **Data Setting**: Selected chama is stored in `ChamaDataController`
3. **Navigation**: User is navigated to `ViewingSingleChama`
4. **Transition**: Standard navigation

## 6. Admin Functions & Actions

### 6.1 Pagination Controls
**Location**: Bottom of chama admin screen
- **Rows Per Page**: Dropdown (15, 30, 50, 100 options)
- **Previous/Next**: Navigation buttons
- **Refresh**: Reload current page
- **Page Info**: Current page / Total pages display

### 6.2 Single Chama View Actions
**Location**: `ViewingSingleChama` and `ChamaDetailsPage`

#### Desktop Admin View Button
- **Icon**: `Icons.desktop_mac`
- **Function**: Navigate to comprehensive admin view
- **Page**: `ChamaDetailsPage`

## 7. Comprehensive Admin Actions (ChamaDetailsPage)

### 7.1 Financial Management
#### Send Funds to Beneficiary
- **Function**: `sendFundsToBeneficiary(id: chama.id)`
- **API**: `chama/admin/send-funds-beneficiaries/`
- **Purpose**: Transfer accumulated funds to current beneficiary

#### Add General Penalty
- **Function**: `addGeneralPenalty(params: Map<String, dynamic>)`
- **API**: `chama/admin/add-general-penalty/`
- **Purpose**: Apply penalties to chama members

#### Add Beneficiary Account
- **Function**: `addBeneficiaryAccount(params: Map<String, dynamic>)`
- **API**: `chama/chama/add-beneficiary-account/`
- **Purpose**: Set up beneficiary payment accounts

### 7.2 Administrative Actions
The admin view provides 15+ action chips for comprehensive chama management:

#### Penalties Management
- **Chip**: "Penalties"
- **Icon**: `Icons.gavel`
- **Page**: `Penalties`
- **Function**: View and manage member penalties

#### Invoices Management
- **Chip**: "Invoices"
- **Icon**: `Icons.receipt_long`
- **Page**: `InvoicesPage`
- **Function**: View payment invoices and billing

#### Occurrence Management
- **Chip**: "Occurence"
- **Icon**: `Icons.calendar_today`
- **Page**: `ChamaOccurence`
- **Function**: Manage contribution cycles and schedules

#### Edit Chama
- **Chip**: "Edit Chama"
- **Icon**: `Icons.edit`
- **Page**: `UpdateChama`
- **Function**: Modify chama details and settings

#### Operations
- **Chip**: "Operations"
- **Icon**: `Icons.settings`
- **Page**: `Operations`
- **Function**: General chama operations and management

#### Resources Management
- **Chip**: "Chama Resources"
- **Icon**: `Icons.library_books`
- **Page**: `ResourcesWidget`
- **Function**: Manage chama documents and resources

#### Members Management
- **Chip**: "Members"
- **Icon**: `Icons.manage_accounts_outlined`
- **Page**: `ChamaBeneficiariesPage`
- **Function**: View and manage chama members

#### Block Chama
- **Chip**: "Block Chama"
- **Icon**: `Icons.block`
- **Function**: Block/suspend chama operations
- **Status**: Placeholder (not implemented)

#### Settings Management
- **Chip**: "Chama Settings"
- **Icon**: `Icons.settings`
- **Page**: `ChamaSettingsScreen`
- **Function**: Configure chama parameters

#### Meetings Management
- **Chip**: "Meetings"
- **Icon**: `Icons.settings`
- **Page**: `MeetingsPage`
- **Function**: View scheduled meetings

#### Add Meeting
- **Chip**: "Add Meeting"
- **Icon**: `Icons.add`
- **Page**: `AddMeetingPage`
- **Function**: Schedule new meetings

#### WhatsApp Integration
- **Chip**: "Add WhatsApp"
- **Icon**: WhatsApp logo
- **Dialog**: `AttachWhatsapp`
- **Function**: Connect WhatsApp groups for notifications

#### Transaction Management
- **Chip**: "Chama Transactions"
- **Icon**: `Icons.list`
- **Page**: `TransactionTable`
- **Function**: View all chama transactions

## 8. Single Chama View Features

### 8.1 Header Information
- **Chama Title**: Prominent display
- **Chama Balance**: Formatted currency display
- **Penalty Balance**: If applicable
- **Description**: Rich text with expand/collapse

### 8.2 Key Metrics Display
- **Contribution Amount**: Per cycle with frequency
- **Penalty Count**: Number of penalties accrued
- **Next Cycle**: Relative time display
- **Deadline**: Formatted date display

### 8.3 Beneficiary Management
- **Next Beneficiary**: Carousel view of upcoming beneficiaries
- **Member Details**: Name, role, amount to receive
- **Beneficiary Actions**: Click to manage beneficiary details

### 8.4 WhatsApp Integration
- **Connected Groups**: List of linked WhatsApp groups
- **Add Group**: Quick action to connect new groups
- **Group Management**: View and manage connected groups

### 8.5 Action Tabs
#### Services Tab
- **Chama Services**: Various chama management functions
- **Quick Actions**: Common administrative tasks

#### Transactions Tab
- **Transaction History**: Chronological list of all transactions
- **Transaction Details**: Full transaction information
- **Export Options**: Data export capabilities

### 8.6 Sharing Features
- **Share Button**: Share chama details via social platforms
- **QR Code**: Generate QR code for easy joining
- **Contribution Link**: Direct link to contribution page

## 9. Data Models

### Chama Model Structure
```dart
class Chama {
  int? id;
  DateTime? createdAt;
  DateTime? updatedAt;
  int? kittyId;
  String? title;
  String? username;
  String? description;
  String? phoneNumber;
  String? accountNumber;
  String? accountNumberRef;
  int? channel;
  String? transferMode;
  double? balance;
  double? totaBal;
  dynamic amount;
  String? email;
  int? refererCode;
  DateTime? nextOccurrence;
  String? frequency;
  String? status;
  int? lastReceivedMemberId;
}
```

### UserChama Wrapper
```dart
class UserChama {
  int? membersCount;
  Chama? chama;
  Member? member;
}
```

### NextBeneficiary Model
```dart
class NextBeneficiary {
  double? beneficiaryPercentage;
  dynamic amountToReceive;
  Member? member;
  List<Beneficiary>? beneficiaries;
}
```

## 10. Advanced Features

### 10.1 Transaction Management
```dart
Future<bool> getChamaTrnsactions({
  required int chamaId, 
  required int page, 
  String filters = ''
}) async {
  try {
    String url = "${ApiUrls.getChamaTransactions}?page=$page&size=${invoiceSize.value}&chama_id=$chamaId&$filters";
    var response = await apiProvider.request(url: url, method: Method.GET);
    
    if (response.data["status"]) {
      transactions.clear();
      for (var element in response.data["data"]["items"] ?? []) {
        transactions.add(Transaction.fromJson(element));
      }
      return true;
    }
  } catch (e) {
    logger.e(e);
    return false;
  }
}
```

### 10.2 Settings Management
```dart
Future getChamaSettings({required int chamaId}) async {
  try {
    var res = await apiProvider.request(
        url: "${ApiUrls.getChamaSettings}?chama_id=$chamaId",
        method: Method.GET);
    
    final data = res.data["data"];
    if (data != null) {
      final settingss = ChamaSetting.fromJson(data);
      chamaSettings(settingss);
    }
    return res.data["status"];
  } catch (e) {
    logger.e(e);
    return false;
  }
}
```

### 10.3 Occurrence Management
```dart
Future getChamaOccurence(int id, {String filters = ''}) async {
  try {
    var response = await apiProvider.request(
        method: Method.GET,
        url: "${ApiUrls.getChamaOccurence}?page=${invoiceCurrentPage.value}&size=${invoiceSize.value}&chama_id=$id&$filters");
        
    if (response.data['status'] ?? false) {
      chamaOcurences.value = (response.data['data']['items'] as List)
          .map((e) => ChamaOcurenceModel.fromJson(e))
          .toList();
    }
  } catch (e) {
    logger.e(e);
  }
}
```

## 11. Status Management

### Status Color Coding
```dart
// Status colors are handled in common color codes
Color getStatusColor(String status) {
  switch (status.toLowerCase()) {
    case "active": return const Color(0xFF56AF57);
    case "completed": return const Color(0xFF56AF57);
    case "suspended": return const Color(0xFFEE5B60);
    case "pending": return Colors.amber;
    default: return const Color(0xFFEE5B60);
  }
}
```

### Status Types
- **ACTIVE**: Green - Chama is operational
- **COMPLETED**: Green - Chama cycle completed
- **SUSPENDED**: Red - Chama has been suspended
- **PENDING**: Amber - Awaiting approval or action

## 12. Key Features Summary

### Admin Table Features
- ✅ Sortable columns
- ✅ Resizable columns
- ✅ Frozen first column
- ✅ Cell-based navigation
- ✅ Loading states
- ✅ Error handling
- ✅ Pagination controls

### Filtering Features
- ✅ Date range filtering
- ✅ Text search with debounce
- ✅ Multiple filter criteria
- ✅ Real-time filtering
- ✅ Frequency filtering
- ✅ Clear filter options

### Chama Management Features
- ✅ Comprehensive chama details
- ✅ Financial management
- ✅ Member management
- ✅ Penalty system
- ✅ Meeting scheduling
- ✅ Resource management
- ✅ WhatsApp integration
- ✅ Transaction tracking
- ✅ Settings configuration

### Administrative Functions
- ✅ Send funds to beneficiaries
- ✅ Add general penalties
- ✅ Manage beneficiary accounts
- ✅ View invoices and billing
- ✅ Manage contribution cycles
- ✅ Edit chama details
- ✅ Block/unblock chamas
- ✅ Configure settings
- ✅ Schedule meetings
- ✅ Manage resources

### User Experience Features
- ✅ Responsive design
- ✅ Pull-to-refresh
- ✅ Loading indicators
- ✅ Error handling
- ✅ Share functionality
- ✅ QR code generation
- ✅ Rich text descriptions
- ✅ Carousel navigation

## 13. Technical Implementation Notes

### State Management
- Uses **GetX** for reactive state management
- Multiple controllers for different aspects (admin, data, settings)
- Observable variables for real-time UI updates

### UI Components
- **SfDataGrid** for table display
- **CustomScrollView** with slivers for single chama view
- **ActionChip** widgets for admin actions
- **SmartRefresher** for pull-to-refresh functionality
- **PageView** for beneficiary carousel

### Performance Optimizations
- Debounced search inputs (1-second delay)
- Lazy loading with pagination
- Efficient data grid rendering
- Conditional widget rendering
- Memory management for large datasets

### Error Handling
- Comprehensive try-catch blocks
- User-friendly error messages
- Fallback UI states
- Network error handling
- Data validation

This comprehensive documentation covers all aspects of the Chama Admin functionality, from basic data display to advanced administrative operations, providing a complete reference for understanding and maintaining the chama management system.