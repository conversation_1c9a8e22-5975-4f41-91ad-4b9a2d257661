import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';
import 'package:onekitty/screens/dashboard/pages/chama/signatories/add_signatories.dart';
import '../../../../../utils/size_config.dart';
import '../../../../../utils/utils_exports.dart';

class Signatories extends StatefulWidget {
  const Signatories({super.key});

  @override
  State<Signatories> createState() => _SignatoriesState();
}

class _SignatoriesState extends State<Signatories> {
  final ChamaDataController chamaDataController =
      Get.put(ChamaDataController());
  final ChamaController chamaController = Get.put(ChamaController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      //appBar: buildAppBar(context),
      body: SafeArea(
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
          child: Column(
            children: [
              ActionChip(
                  avatar: const Icon(Icons.person_add_outlined, size: 18),
                  label: const Text('Add Signatory'),
                  onPressed: () async {
                    final res = await Get.to(() => const AddSignatories());
                    if (res ?? false) {
                      Get.find<ChamaController>().getChamaTrnsactions(
                          chamaId: chamaController.chamaId.value, page: 0);
                    }
                  }),
              Expanded(
                child: GetX(
                    init: ChamaController(),
                    initState: (state) {
                      Future.delayed(Duration.zero, () async {
                        try {
                          await state.controller?.getSignatories(
                              chamaId:
                                  chamaDataController.chama.value.chama?.id ??
                                      0);
                        } catch (e) {
                          throw e;
                        }
                      });
                    },
                    builder: (ChamaController chamaController) {
                      if (chamaController.isGetSignatoryLoading.isTrue) {
                        return SizedBox(
                          height: SizeConfig.screenHeight * .33,
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SpinKitDualRing(
                                  color: ColorUtil.blueColor,
                                  lineWidth: 4.sp,
                                  size: 40.0.sp,
                                ),
                                const Text(
                                  "loading..",
                                  style: TextStyle(
                                    color: Colors.white,
                                  ),
                                )
                              ],
                            ),
                          ),
                        );
                      } else if (chamaController.signatories.isEmpty) {
                        return Center(
                          child: Column(
                            children: [
                              Image.asset(
                                AssetUrl.notFound,
                                height: 130.h,
                              ),
                              const Text("No Signatories added to this chama"),
                              const SizedBox(
                                height: 10,
                              ),
                            ],
                          ),
                        );
                      } else if (chamaController.signatories.isNotEmpty) {
                        return Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 2.h, vertical: 17.h),
                            child: DataTable2(
                              columns: const [
                                DataColumn2(
                                    label: Text('#'), size: ColumnSize.S),
                                DataColumn2(
                                    label: Text('Name'), size: ColumnSize.L),
                                DataColumn2(
                                    label: Text('Phone'), size: ColumnSize.L),
                                DataColumn2(
                                    label: Text('Notification'),
                                    size: ColumnSize.M),
                                DataColumn2(
                                    label: Text('Role'), size: ColumnSize.M),
                                DataColumn2(
                                    label: Text('Actions'), size: ColumnSize.S),
                              ],
                              rows: List<DataRow>.generate(
                                chamaController.signatories.length,
                                (index) {
                                  final signatory =
                                      chamaController.signatories[index];
                                  return DataRow(
                                    cells: [
                                      DataCell(Text('${index + 1}')),
                                      DataCell(
                                        Row(
                                          children: [
                                            CustomImageView(
                                              imagePath: AssetUrl.imgPerson,
                                              height: 25.h,
                                              width: 25.w,
                                              margin:
                                                  EdgeInsets.only(right: 8.w),
                                            ),
                                            Text(signatory.member?.firstName ??
                                                ''),
                                          ],
                                        ),
                                      ),
                                      DataCell(
                                          Text(signatory.phoneNumber ?? '')),
                                      DataCell(Text(
                                          signatory.notificationType ?? '')),
                                      DataCell(
                                          Text(signatory.member?.role ?? '')),
                                      DataCell(
                                        DropdownButtonHideUnderline(
                                          child: DropdownButton<String>(
                                            icon: const Icon(Icons.more_vert),
                                            items: <String>[
                                              'Edit',
                                              'Delete',
                                              'View Details'
                                            ].map((String value) {
                                              return DropdownMenuItem<String>(
                                                value: value,
                                                child: Text(value),
                                              );
                                            }).toList(),
                                            onChanged: (String? newValue) {
                                              switch (newValue) {
                                                case 'Edit':
                                                  // Edit signatory function
                                                  break;
                                                case 'Delete':
                                                  showDeleteDialog(index);
                                                  break;
                                                case 'View Details':
                                                  showDialog(
                                                    context: context,
                                                    builder:
                                                        (BuildContext context) {
                                                      return AlertDialog(
                                                        title: const Text(
                                                            'Signatory Details'),
                                                        content:
                                                            SingleChildScrollView(
                                                          child: ListBody(
                                                            children: <Widget>[
                                                              Text(
                                                                  'Name: ${signatory.member?.firstName ?? 'N/A'} ${signatory.member?.secondName ?? 'N/A'}'),
                                                              Text(
                                                                  'Phone: ${signatory.phoneNumber ?? 'N/A'}'),
                                                              Text(
                                                                  'Notification Type: ${signatory.notificationType ?? 'N/A'}'),
                                                              Text(
                                                                  'Role: ${signatory.member?.role ?? 'N/A'}'),
                                                            ],
                                                          ),
                                                        ),
                                                        actions: <Widget>[
                                                          TextButton(
                                                            child: const Text(
                                                                'Close'),
                                                            onPressed: () {
                                                              Navigator.of(
                                                                      context)
                                                                  .pop();
                                                            },
                                                          ),
                                                        ],
                                                      );
                                                    },
                                                  );
                                                  break;
                                              }
                                            },
                                          ),
                                        ),
                                      ),
                                    ],
                                  );
                                },
                              ),
                              columnSpacing: 12,
                              horizontalMargin: 12,
                              minWidth: 600,
                            ));
                      }
                      return const Text("No Signatories added to this chama");
                    }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  deleteSignatory(index) async {
    final signatory = chamaController.signatories[index];
    bool res = await chamaController.deleteSignatory(
        signatoryId: signatory.id ?? 0, chamaId: signatory.chamaId ?? 0);
    if (res) {
      Snack.show(res, chamaController.apiMessage.string);
      setState(() {
        chamaController.signatories.removeAt(index);
      });
    }
  }

  void showDeleteDialog(index) {
    showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            actions: [
              OutlinedButton(
                  onPressed: () {
                    Get.back();
                  },
                  child: const Text("No")),
              Obx(() => CustomKtButton(
                  width: 55.w,
                  height: 35.h,
                  isLoading: chamaController.isDeleteSignatoryLoading.isTrue,
                  onPress: () {
                    deleteSignatory(index);
                    Navigator.pop(context);
                  },
                  btnText: "Yes"))
            ],
            content:
                const Text("Are you sure you want to delete this signatory?"),
          );
        });
  }
}
