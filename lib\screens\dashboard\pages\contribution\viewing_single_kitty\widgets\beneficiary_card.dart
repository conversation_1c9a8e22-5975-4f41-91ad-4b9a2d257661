import 'package:animations/animations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/beneficiary_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/contribution_kitties/beneficiaries_page.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/viewing_single_kitty/widgets/view_single_beneficiary.dart';
import 'package:onekitty/utils/formatted_currency.dart';
import 'package:onekitty/utils/themes_colors.dart';
import 'package:onekitty/utils/timeSince.dart';
import 'package:onekitty/main.dart' as main;
import 'package:carousel_slider/carousel_slider.dart' as carousel;

class BeneficiaryCard extends StatelessWidget {
  const BeneficiaryCard({super.key});

  @override
  Widget build(BuildContext context) {
    final DataController dataController = Get.find();
    return Column(
      children: [
        SizedBox(height: 6.h),
        const Divider(color: AppColors.primary),
        SizedBox(
            height: 165.h,
            child: GetBuilder<BeneficiaryController>(initState: (_) async {
              final benController = Get.put(BeneficiaryController());
              await benController
                  .getBeneficiaries(dataController.kitty.value.kitty?.id ?? 0);
            }, builder: (benController) {
              return SingleChildScrollView(
                child: Column(
                  children: [
                    SizedBox(
                      height: 24.h,
                      child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Obx(
                              () => Text(
                                'Beneficiar${benController.beneficiaries.length == 1 ? 'y' : 'ies'}:',
                                style: TextStyle(
                                    fontSize: 12.spMin,
                                    color: main.isLight.value ? Colors.black : Colors.white,
                                    fontWeight: FontWeight.w600),
                              ),
                            ),
                            GestureDetector(
                                onTap: () =>
                                    Get.to(() => const BeneficiariesPage()),
                                child: Text(
                                  'See All',
                                  style: TextStyle(
                                      fontSize: 12.spMin,
                                      color: AppColors.primary,
                                      fontWeight: FontWeight.w600),
                                )),
                          ]),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(4.0),
                      child: benController.isFetchingBeneficiaries.value
                          ? SpinKitDualRing(
                              color: ColorUtil.blueColor,
                              lineWidth: 4.sp,
                              size: 40.0.sp,
                            )
                          : benController.beneficiaries.length == 1
                              ? OpenContainer(
                                closedColor:  main.isLight.value ? Colors.white : Colors.transparent,
                                  closedElevation: 0,
                                  openBuilder: (context, action) {
                                    return ViewSingleBeneficiary(
                                        beneficiary:
                                            benController.beneficiaries[0]);
                                  },
                                  closedBuilder: (context, action) {
                                    return Container(
                                      width: 300.w,
                                      decoration: BoxDecoration(
                                        color: main.isLight.value ? Colors.white : appTheme.gray900,
                                        borderRadius: BorderRadius.circular(10),
                                        boxShadow: const [
                                          BoxShadow(
                                            color: Colors.black26,
                                            blurRadius: 5,
                                            offset: Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      padding: const EdgeInsets.only(
                                          top: 2.0, left: 12, right: 12),
                                      margin: const EdgeInsets.symmetric(
                                          horizontal: 4.0, vertical: 6),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            children: [
                                              CircleAvatar(
                                                child: Text(
                                                  benController.beneficiaries[0]
                                                                  .accountName !=
                                                              null &&
                                                          benController
                                                              .beneficiaries[0]
                                                              .accountName!
                                                              .isNotEmpty &&
                                                          benController
                                                              .beneficiaries[0]
                                                              .accountName!
                                                              .split(' ')
                                                              .isNotEmpty
                                                      ? benController
                                                          .beneficiaries[0]
                                                          .accountName!
                                                          .split(' ')
                                                          .map((word) =>
                                                              word.isNotEmpty
                                                                  ? word[0]
                                                                  : '')
                                                          .take(2)
                                                          .join()
                                                          .toUpperCase()
                                                      : '',
                                                ),
                                              ),
                                              const SizedBox(width: 8),
                                              Text(
                                                '${benController.beneficiaries[0].accountName}',
                                                style: const TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  fontSize: 16,
                                                ),
                                              ),
                                              if (benController
                                                      .beneficiaries[0].role ==
                                                  'PRIMARY')
                                                Container(
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      horizontal: 8.0,
                                                      vertical: 2.0),
                                                  decoration: BoxDecoration(
                                                    border: Border.all(
                                                        color:
                                                            AppColors.primary),
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            25),
                                                    color: Colors.transparent,
                                                  ),
                                                  child: Row(
                                                    children: [
                                                      Icon(
                                                          Icons
                                                              .verified_outlined,
                                                          color:
                                                              AppColors.primary,
                                                          size: 10.sp),
                                                      const SizedBox(width: 4),
                                                      Text(
                                                        'Primary',
                                                        style: TextStyle(
                                                          color:
                                                              AppColors.primary,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                          fontSize: 8.spMin,
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                )
                                            ],
                                          ),
                                          const SizedBox(height: 2),
                                          Text(
                                              'Acct: ${benController.beneficiaries[0].accountNumber}'),
                                          // Text('Bank: Bank ${index % 3 + 1}'),
                                          const SizedBox(height: 2),
                                          if (benController
                                                  .beneficiaries[0].endDate !=
                                              null)
                                            FittedBox(
                                              child:
                                                  Text.rich(TextSpan(children: [
                                                TextSpan(
                                                    text:
                                                        'End Date: ${DateFormat('d MMM HH:MM a').format(benController.beneficiaries[0].endDate!)}'),
                                                TextSpan(
                                                    style: TextStyle(
                                                      fontSize: 12.spMin,
                                                    ),
                                                    text:
                                                        ' - ${highPrecisiontimeSince(benController.beneficiaries[0].endDate!.toLocal())}')
                                              ])),
                                            ),

                                          const SizedBox(height: 2),
                                          Row(
                                            children: [
                                              // Text(benController
                                              //     .beneficiaries[0].splitConfig!
                                              //     .toLowerCase()),
                                              benController.beneficiaries[0]
                                                          .splitConfig ==
                                                      "AMOUNT"
                                                  ? SizedBox(
                                                      height: 40.h,
                                                      child: Center(
                                                        child: Text(
                                                            "${FormattedCurrency().getFormattedCurrency(benController.beneficiaries[0].amount ?? 0)}",
                                                            style: const TextStyle(
                                                                fontSize: 20,
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w600,
                                                                letterSpacing:
                                                                    1.6)),
                                                      ),
                                                    )
                                                  : Row(
                                                      children: [
                                                        SizedBox(
                                                          width: 60.w,
                                                          height: 40.h,
                                                          child: Stack(
                                                            alignment: Alignment
                                                                .center,
                                                            children: [
                                                              CircularProgressIndicator(
                                                                value: benController
                                                                    .beneficiaries[
                                                                        0]
                                                                    .percentage
                                                                    ?.toDouble(),
                                                                strokeWidth: 4,
                                                                backgroundColor:
                                                                    Colors.grey[
                                                                        200],
                                                                valueColor: const AlwaysStoppedAnimation<
                                                                        Color>(
                                                                    AppColors
                                                                        .primary),
                                                              ),
                                                              Text(
                                                                '${((benController.beneficiaries[0].percentage ?? 0) * 100.0).toStringAsFixed(1)}%',
                                                                style: TextStyle(
                                                                    fontSize: 8
                                                                        .spMin),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                        SizedBox(
                                                          width: 5.w,
                                                        ),
                                                        Text(
                                                          benController
                                                                      .beneficiaries[
                                                                          0]
                                                                      .splitConfig ==
                                                                  "AMOUNT"
                                                              ? ""
                                                              : FormattedCurrency().getFormattedCurrency(((double.tryParse(
                                                                          "${dataController.kitty.value.kitty?.balance ?? 0.0}") ??
                                                                      0.0) *
                                                                  (benController
                                                                          .beneficiaries[
                                                                              0]
                                                                          .percentage
                                                                      as double))),
                                                          style: TextStyle(
                                                            fontSize: 12.spMin,
                                                            fontWeight:
                                                                FontWeight.w600,
                                                          ),
                                                        )
                                                      ],
                                                    ),
                                              const Spacer(),
                                              GestureDetector(
                                                onTap: () {
                                                  action.call();
                                                },
                                                child: Chip(
                                                  label: const Text('View'),
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            25),
                                                    side: const BorderSide(
                                                        color:
                                                            Colors.deepPurple,
                                                        width: 1),
                                                  ),
                                                  backgroundColor:
                                                      Colors.transparent,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                )
                              : carousel.CarouselSlider(
                                  items: benController.beneficiaries
                                      .map((beneficiary) {
                                    return Container(
                                      margin: EdgeInsets.symmetric(
                                          horizontal: 4.0.w, vertical: 3.h),
                                      width: 300.w,
                                      decoration: BoxDecoration(
                                        color: main.isLight.value ? Colors.white : AppColors.dark,
                                        borderRadius: BorderRadius.circular(10),
                                        boxShadow: const [
                                          BoxShadow(
                                            color: Colors.black26,
                                            blurRadius: 5,
                                            offset: Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      padding: const EdgeInsets.only(
                                        top: 2,
                                        right: 8,
                                        left: 8,
                                      ),
                                      child: OpenContainer(
                                        closedElevation: 0,
                                        closedColor: Colors.transparent,
                                        openBuilder: (context, action) {
                                          return ViewSingleBeneficiary(
                                              beneficiary: benController
                                                  .beneficiaries[0]);
                                        },
                                        closedBuilder: (context, action) =>
                                            Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              children: [
                                                CircleAvatar(
                                                  child: Text(
                                                    beneficiary.accountName !=
                                                                null &&
                                                            beneficiary
                                                                .accountName!
                                                                .isNotEmpty &&
                                                            beneficiary
                                                                .accountName!
                                                                .split(' ')
                                                                .isNotEmpty
                                                        ? beneficiary
                                                            .accountName!
                                                            .split(' ')
                                                            .map((word) =>
                                                                word.isNotEmpty
                                                                    ? word[0]
                                                                    : '')
                                                            .take(2)
                                                            .join()
                                                            .toUpperCase()
                                                        : '',
                                                  ),
                                                ),
                                                const SizedBox(width: 8),
                                                Text(
                                                  '${beneficiary.accountName}',
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    fontSize: 16,
                                                  ),
                                                ),
                                                if (beneficiary.role ==
                                                    'PRIMARY')
                                                  Container(
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        horizontal: 8.0,
                                                        vertical: 2.0),
                                                    decoration: BoxDecoration(
                                                      border: Border.all(
                                                          color: AppColors
                                                              .primary),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              25),
                                                      color: Colors.transparent,
                                                    ),
                                                    child: Row(
                                                      children: [
                                                        Icon(
                                                            Icons
                                                                .verified_outlined,
                                                            color: AppColors
                                                                .primary,
                                                            size: 10.sp),
                                                        const SizedBox(
                                                            width: 4),
                                                        Text(
                                                          'Primary',
                                                          style: TextStyle(
                                                            color: AppColors
                                                                .primary,
                                                            fontWeight:
                                                                FontWeight.bold,
                                                            fontSize: 8.spMin,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  )
                                              ],
                                            ),
                                            const SizedBox(height: 2),
                                            Text(
                                                'Acct: ${beneficiary.accountNumber}'),
                                            // Text('Bank: Bank ${index % 3 + 1}'),

                                            const SizedBox(height: 2),
                                            if (beneficiary.endDate != null)
                                              FittedBox(
                                                child: Text.rich(
                                                    TextSpan(children: [
                                                  TextSpan(
                                                      text:
                                                          'End Date: ${DateFormat('d MMM HH:MM a').format(beneficiary.endDate!)}'),
                                                  TextSpan(
                                                      style: TextStyle(
                                                        fontSize: 12.spMin,
                                                      ),
                                                      text:
                                                          ' - ${highPrecisiontimeSince(beneficiary.endDate!.toLocal())}')
                                                ])),
                                              ),
                                            Row(
                                              children: [
                                                beneficiary.splitConfig ==
                                                        "AMOUNT"
                                                    ? SizedBox(
                                                        height: 25.h,
                                                        child: Center(
                                                          child: Text(
                                                              "${FormattedCurrency().getFormattedCurrency(beneficiary.amount ?? 0)}",
                                                              style: const TextStyle(
                                                                  fontSize: 20,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w600,
                                                                  letterSpacing:
                                                                      1.6)),
                                                        ),
                                                      )
                                                    : SizedBox(
                                                        width: 40.w,
                                                        height: 40.h,
                                                        child: Row(
                                                          children: [
                                                            Stack(
                                                              alignment:
                                                                  Alignment
                                                                      .center,
                                                              children: [
                                                                CircularProgressIndicator(
                                                                  value: beneficiary
                                                                      .percentage
                                                                      ?.toDouble(),
                                                                  strokeWidth:
                                                                      4,
                                                                  backgroundColor:
                                                                      Colors.grey[
                                                                          200],
                                                                  valueColor: const AlwaysStoppedAnimation<
                                                                          Color>(
                                                                      AppColors
                                                                          .primary),
                                                                ),
                                                                Text(
                                                                  '${((beneficiary.percentage ?? 0) * 100.0).toStringAsFixed(1)}%',
                                                                  style: TextStyle(
                                                                      fontSize:
                                                                          8.spMin),
                                                                ),
                                                              ],
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                SizedBox(
                                                  width: 5.w,
                                                ),
                                                if (beneficiary.splitConfig ==
                                                    "PERCENTAGE")
                                                  FittedBox(
                                                    child: Text(
                                                      FormattedCurrency()
                                                          .getFormattedCurrency(
                                                              ((double.tryParse(
                                                                          "${dataController.kitty.value.kitty?.balance ?? 0.0}") ??
                                                                      0.0) *
                                                                  (beneficiary
                                                                          .percentage
                                                                      as double))),
                                                      style: TextStyle(
                                                        fontSize: 12.spMin,
                                                        fontWeight:
                                                            FontWeight.w600,
                                                      ),
                                                    ),
                                                  ),
                                                const Spacer(),
                                                GestureDetector(
                                                  onTap: () {
                                                    action.call();
                                                  },
                                                  child: Container(
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        horizontal: 12,
                                                        vertical: 3),
                                                    decoration: BoxDecoration(
                                                      color: Colors.transparent,
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              25),
                                                      border: Border.all(
                                                        color:
                                                            Colors.deepPurple,
                                                        width: 1,
                                                      ),
                                                    ),
                                                    child: const Text(
                                                      'View',
                                                      style: TextStyle(
                                                        color:
                                                            Colors.deepPurple,
                                                        fontSize: 14,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                  options: carousel.CarouselOptions(
                                    height: 135.h,
                                    // padEnds: false,
                                    enlargeCenterPage: true,
                                    enableInfiniteScroll: true,
                                    viewportFraction: 0.8,
                                    autoPlay: false,
                                  )),
                    ),
                  ],
                ),
              );
            })),
        const Divider(color: AppColors.primary),
      ],
    );
  }
}
