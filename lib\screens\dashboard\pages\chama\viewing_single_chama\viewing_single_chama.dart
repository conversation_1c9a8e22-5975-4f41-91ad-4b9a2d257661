// ignore_for_file: prefer_const_literals_to_create_immutables, prefer_const_constructors
import 'dart:convert';
import 'dart:io';
import 'dart:ui';
import 'package:date_time_format/date_time_format.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_quill/flutter_quill.dart' as q; 
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:onekitty/admin_screens/chama/view_chama.dart';
import 'package:onekitty/utils/responsive_size.dart';
import 'package:readmore/readmore.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
import 'package:onekitty/models/chama/chama_model.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/screens/dashboard/pages/chama/tabs/chama_services.dart';
import 'package:onekitty/screens/dashboard/pages/chama/viewing_single_chama/transactions/chama_transactions.dart';
import 'package:onekitty/screens/dashboard/pages/chama/viewing_single_chama/whatsapp/whatsapp_widget.dart';
import 'package:onekitty/utils/asset_urls.dart';
import 'package:onekitty/utils/custom_image_view.dart';
import 'package:onekitty/utils/custom_text_style.dart';
import 'package:onekitty/utils/formatted_currency.dart';
import 'package:onekitty/utils/size_config.dart';
import 'package:onekitty/utils/themes_colors.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:share_plus/share_plus.dart';

class ViewingSingleChama extends StatefulWidget {
  const ViewingSingleChama({super.key});

  @override
  State<ViewingSingleChama> createState() => _ViewingSingleChamaState();
}

class _ViewingSingleChamaState extends State<ViewingSingleChama> {
  final ChamaDataController chamaDataController =
      Get.put(ChamaDataController());

  final PageController _pageController =
      PageController(initialPage: 1, viewportFraction: 0.8);

  final ChamaController chamaController = Get.put(ChamaController());
  q.QuillController descr = q.QuillController.basic();

  List<String> circleImages = [
    AssetUrl.group6,
    AssetUrl.winter,
    AssetUrl.imgEllipse1,
  ];

  GlobalKey globalKey = GlobalKey();
  final RefreshController _refreshController =
      RefreshController(initialRefresh: true);

  void _onRefresh() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await chamaController.getAllChamaDetails(
          chamaId: chamaDataController.singleChamaDts.value.id ?? 0);
      chamaDataController.singleChamaDts.value =
          chamaController.chamaDetails.value;
      await chamaController.getBenficiaries(
          chamaId: chamaDataController.singleChamaDts.value.id ?? 0);
    });

    _refreshController.refreshCompleted();
  }

  @override
  void initState() {
    readDescr();
    super.initState();
  }

  void readDescr() {
    try {
      var jsonF = jsonDecode(
          chamaDataController.singleChamaDts.value.description ?? "");
      descr = q.QuillController(
        document: q.Document.fromJson(jsonF),
        selection: const TextSelection.collapsed(offset: 0),
      );
    } catch (e) {}
  }

  @override
  void dispose() {
    _pageController.dispose();
    _onRefresh();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: ThemeData.light(),
      child: SafeArea(
        child: Scaffold(
          backgroundColor: appTheme.gray50,
          //appBar: RowAppBar(), //buildAppBar(context),
          body: SmartRefresher(
            controller: _refreshController,
            onRefresh: _onRefresh,
            child: SingleChildScrollView(
              //padding: EdgeInsets.only(top: ResponsiveSize.width(context,5)),
              child: Column(
                children: [
                  Row(children: [
                    GestureDetector(
                      onTap: () => Navigator.pop(context),
                      child: Row(
                        children: [
                          IconButton(
                              onPressed: () {
                                Navigator.pop(context);
                              },
                              icon: const Icon(Icons.arrow_back)),
                          const Text("Back"),
                        ],
                      ),
                    ),
                    Spacer(),
                    IconButton(
                        icon: Icon(Icons.desktop_mac),
                        onPressed: () {
                          Get.to(() => ChamaDetailsPage(
                                chama: chamaDataController.singleChamaDts.value,
                              ));
                        }),
                  ]),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: ResponsiveSize.width(context,31)),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              chamaDataController.singleChamaDts.value.title ??
                                  "",
                              style: CustomTextStyles.labelMediumff545963,
                              overflow: TextOverflow.ellipsis,
                            ),
                            SizedBox(
                              width: ResponsiveSize.width(context,15),
                            ),
                          ],
                        ),
                        SizedBox(height: ResponsiveSize.height(context,8)),
                        Obx(() => Visibility(
                              visible: (chamaDataController
                                          .singleChamaDts.value.totaBal ??
                                      0.0) >
                                  0.0,
                              child: Column(
                                children: [
                                  Text(
                                      "Chama Balance: ${FormattedCurrency().getFormattedCurrency(chamaDataController.singleChamaDts.value.totaBal)}",
                                      style:
                                          CustomTextStyles.titleMediumSemiBold),
                                  // Text(
                                  //     "Penalty Balance: ${FormattedCurrency().getFormattedCurrency(chamaController.penaltyKittyBalance.value)}",
                                  //     style: CustomTextStyles.titleMediumSemiBold),
                                ],
                              ),
                            )),
                        Padding(
                          padding: EdgeInsets.only(
                            left: ResponsiveSize.width(context,4),
                            top: ResponsiveSize.height(context,4),
                          ),
                          child: Obx(
                            () => Visibility(
                              visible: chamaController.penaltyBal > 0,
                              child: RichText(
                                text: TextSpan(
                                  children: [
                                    TextSpan(
                                      text: "Paid Penalties: ",
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontStyle: FontStyle.italic,
                                        color: appTheme.gray90001,
                                      ),
                                    ),
                                    TextSpan(
                                      text: FormattedCurrency()
                                          .getFormattedCurrency(
                                        chamaController.penaltyBal,
                                      ),
                                      style: TextStyle(
                                        fontStyle: FontStyle.italic,
                                        color: appTheme.gray90001,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: ResponsiveSize.height(context,6)),
                        ReadMoreText(descr.document.toPlainText(),
                            trimMode: TrimMode.Line,
                            trimLines: 2,
                            trimCollapsedText: 'show more',
                            trimExpandedText: 'show less',
                            colorClickableText: ColorUtil.blueColor,
                            style: CustomTextStyles.bodySmallBluegray700),
                        buildShareIcons(context),
                        SizedBox(height: ResponsiveSize.height(context,6)),
                        _buildFrame(context),
                        SizedBox(height: ResponsiveSize.height(context,31)),
                        Align(
                          alignment: Alignment.topLeft,
                          child: Text(
                            "Next Beneficiary",
                            style: context.dividerTextLarge
                                ?.copyWith(fontSize: 15),
                          ),
                        ),
                        SizedBox(
                          height: ResponsiveSize.width(context,5),
                        ),
                        _buildFrame1(context),
                        SizedBox(height: ResponsiveSize.height(context,6)),
                        GetBuilder(builder: (ChamaController chamaController) {
                          if (chamaController.notifications.isEmpty) {
                            return SizedBox.shrink();
                          } else {
                            return _buildRow(context);
                          }
                        }),
                        SizedBox(height: ResponsiveSize.height(context,6)),
                        _buildFrame2(),
                        SizedBox(height: ResponsiveSize.height(context,10)),
                      ],
                    ),
                  ),
                  _buildTabs(context),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFrame(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Row(
          children: [
            Row(
              //mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                for (int i = 0; i < circleImages.length; i++)
                  Align(
                    widthFactor: 0.5,
                    child: CircleAvatar(
                      radius: 23,
                      backgroundColor: Colors.white,
                      child: CircleAvatar(
                        radius: 20,
                        backgroundImage: AssetImage(circleImages[i]),
                      ),
                    ),
                  )
              ],
            ),
            SizedBox(
              width: ResponsiveSize.width(context,10),
            ),
            //TODO 
            // Obx(() => Text("${chamaController.membersCount} Members"))
          ],
        ),
        SizedBox(height: ResponsiveSize.width(context,5)),
        Row(
          children: [
            CustomImageView(
                imagePath: AssetUrl.money, height: ResponsiveSize.height(context,20), width: ResponsiveSize.width(context,20)),
            SizedBox(
              width: ResponsiveSize.width(context,10),
            ),
            Text(
                "${FormattedCurrency().getFormattedCurrency(chamaDataController.chama.value.chama?.amount ?? 0)}/${chamaDataController.chama.value.chama?.frequency}")
          ],
        ),
        Row(
          children: [
            CustomImageView(
              color: Colors.black,
              imagePath: AssetUrl.penalty2,
              height: ResponsiveSize.height(context,14),
              width: ResponsiveSize.width(context,14),
            ),
            Padding(
              padding: EdgeInsets.only(
                left: ResponsiveSize.width(context,4),
                top: ResponsiveSize.height(context,4),
              ),
              child: Obx(
                () => Text(
                    "No. of penalties accured: ${chamaController.penaltyCount}"),
              ),
            ),
          ],
        ),
        SizedBox(height: ResponsiveSize.height(context,5)),
        Row(
          children: [
            CustomImageView(
              imagePath: AssetUrl.imgClock,
              height: ResponsiveSize.height(context,14),
              width: ResponsiveSize.width(context,14),
            ),
            Padding(
                padding: EdgeInsets.only(
                  left: ResponsiveSize.width(context,4),
                  top: ResponsiveSize.height(context,4),
                ),
                child: RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: DateTimeFormat.relative(
                            chamaDataController
                                    .singleChamaDts.value.nextOccurrence
                                    ?.toLocal() ??
                                DateTime.now(),
                            levelOfPrecision: 1,
                            prependIfBefore: 'Next cycle in',
                            ifNow: "Now",
                            appendIfAfter: "ago"),
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: appTheme.gray90001,
                        ),
                      ),
                    ],
                  ),
                )),
          ],
        ),
        Padding(
          padding: EdgeInsets.only(
            left: ResponsiveSize.width(context,20),
            top: ResponsiveSize.height(context,2),
            bottom: ResponsiveSize.height(context,3),
          ),
          child: RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: "Deadline: ",
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontStyle: FontStyle.italic,
                    color: appTheme.gray90001,
                  ),
                ),
                TextSpan(
                  text: DateFormat('MMM dd, yyyy').format(chamaDataController
                          .singleChamaDts.value.nextOccurrence
                          ?.toLocal() ??
                      DateTime.now()),
                  style: CustomTextStyles.bodySmallGray900,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRow(BuildContext context) {
    return Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
      Expanded(
        child: Text(
          "Connected WhatsApp groups:",
          style: TextStyle(fontSize: ResponsiveSize.height(context,13), color: appTheme.gray900),
        ),
      ),
      CustomImageView(
        imagePath: AssetUrl.addGif,
        height: ResponsiveSize.height(context,15),
        width: ResponsiveSize.width(context,15),
        // margin: EdgeInsets.only(bottom: ResponsiveSize.height(context,2)),
      ),
      // if (chamaDataController.chama.value.member?.role == "CHAIRPERSON")
      Padding(
          padding: EdgeInsets.only(left: ResponsiveSize.width(context,2)),
          child: InkWell(
              onTap: () {
                Get.toNamed(NavRoutes.addGrouplink);
              },
              child: Text("Add Group",
                  style: CustomTextStyles.titleSmallIndigo500)))
    ]);
  }

  Widget _buildTabs(BuildContext context) {
    return Column(
      children: [
        DefaultTabController(
            length: 2,
            child: Column(
              children: [
                Container(
                  margin: EdgeInsets.symmetric(horizontal: 12),
                  padding: EdgeInsets.all(ResponsiveSize.width(context,5)),
                  decoration: AppDecoration.fillSlate.copyWith(
                    borderRadius: BorderRadiusStyle.roundedBorder6,
                  ),
                  child: TabBar(
                    physics: const ClampingScrollPhysics(),
                    //padding: const EdgeInsets.only(left: 5, right: 5),
                    unselectedLabelColor: Colors.black,
                    labelColor: Theme.of(context).primaryColor,
                    indicatorSize: TabBarIndicatorSize.tab,
                    dividerColor: Colors.transparent,
                    indicator: BoxDecoration(
                        borderRadius: BorderRadius.circular(5),
                        color: Colors.white),
                    tabs: [
                      Tab(
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: ResponsiveSize.width(context,1)),
                          child: const Text("Services"),
                        ),
                      ),
                      Tab(
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: ResponsiveSize.width(context,1)),
                          child: const Text("Transactions"),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: ResponsiveSize.height(context,10),
                ),
                SizedBox(
                  height: ResponsiveSize.height(context,500) + 200,
                  child: Column(
                    children: [
                      Expanded(
                        child: TabBarView(
                          children: [
                            ChamaServicesWidget(chama: chamaDataController.singleChamaDts.value),
                            ChamaTransactionsPage(
                              isFullPage: false,
                              isFromMemberTransactions: false,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ))
      ],
    );
  }

  Widget _buildFrame1(BuildContext context) {
    return GetX(
      init: ChamaController(),
      initState: (state) {
        Future.delayed(Duration.zero, () async {
          try {
            // await state.controller?.getBenficiaries(
            //     chamaId: chamaDataController.singleChamaDts.value.id ?? 0);
            // chamaController.reset();
          } catch (e) {}
        });
      },
      builder: (ChamaController controller) {
        if (controller.isgetRes.isTrue) {
          return SizedBox(
            height: SizeConfig.screenHeight * .1,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SpinKitDualRing(
                    color: ColorUtil.blueColor,
                    lineWidth: ResponsiveSize.fontSize(context,4),
                    size: ResponsiveSize.fontSize(context,40),
                  ),
                  const Text(
                    "loading..",
                    style: TextStyle(
                      color: Colors.white,
                    ),
                  )
                ],
              ),
            ),
          );
        } else if (controller.beneficiaries.isEmpty) {
          return Text(
            "No Beneficiaries",
            style: context.dividerTextLarge,
            textAlign: TextAlign.center,
          );
        } else if (controller.beneficiaries.isNotEmpty) {
          return SizedBox(
            height: ResponsiveSize.height(context,100),
            child: PageView.builder(
              controller: _pageController,
              scrollDirection: Axis.horizontal,
              itemCount: controller.beneficiaries.length,
              itemBuilder: (context, index) {
                final beneficiary = controller.beneficiaries[index];
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 3.0),
                  child: InkWell(
                    onTap: () {
                      final dataController = Get.put(ChamaDataController());
                      dataController.singleBenf.value = NextBeneficiary(
                        beneficiaryPercentage:
                            beneficiary.beneficiaryPercentage,
                        amountToReceive: beneficiary.amountToReceive,
                        member: beneficiary.member,
                        beneficiaries: beneficiary.beneficiaries,
                      );
                      // if (dataController.chama.value.member!.id ==
                      //         beneficiary.member!.id ||
                      //     dataController.chama.value.member!.role ==
                      //         "CHAIRPERSON" ||
                      //     dataController.chama.value.member!.role ==
                      //         "TREASURER" ||
                      //     dataController.chama.value.member!.role ==
                      //         "SECRETARY") {
                      Get.toNamed(NavRoutes.beneficiary);
                      // }
                    },
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          padding: EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            color: appTheme.whiteA700,
                            border: Border.all(color: appTheme.gray200),
                          ),
                          child: Row(
                            children: [
                              CircleAvatar(
                                radius: 20,
                                backgroundImage:
                                    AssetImage(AssetUrl.imgEllipse1),
                              ),
                              SizedBox(
                                width: ResponsiveSize.width(context,7),
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    '${beneficiary.member!.firstName ?? ""} ${beneficiary.member!.secondName}',
                                    style: context.dividerTextLarge,
                                  ),
                                  Text(
                                    beneficiary.member!.role ?? "",
                                    style: context.dividerTextLarge
                                        ?.copyWith(color: appTheme.gray600),
                                  ),
                                  Text(
                                    FormattedCurrency().getFormattedCurrency(
                                        beneficiary.amountToReceive),
                                    style: context.dividerTextLarge
                                        ?.copyWith(color: appTheme.green800),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          );
        }

        return Text(
          "Some Issue occurred while loading",
          style: context.dividerTextLarge,
          textAlign: TextAlign.center,
        );
      },
    );
  }

  Widget _buildFrame2() {
    return GetX(
        init: ChamaController(),
        initState: (state) {
          Future.delayed(Duration.zero, () async {
            try {
              await state.controller?.getAllChamaDetails(
                  chamaId: chamaDataController.chama.value.chama?.id ?? 0);
            } catch (e) {}
          });
        },
        builder: (ChamaController chamaController) {
          if (chamaController.isGetAllChamaDetailsLoading.isTrue) {
            return SizedBox(
              height: SizeConfig.screenHeight * .1,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SpinKitDualRing(
                      color: ColorUtil.blueColor,
                      lineWidth: ResponsiveSize.fontSize(context,4),
                      size: ResponsiveSize.fontSize(context,40),
                    ),
                    const Text(
                      "loading..",
                      style: TextStyle(
                        color: Colors.white,
                      ),
                    )
                  ],
                ),
              ),
            );
          } else if (chamaController.notifications.isEmpty) {
            if (true /*chamaDataController.chama.value.member?.role == "CHAIRPERSON"*/) {
              return InkWell(
                  onTap: () {
                    Get.toNamed(NavRoutes.addGrouplink);
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "No whatsapp connected:",
                        style: context.dividerTextLarge,
                      ),
                      Text(
                        "Add Whatsapp",
                        style: context.dividerTextLarge
                            ?.copyWith(color: AppColors.blueButtonColor),
                      ),
                    ],
                  ));
            } else {
              return Text("No whatsapp groups connected");
            }
          } else if (chamaController.notifications.isNotEmpty) {
            return ListView.separated(
                shrinkWrap: true,
                itemBuilder: (context, index) {
                  final whatsapp = chamaController.notifications[index];
                  return ChamaWhatsappWidget(whatsapp: whatsapp);
                },
                separatorBuilder: (context, index) {
                  return SizedBox(
                    height: 5,
                  );
                },
                itemCount: chamaController.notifications.length);
          }
          return Text("An error occured");
        });
  }

  Widget buildShareIcons(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        InkWell(
          onTap: () async {
            String shareMsg =
                "Chama Title: ${chamaDataController.singleChamaDts.value.title ?? ""}\nClick: https://onekitty.co.ke/chama/${chamaDataController.singleChamaDts.value.kittyId ?? 0}\n to Contribute";
            await Share.share(shareMsg, subject: 'Chama Details');
          },
          child: Padding(
            padding: EdgeInsets.only(right: ResponsiveSize.width(context,40)),
            child: Container(
              padding: EdgeInsets.only(left: ResponsiveSize.width(context,15)),
              child: CustomImageView(
                height: ResponsiveSize.height(context,15),
                width: ResponsiveSize.width(context,15),
                imagePath: AssetUrl.share,
              ),
            ),
          ),
        ),
        InkWell(
          onTap: () {
            showModalBottomSheet(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10.0),
              ),
              context: context,
              isScrollControlled: true,
              constraints: BoxConstraints(
                maxHeight: SizeConfig.screenHeight * .8,
                maxWidth: SizeConfig.screenWidth,
              ),
              builder: (_) => SizedBox(
                height: SizeConfig.screenHeight * .7,
                width: SizeConfig.screenWidth,
                child: Column(
                  children: [
                    const SizedBox(
                      height: 10,
                    ),
                    Padding(
                      padding: const EdgeInsets.all(15.0),
                      child: RepaintBoundary(
                        key: globalKey,
                        child: Container(
                          margin: const EdgeInsets.all(20.0),
                          color: Colors.white,
                          child: Column(
                            children: [
                              const Text(
                                "Scan to contribute",
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 20,
                                ),
                              ),
                              QrImageView(
                                padding: const EdgeInsets.all(10.0),
                                data:
                                    "onekitty.co.ke/chama/${chamaDataController.singleChamaDts.value.kittyId}",
                                version: QrVersions.auto,
                                gapless: false,
                                errorCorrectionLevel: QrErrorCorrectLevel.H,
                                embeddedImage: AssetImage(AssetUrl.logo4),
                                embeddedImageStyle: const QrEmbeddedImageStyle(
                                  size: Size(80, 80),
                                ),
                                size: 250.0,
                              ),
                              const SizedBox(
                                height: 5.0,
                              ),
                              Text(
                                "  onekitty.co.ke/chama/${chamaDataController.singleChamaDts.value.kittyId} ",
                                style: const TextStyle(
                                  color: Colors.blue,
                                  fontSize: 16,
                                ),
                              ),
                              const SizedBox(height: 10),
                              Text(
                                chamaDataController
                                        .singleChamaDts.value.title ??
                                    "" "\n",
                                style: const TextStyle(fontSize: 17),
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                    Container(
                      margin: const EdgeInsets.only(right: 10.0),
                      child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor:
                                Theme.of(context).colorScheme.primary,
                            fixedSize: const Size(250, 20),
                          ),
                          child: Text(
                            "share".tr,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 15,
                            ),
                          ),
                          onPressed: () async {
                            _captureAndSharePng();
                          }),
                    ),
                  ],
                ),
              ),
            );
          },
          child: Padding(
            padding: EdgeInsets.only(right: ResponsiveSize.width(context,30)),
            child: QrImageView(
              data:
                  "https://onekitty.co.ke/chama/${chamaDataController.singleChamaDts.value.kittyId}",
              version: QrVersions.auto,
              gapless: false,
              size: 45,
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _captureAndSharePng() async {
    try {
      RenderRepaintBoundary boundary =
          globalKey.currentContext?.findRenderObject() as RenderRepaintBoundary;

      var image = await boundary.toImage(pixelRatio: 5.0);

      ByteData byteData =
          await image.toByteData(format: ImageByteFormat.png) as ByteData;
      Uint8List pngBytes = byteData.buffer.asUint8List();

      final tempDir = await getTemporaryDirectory();
      final file = await File('${tempDir.path}/image.png').create();
      await file.writeAsBytes(pngBytes);

      await Share.shareXFiles(
        [XFile(file.path)],
      );
    } catch (e) {
      if (kDebugMode) {
        print(e.toString());
      }
    }
  }
}
