


import 'dart:ui';

import 'package:flutter/widgets.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class ResponsiveSize {
  // Breakpoints for different screen sizes
  static const double _mobileBreakpoint = 600;
  static const double _tabletBreakpoint = 1200;
  static const double _desktopBreakpoint = 1440;

  // Base design sizes (adjust according to your design)
  static const double _baseWidth = 1440;
  static const double _baseHeight = 900;

  static void init() {
    // Initialize screenutil with base design size
    ScreenUtil.init(
      Get.context!,
      designSize: const Size(_baseWidth, _baseHeight),
      minTextAdapt: true,
    );
  }

  static double textSize(double desktopSize, double mobileSize) {
    final screenWidth = MediaQuery.of(Get.context!).size.width;
    
    if (screenWidth >= _desktopBreakpoint) {
      return _scaleText(desktopSize);
    } else if (screenWidth >= _tabletBreakpoint) {
      // Linear interpolation between desktop and mobile sizes
      final t = (screenWidth - _tabletBreakpoint) / 
               (_desktopBreakpoint - _tabletBreakpoint);
      return _scaleText(lerpDouble(mobileSize, desktopSize, t)!);
    } else if (screenWidth >= _mobileBreakpoint) {
      // Linear interpolation between mobile and tablet sizes
      final t = (screenWidth - _mobileBreakpoint) / 
               (_tabletBreakpoint - _mobileBreakpoint);
      return _scaleText(lerpDouble(mobileSize, desktopSize, t)!);
    } else {
      return _scaleText(mobileSize);
    }
  }

  static double _scaleText(double size) {
    // Use screenutil for text scaling with limits
    return size.sp.clamp(size * 0.8, size * 1.2);
  }

  // Additional responsive sizing methods
  static double responsiveWidth(double size) {
    return ScreenUtil().setWidth(size);
  }

  static double responsiveHeight(double size) {
    return ScreenUtil().setHeight(size);
  }

  static double responsiveRadius(double size) {
    return ScreenUtil().radius(size);
  }

  // Screen type detection
  static bool get isMobile => 
      MediaQuery.of(Get.context!).size.width < _mobileBreakpoint;

  static bool get isTablet => 
      MediaQuery.of(Get.context!).size.width >= _mobileBreakpoint && 
      MediaQuery.of(Get.context!).size.width < _tabletBreakpoint;

  static bool get isDesktop => 
      MediaQuery.of(Get.context!).size.width >= _desktopBreakpoint;
}


class ResponsiveLayout extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget desktop;

  const ResponsiveLayout({
    super.key,
    required this.mobile,
    this.tablet,
    required this.desktop,
  });

  // This size work fine for my design, maybe you need to customize it
  static const int mobileBreakpoint = 650;
  static const int tabletBreakpoint = 1200;

  static bool isMobile(BuildContext context) =>
      MediaQuery.of(context).size.width < mobileBreakpoint;

  static bool isTablet(BuildContext context) =>
      MediaQuery.of(context).size.width >= mobileBreakpoint &&
      MediaQuery.of(context).size.width < tabletBreakpoint;

  static bool isDesktop(BuildContext context) =>
      MediaQuery.of(context).size.width >= tabletBreakpoint;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= tabletBreakpoint) {
          return desktop;
        } else if (constraints.maxWidth >= mobileBreakpoint) {
          return tablet ?? desktop;
        } else {
          return mobile;
        }
      },
    );
  }
}

