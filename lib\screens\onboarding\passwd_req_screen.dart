import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get_storage_pro/get_storage_pro.dart';
import 'package:local_auth/local_auth.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/controllers/auth_controller.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart' show NavRoutes;
import 'package:onekitty/screens/onboarding/maintainance_page.dart';
import 'package:onekitty/screens/onboarding/login_screen.dart'; 
import 'package:onekitty/screens/widgets/text_form_field.dart';
import 'package:onekitty/services/auth_manager.dart';
import 'package:onekitty/utils/cache_keys.dart';
import 'package:onekitty/utils/my_button.dart';
import '../../utils/utils_exports.dart';

// ignore: must_be_immutable
class AuthPasswdScreen extends StatefulWidget {
  final bool? isForgot;

  const AuthPasswdScreen({
    super.key,
    this.isForgot,
  });

  @override
  State<AuthPasswdScreen> createState() => _AuthPasswdScreenState();
}

class _AuthPasswdScreenState extends State<AuthPasswdScreen> {
  final TextEditingController passwordController = TextEditingController();
  final AuthenticationController authenticationController = Get.find();

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool hidePassword = true;
  bool isFromStartPage = true;

  final LocalAuthentication auth = LocalAuthentication();
  final Logger logger = Get.find();
  final GetStorage box = Get.find();

  @override
  void initState() {
    if (Get.arguments != null) {
      isFromStartPage = Get.arguments[0];
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        body: LayoutBuilder(
          builder: (context, constraints) {
            // Check available width to decide layout
            if (constraints.maxWidth < 720) {
              // Mobile/Small Screen Layout
              return SingleChildScrollView(
                padding: EdgeInsets.symmetric(horizontal: 18.w, vertical: 30.h),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SizedBox(height: 50.h),
                      CustomImageView(
                        imagePath: AssetUrl.logo7,
                        height: 190.h,
                        width: 160.w,
                      ),
                      SizedBox(height: 20.h),
                      const Text("Enter your password to continue"),
                      SizedBox(height: 12.h),
                      CustomTextField(
                        prefixIcon: Icons.lock,
                        controller: passwordController,
                        obscureText: hidePassword,
                        suffixIcon: GestureDetector(
                          onTap: () {
                            setState(() {
                              hidePassword = !hidePassword;
                            });
                          },
                          child: hidePassword
                              ? const Icon(Icons.remove_red_eye)
                              : const Icon(Icons.visibility_off),
                        ),
                        hintText: "Password",
                        labelText: "Password",
                        isRequired: true,
                        validator: (value) {
                          if (value!.isEmpty) {
                            return "Password required";
                          }
                          return null;
                        },
                      ),
                      SizedBox(height: 14.h),
                      InkWell(
                        onTap: () {
                          AuthenticationManager authenticationManager = Get.find();
                          authenticationManager.logOut();
                          Get.offAll(
                            () => const LoginScreen(),
                            transition: Transition.leftToRightWithFade,
                          );
                        },
                        child: Text(
                          "Click here to log out",
                          style: TextStyle(
                            fontSize: 15.spMin,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      SizedBox(height: 20.h),
                      Obx(
                        () => MyButton(
                          showLoading: authenticationController.isLoginloading.isTrue,
                          onClick: () async {
                            if (_formKey.currentState!.validate()) {
                              await handleAuth(passwordController.text);
                            }
                          },
                          width: 250.w,
                          label: 'Continue',
                        ),
                      ),
                    ],
                  ),
                ),
              );
            } else {
              // Desktop/Large Screen Layout
              return Form(
                key: _formKey,
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 40.w, vertical: 20.h),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            const Text("Enter your password to continue"),
                            SizedBox(height: 12.h),
                            CustomTextField(
                              prefixIcon: Icons.lock,
                              controller: passwordController,
                              // onFieldSubmitted: (val) async {
                              //   if (_formKey.currentState!.validate()) {
                              //     await handleAuth(passwordController.text);
                              //   }
                              // },
                              obscureText: hidePassword,
                              suffixIcon: GestureDetector(
                                onTap: () {
                                  setState(() {
                                    hidePassword = !hidePassword;
                                  });
                                },
                                child: hidePassword
                                    ? const Icon(Icons.remove_red_eye)
                                    : const Icon(Icons.visibility_off),
                              ),
                              hintText: "Password",
                              labelText: "Password",
                              isRequired: true,
                              validator: (value) {
                                if (value!.isEmpty) {
                                  return "Password required";
                                }
                                return null;
                              },
                            ),
                            SizedBox(height: 14.h),
                            InkWell(
                              onTap: () {
                                AuthenticationManager authenticationManager = Get.find();
                                authenticationManager.logOut();
                                Get.offAll(
                                  () => const LoginScreen(),
                                  transition: Transition.leftToRightWithFade,
                                );
                              },
                              child: Text(
                                "Click here to log out",
                                style: TextStyle(
                                  fontSize: 15.spMin,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            SizedBox(height: 20.h),
                            Obx(
                              () => MyButton(
                                showLoading: authenticationController.isLoginloading.isTrue,
                                onClick: () async {
                                  if (_formKey.currentState!.validate()) {
                                    await handleAuth(passwordController.text);
                                  }
                                },
                                width: 250.w,
                                label: 'Continue',
                              ),
                            ),
                          ],
                        ),
                      ),
                      // Add a vertical divider between the two columns
                      VerticalDivider(
                        thickness: 1,
                        color: Colors.grey[300],
                        width: 40.w,
                      ),
                      Expanded(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CustomImageView(
                              imagePath: AssetUrl.logo7,
                              height: 190.h,
                              width: 160.w,
                              margin: EdgeInsets.only(left: 60.w),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }
          },
        ),
      ),
    );
  }

  handleAuth(String pass) async {
    final box = Get.find<GetStorage>();
    final user = box.read(CacheKeys.user);
    final bool maintainace = box.read(CacheKeys.maintainance);
    final res = await authenticationController.login(
      user["phone_number"].toString(),
      pass,
    );

    if (res) {
      if (maintainace) {
        Get.replace(const  MaintenancePage());
      } else {
        logger.d("Is from start page: $isFromStartPage");
        if (isFromStartPage) {
          
          Get.offAllNamed(NavRoutes.bottomNavSection);

        } else {
          Get.back(result: res);
        }
      }
    } else {
      Snack.show(
        res,
        authenticationController.apiMessage.string,
      );
    }
  }
}
