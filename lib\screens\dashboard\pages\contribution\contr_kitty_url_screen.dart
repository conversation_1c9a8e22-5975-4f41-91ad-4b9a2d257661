import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/helpers/show_snack_bar.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import '../../../../utils/utils_exports.dart';

// ignore: must_be_immutable
class EnteringUrlForAKittyScreen extends StatelessWidget {
  EnteringUrlForAKittyScreen({super.key});
  final ContributeController contributeController = Get.find();
  final kittyUrlController = TextEditingController();
  final greeting = getGreeting();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: Form(
        child: SingleChildScrollView(
          child: Container(
            width: double.maxFinite,
            padding: EdgeInsets.symmetric(
              horizontal: 30.w,
              vertical: 23.h,
            ),
            child: Column(
              children: [
                Align(
                  alignment: Alignment.centerLeft,
                  child: Padding(
                    padding: EdgeInsets.only(left: 2.w),
                    child: Row(
                      children: [
                        CustomImageView(
                          imagePath: AssetUrl.imgEllipse1,
                          height: 44.h,
                          width: 44.w,
                          radius: BorderRadius.circular(
                            22.w,
                          ),
                        ),
                        Container(
                          width: 106.w,
                          margin: EdgeInsets.only(left: 6.w),
                          child: Text(
                            greeting,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: CustomTextStyles.titleSmallGray900,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 5.h),
                SizedBox(
                  height: 180.h,
                  width: 314.w,
                  child: CustomImageView(
                    imagePath: AssetUrl.imgGroup6,
                    height: 159.h,
                    width: 215.w,
                    alignment: Alignment.bottomCenter,
                  ),
                ),
                SizedBox(height: 33.h),
                Text(
                  "Contribute to a kitty",
                  style: CustomTextStyles.titleLargeBlack900,
                ),
                SizedBox(height: 10.h),
                Container(
                  width: 347.w,
                  margin: EdgeInsets.only(
                    left: 8.w,
                    right: 13.w,
                  ),
                  child: Text(
                    "Join hands today and be part of something special",
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.center,
                    style: CustomTextStyles.bodyLargePoppinsGray600,
                  ),
                ),
                SizedBox(height: 29.h),
                _buildEnteringUrlForLink(context),
                SizedBox(height: 5.h),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Section Widget
  Widget _buildEnteringUrlForLink(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(right: 4.w),
      padding: EdgeInsets.all(12.w),
      decoration: AppDecoration.shadow1.copyWith(
        borderRadius: BorderRadiusStyle.roundedBorder6,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Kitty url or Kitty ID",
                style: CustomTextStyles.titleSmallGray900,
              ),
              SizedBox(height: 2.h),
              CustomTextFormField(
                autofocus: false,
                controller: kittyUrlController,
                hintText: "https://onekitty.co.ke/kitty/674/",
                textInputAction: TextInputAction.done,
              ),
            ],
          ),
          SizedBox(height: 24.h),
          Obx(
            () => CustomKtButton(
              isLoading: contributeController.isgetkittyloading.isTrue,
              onPress: () async {
                final kittId = kittyUrlController.text;
                int?
                    id; // Declare id variable outside if block to make it accessible later

                if (!RegExp(r'^\d+$').hasMatch(kittId)) {
                  // Extract ID from URL if it consists of letters and symbols
                  id = extractUrlId(kittId);
                } else {
                  // If the input is already an ID, use it directly
                  id = int.tryParse(kittId);
                }

                final res = await contributeController.getKitty(id: id);
                if (res && contributeController.kittyType.value == 0) {
                  Snack.showInfo(
                      message1: contributeController.apiMessage.string);
                  Navigator.pushNamed(context, NavRoutes.contibuteScreen);
                }
                if (res && contributeController.kittyType.value == 4) {
                  Get.toNamed(NavRoutes.chamaContribute);
                }
              },
              height: 44.h,
              btnText: "Submit",
            ),
          ),
        ],
      ),
    );
  }
}

int? extractUrlId(String url) {
  final RegExp regExp = RegExp(r'(?<=\/)\d+(?=\/?$)');
  final Match? match = regExp.firstMatch(url);
  int? id;
  if (match != null) {
    id = int.tryParse(match.group(0) ?? '');
  }
  return id;
}

String getGreeting() {
  var hour = DateTime.now().hour;
  if (hour < 12) {
    return "Good morning";
  } else if (hour < 18) {
    return "Good afternoon";
  } else {
    return "Good evening";
  }
}
