import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/admin/chama/chama_admin_controller.dart';
import 'package:onekitty/models/admin/chama_occurence_model.dart';
import 'package:onekitty/models/chama/chama_model.dart';
import 'package:onekitty/utils/my_button.dart';

import 'widgets/table_footer.dart';

class ChamaOccurence extends StatefulWidget {
  final Chama chama;
  const ChamaOccurence({super.key, required this.chama});
  @override
  State<ChamaOccurence> createState() => _ChamaOccurenceState();
}

class _ChamaOccurenceState extends State<ChamaOccurence> {
  final controller = Get.find<ChamaAdminController>();
  @override
  void initState() {
    controller.clearInvoice();
    onRefresh();
    super.initState();
  }

  void onRefresh() {
    controller.getChamaOccurence(widget.chama.id!);
  }

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ChamaAdminController>();
    final chamaIdController = TextEditingController(),
        cycleCountController = TextEditingController(),
        memberIdController = TextEditingController();
    final status = 'Paid'.obs;
    final filter = '';
    return Scaffold(
        appBar: AppBar(
          title: const Text('Chama Occurence'),
        ),
        body: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  TextField(
                    decoration: const InputDecoration(
                      labelText: 'Cycle Count',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    controller: cycleCountController,
                  ),
                  const SizedBox(height: 10),
                  TextField(
                    decoration: const InputDecoration(
                      labelText: 'Member ID',
                      border: OutlineInputBorder(),
                    ),
                    controller: memberIdController,
                  ),
                  const SizedBox(height: 10),
                  DropdownButtonFormField<String>(
                    decoration: const InputDecoration(
                      labelText: 'Status',
                      border: OutlineInputBorder(),
                    ),
                    items: ['Pending', 'Paid', 'Overpaid', 'Underpaid']
                        .map((status) => DropdownMenuItem(
                              value: status,
                              child: Text(status),
                            ))
                        .toList(),
                    onChanged: (value) {
                      status(value);
                    },
                  ),
                  const SizedBox(height: 20),
                  ElevatedButton(
                    child: const Text('Apply Filters'),
                    onPressed: () {
                      final filters =
                          'cycle_count=${cycleCountController.text}&member_id=${memberIdController.text}&status=${status.value.toUpperCase()}';
                      controller.getChamaOccurence(widget.chama.id!,
                          filters: filters);
                    },
                  ),
                ],
              ),
            ),
            Obx(() => controller.isLoading.value
                ? const Center(
                    child: CircularProgressIndicator(),
                  )
                : Expanded(child: ChamaOccurenceTable(chama: widget.chama))),
          ],
        ));
  }
}

class ChamaOccurenceTable extends StatefulWidget {
  final Chama chama;
  const ChamaOccurenceTable({super.key, required this.chama});

  @override
  _ChamaTableState createState() => _ChamaTableState();
}

class _ChamaTableState extends State<ChamaOccurenceTable> {
  int? _sortColumnIndex;
  bool _sortAscending = true;
  final controller = Get.find<ChamaAdminController>();

  void _sort<T>(Comparable<T> Function(ChamaOcurenceModel chama) getField,
      int columnIndex, bool ascending) {
    _sortColumnIndex = columnIndex;
    _sortAscending = ascending;
    controller.chamaOcurences.sort((a, b) {
      final aValue = getField(a);
      final bValue = getField(b);
      return ascending
          ? Comparable.compare(aValue, bValue)
          : Comparable.compare(bValue, aValue);
    });
    controller.update();
  }

  void onRefresh() {
    controller.getChamaOccurence(widget.chama.id!);
  }

  void onRowsPerPageChanged(int value) {
    controller.size.value = value;

    controller.fetchAllChamas(0);
  }

  void onPreviousPage() {
    if (!controller.isFirst.value) {
      controller.fetchAllChamas(controller.currentPage.value - 1);
    }
  }

  void onNextPage() {
    if (!controller.isLast.value) {
      controller.fetchAllChamas(controller.currentPage.value + 1);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: Obx(
            () => DataTable2(
              columnSpacing: 12,
              horizontalMargin: 12,
              minWidth: 600,
              sortColumnIndex: _sortColumnIndex,
              sortAscending: _sortAscending,
              columns: [
                DataColumn2(
                  label: const Text('ID'),
                  size: ColumnSize.S,
                  onSort: (columnIndex, ascending) {
                    _sort<num>((chama) => chama.id, columnIndex, ascending);
                  },
                ),
                DataColumn2(
                  label: const Text('Created At'),
                  onSort: (columnIndex, ascending) {
                    _sort<DateTime>(
                        (chama) => chama.createdAt ?? DateTime.now(),
                        columnIndex,
                        ascending);
                  },
                ),
                DataColumn2(
                  label: const Text('Updated At'),
                  onSort: (columnIndex, ascending) {
                    _sort<DateTime>(
                        (chama) => chama.updatedAt ?? DateTime.now(),
                        columnIndex,
                        ascending);
                  },
                ),
                DataColumn2(
                  label: const Text('Start Date'),
                  onSort: (columnIndex, ascending) {
                    _sort<DateTime>(
                        (chama) => chama.startDate ?? DateTime.now(),
                        columnIndex,
                        ascending);
                  },
                ),
                DataColumn2(
                  label: const Text('End Date'),
                  onSort: (columnIndex, ascending) {
                    _sort<DateTime>((chama) => chama.endDate ?? DateTime.now(),
                        columnIndex, ascending);
                  },
                ),
                DataColumn2(
                  label: const Text('Response'),
                  size: ColumnSize.L,
                  onSort: (columnIndex, ascending) {
                    _sort<String>(
                        (chama) => chama.response, columnIndex, ascending);
                  },
                ),
                DataColumn2(
                  label: const Text('Total Beneficiaries Amount'),
                  size: ColumnSize.M,
                  onSort: (columnIndex, ascending) {
                    _sort<num>((chama) => chama.totalBenfsAmount, columnIndex,
                        ascending);
                  },
                ),
                DataColumn2(
                  label: const Text('Amount'),
                  size: ColumnSize.M,
                  onSort: (columnIndex, ascending) {
                    _sort<num>((chama) => chama.amount, columnIndex, ascending);
                  },
                ),
                DataColumn2(
                  label: const Text('Chama Balance'),
                  size: ColumnSize.M,
                  onSort: (columnIndex, ascending) {
                    _sort<num>(
                        (chama) => chama.chamaBalance, columnIndex, ascending);
                  },
                ),
                DataColumn2(
                  label: const Text('Percentage Beneficiary'),
                  size: ColumnSize.S,
                  onSort: (columnIndex, ascending) {
                    _sort<num>(
                        (chama) => chama.percentageBen, columnIndex, ascending);
                  },
                ),
                DataColumn2(
                  label: const Text('Percentage Chama'),
                  size: ColumnSize.S,
                  onSort: (columnIndex, ascending) {
                    _sort<num>((chama) => chama.percentageChama, columnIndex,
                        ascending);
                  },
                ),
                const DataColumn2(
                  label: Text('Actions'),
                  size: ColumnSize.S,
                ),
              ],
              rows: controller.chamaOcurences
                  .map((chama) => DataRow2(cells: [
                        DataCell(Text(chama.id.toString())),
                        DataCell(Text(DateFormat('yyyy-MM-dd')
                            .format(chama.createdAt ?? DateTime.now()))),
                        DataCell(Text(DateFormat('yyyy-MM-dd')
                            .format(chama.updatedAt ?? DateTime.now()))),
                        DataCell(Text(DateFormat('yyyy-MM-dd')
                            .format(chama.startDate ?? DateTime.now()))),
                        DataCell(Text(DateFormat('yyyy-MM-dd')
                            .format(chama.endDate ?? DateTime.now()))),
                        DataCell(Text(chama.response)),
                        DataCell(Text(chama.totalBenfsAmount.toString())),
                        DataCell(Text(chama.amount.toString())),
                        DataCell(Text(chama.chamaBalance.toString())),
                        DataCell(
                            Text('${chama.percentageBen.toStringAsFixed(2)}%')),
                        DataCell(Text(
                            '${chama.percentageChama.toStringAsFixed(2)}%')),
                        DataCell(TextButton.icon(
                            onPressed: () =>
                                Get.dialog(DetailPage(chamaOccurrence: chama)),
                            icon: const Icon(Icons.launch),
                            label: const Text('View'))),
                      ]))
                  .toList(),
            ),
          ),
        ),
        TablePaginationFooter(
          currentPage: controller.currentPage.value,
          totalPages: controller.totalPages.value,
          rowsPerPage: controller.size.value,
          availableRowsPerPage: const [15, 30, 50, 100],
          onRowsPerPageChanged: onRowsPerPageChanged,
          onRefresh: onRefresh,
          onPreviousPage: onPreviousPage,
          onNextPage: onNextPage,
        ),
      ],
    );
  }
}

class DetailPage extends StatelessWidget {
  final ChamaOcurenceModel chamaOccurrence;

  const DetailPage({super.key, required this.chamaOccurrence});

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: const BoxConstraints(maxWidth: 600, maxHeight: 800),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Card(
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Text(
                    'Details for Chama: ${chamaOccurrence.chamaId}',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                ),
                const SizedBox(height: 16),
                _buildDetailRow(
                    'Created At', _formatDate(chamaOccurrence.createdAt)),
                _buildDetailRow(
                    'Updated At', _formatDate(chamaOccurrence.updatedAt)),
                _buildDetailRow(
                    'Start Date', _formatDate(chamaOccurrence.startDate)),
                _buildDetailRow(
                    'End Date', _formatDate(chamaOccurrence.endDate)),
                _buildDetailRow('Response', chamaOccurrence.response),
                _buildDetailRow(
                    'Is Error', chamaOccurrence.isError ? 'Yes' : 'No'),
                _buildDetailRow('Total Beneficiaries Amount',
                    chamaOccurrence.totalBenfsAmount.toString()),
                _buildDetailRow('Amount', chamaOccurrence.amount.toString()),
                _buildDetailRow('Chama Saved Amount',
                    chamaOccurrence.chamaSavedAmount.toString()),
                _buildDetailRow(
                    'Chama Balance', chamaOccurrence.chamaBalance.toString()),
                _buildDetailRow('Response Code', chamaOccurrence.responseCode),
                _buildDetailRow(
                    'Member ID', chamaOccurrence.memberId.toString()),
                _buildDetailRow('Percentage Beneficiary',
                    '${chamaOccurrence.percentageBen.toStringAsFixed(2)}%'),
                _buildDetailRow('Percentage Chama',
                    '${chamaOccurrence.percentageChama.toStringAsFixed(2)}%'),
                if (chamaOccurrence.chamaBeneficiary != null) ...[
                  const Divider(),
                  Text(
                    'Beneficiary Details',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  _buildDetailRow('Beneficiary Title',
                      chamaOccurrence.chamaBeneficiary!.title),
                  _buildDetailRow('Beneficiary Type',
                      chamaOccurrence.chamaBeneficiary!.beneficiaryType),
                  _buildDetailRow('Account Number',
                      chamaOccurrence.chamaBeneficiary!.accountNumber),
                  _buildDetailRow('Channel Name',
                      chamaOccurrence.chamaBeneficiary!.channelName),
                  _buildDetailRow('Percentage',
                      '${chamaOccurrence.chamaBeneficiary!.percentage.toStringAsFixed(2)}%'),
                ],
                MyButton(
                  width: 150.w,
                  showLoading: false,
                  onClick: () => Get.back(),
                  label: 'Close',
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$label: ',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          Expanded(
            child: Text(value,
                style: const TextStyle(fontWeight: FontWeight.w500)),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime? date) {
    return date != null ? DateFormat('yyyy-MM-dd').format(date) : 'N/A';
  }
}
