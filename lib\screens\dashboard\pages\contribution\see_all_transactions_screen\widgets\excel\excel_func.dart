import 'dart:io';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/controllers/chama/chama_controller.dart';
import 'package:onekitty/controllers/events/view_single_event.dart';
import 'package:onekitty/controllers/kitty_controller.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:path_provider/path_provider.dart';
import 'package:syncfusion_flutter_xlsio/xlsio.dart' as xlsio;

Future<String> createExcel(
    {required bool isKitty, List<dynamic>? transactions, int? eventId}) async {
  final xlsio.Workbook workbook = xlsio.Workbook();
  final xlsio.Worksheet sheet = workbook.worksheets[0];
  final KittyController kittyController = Get.put(KittyController());
  final ChamaController chamaController = Get.put(ChamaController());
  final DataController _dataController = Get.put(DataController());
  final ChamaDataController dataController = Get.put(ChamaDataController());
  final event = eventId != null
      ? Get.find<ViewSingleEventController>().event.value
      : null;
  final xlsio.Range titleRange = sheet.getRangeByName('A1:E1');
  titleRange.merge();
  isKitty
      ? titleRange.setText(
          '${event?.title ?? _dataController.kitty.value.kitty!.title ?? ""} Transaction Report')
      : titleRange.setText(
          '${dataController.singleChamaDts.value.title ?? ""} Transaction Report');
  titleRange.cellStyle.bold = true;
  titleRange.cellStyle.fontSize = 14;
  titleRange.cellStyle.hAlign = xlsio.HAlignType.center;

  sheet.getRangeByName('A2').setText('Date');
  sheet.getRangeByName('B2').setText('Details');
  sheet.getRangeByName('C2').setText('Amount');
  sheet.getRangeByName('D2').setText('Reference');

  sheet.getRangeByName('E2').setText('Type');

  if (isKitty) {
    transactions = kittyController.transactionsKitty.toList();
  } else {
    transactions = chamaController.chamaTransactions.toList();
  }
  final dateformat = DateFormat('EE, dd MMMM h:mm a');

  DateTime recent = transactions.first.createdAt!;
  DateTime nrecent = transactions.first.createdAt!;

  for (var transaction in transactions) {
    if (transaction.createdAt!.isAfter(recent)) {
      recent = transaction.createdAt!;
    }
    if (transaction.createdAt!.isBefore(nrecent)) {
      nrecent = transaction.createdAt!;
    }
  }
  for (int i = 0; i < transactions.length; i++) {
    sheet.getRangeByName('A${i + 3}').setText(
          dateformat.format(transactions[i].createdAt!.toLocal()),
        );
    isKitty
        ? sheet.getRangeByName('B${i + 3}').setText(
            '${transactions[i].firstName ?? ""} ${transactions[i].secondName ?? ''}__${transactions[i].phoneNumber ?? ''}')
        : sheet.getRangeByName('B${i + 3}').setText(
            '${transactions[i].firstName ?? ""} ${transactions[i].secondName ?? ''}__${transactions[i].phoneNumber ?? ''}');
    sheet
        .getRangeByName('C${i + 3}')
        .setText(transactions[i].amount.toString());
    sheet
        .getRangeByName('D${i + 3}')
        .setText(transactions[i].transactionCode?.toString() ?? '');
    sheet.getRangeByName('E${i + 3}').setText(transactions[i].typeInOut);
  }
  for (int col = 1; col <= 5; col++) {
    sheet.autoFitColumn(col);
  }
  sheet.protect('@OneKitty');

  final List<int> bytes = workbook.saveAsStream();
  workbook.dispose();

  final String path = (await getApplicationSupportDirectory()).path;
  final String filename = isKitty
      ? '$path/${event?.title ?? _dataController.kitty.value.kitty?.title ?? ''}*${DateFormat('MMM dd').format(nrecent.toLocal())}_${DateFormat('MMM dd').format(recent.toLocal())}*${event != null ? 'Event' : 'Kitty'}Transactions.xlsx'
      : '$path/${dataController.singleChamaDts.value.title}*${DateFormat('MMM dd').format(nrecent.toLocal())}_${DateFormat('MMM dd').format(recent.toLocal())}*ChamaTransactions.xlsx';
  final File file = File(filename);
  await file.writeAsBytes(bytes, flush: true);

  return filename;
}

Future<String> userExcel({List<dynamic>? transactions}) async {
  final xlsio.Workbook workbook = xlsio.Workbook();
  final xlsio.Worksheet sheet = workbook.worksheets[0];
  final UserKittyController userController = Get.put(UserKittyController());

  final xlsio.Range titleRange = sheet.getRangeByName('A1:E1');
  titleRange.merge();
  titleRange.setText('Transaction Report');

  titleRange.cellStyle.bold = true;
  titleRange.cellStyle.fontSize = 14;
  titleRange.cellStyle.hAlign = xlsio.HAlignType.center;

  sheet.getRangeByName('A2').setText('Date');

  sheet.getRangeByName('B2').setText('Product');
  sheet.getRangeByName('C2').setText('Amount');
  sheet.getRangeByName('D2').setText('Reference');
  sheet.getRangeByName('E2').setText('Type');

  transactions = userController.alltransactions.toList();
  final dateformat = DateFormat('EE, dd MMMM h:mm a');

  DateTime recent = transactions.first.createdAt!;
  DateTime nrecent = transactions.first.createdAt!;

  for (var transaction in transactions) {
    if (transaction.createdAt!.isAfter(recent)) {
      recent = transaction.createdAt!;
    }
    if (transaction.createdAt!.isBefore(nrecent)) {
      nrecent = transaction.createdAt!;
    }
  }
  for (int i = 0; i < transactions.length; i++) {
    sheet
        .getRangeByName('A${i + 3}')
        .setText(dateformat.format(transactions[i].createdAt!.toLocal()));

    sheet.getRangeByName('B${i + 3}').setText(transactions[i].product);

    sheet
        .getRangeByName('C${i + 3}')
        .setText(transactions[i].amount.toString());
    sheet
        .getRangeByName('D${i + 3}')
        .setText(transactions[i].transactionCode.toString());
    sheet.getRangeByName('E${i + 3}').setText(transactions[i].typeInOut);
  }
  for (int col = 1; col <= 5; col++) {
    sheet.autoFitColumn(col);
  }
  sheet.protect('@OneKitty');

  final List<int> bytes = workbook.saveAsStream();
  workbook.dispose();

  final String path = (await getApplicationSupportDirectory()).path;
  final String filename =
      '$path/*${DateFormat('MMM dd').format(nrecent.toLocal())}_${DateFormat('MMM dd').format(recent.toLocal())}*Transactions.xlsx';

  final File file = File(filename);
  await file.writeAsBytes(bytes, flush: true);

  return filename;
}
