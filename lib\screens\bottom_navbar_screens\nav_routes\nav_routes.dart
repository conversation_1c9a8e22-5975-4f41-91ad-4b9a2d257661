// ignore_for_file: prefer_const_constructors

import 'package:flutter/material.dart';
import 'package:onekitty/screens/dashboard/pages/chama/chama_steps.dart';
import 'package:onekitty/screens/dashboard/pages/chama/contribute/contribute.dart';
import 'package:onekitty/screens/dashboard/pages/chama/meetings/meetings.dart';
import 'package:onekitty/screens/dashboard/pages/chama/members/penalize_members.dart';
import 'package:onekitty/screens/dashboard/pages/chama/penalties/penalties.dart';
import 'package:onekitty/screens/dashboard/pages/chama/resources/resources.dart';
import 'package:onekitty/screens/dashboard/pages/chama/settings/settings.dart';
import 'package:onekitty/screens/dashboard/pages/chama/signatories/add_signatories.dart';
import 'package:onekitty/screens/dashboard/pages/chama/signatories/sig_transactions.dart';
import 'package:onekitty/screens/dashboard/pages/chama/signatories/signatories.dart';
import 'package:onekitty/screens/dashboard/pages/chama/signatories/sig_tra.dart';
import 'package:onekitty/screens/dashboard/pages/chama/members/beneficiary.dart';
import 'package:onekitty/screens/dashboard/pages/chama/members/invite.dart';
import 'package:onekitty/screens/dashboard/pages/chama/members/operations.dart';
import 'package:onekitty/screens/dashboard/pages/chama/members/members_order.dart';
import 'package:onekitty/screens/dashboard/pages/chama/resources/updateR.dart';
import 'package:onekitty/screens/dashboard/pages/chama/tabs/add_group.dart';
import 'package:onekitty/screens/dashboard/pages/chama/tabs/update_chama.dart';
import 'package:onekitty/screens/dashboard/pages/chama/resources/upload_resource.dart';
import 'package:onekitty/screens/dashboard/pages/chama/viewing_single_chama/viewing_single_chama.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/contr_error_screen.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/contr_kitty_url_screen.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/contributing_ktty_screen.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/contribution_kitties/contr_kitties_screen.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/see_all_transactions_screen/see_all_transactions_screen.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/sucess_contr_screen.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/viewing_single_kitty/viewing_single_kitty_page.dart';
import 'package:onekitty/screens/dashboard/pages/events/create_event/create_event_page.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/all_sms_screen.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/withdraw/withdaraw_sucess.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/withdraw/withdraw_error.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/empty_sms_screen.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/single_sms.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/success_screen.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/entry_page.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/all_user_transactions.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/tawk.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/topUp_otp.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/top_up_dialog.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/create_kitty/pages/create_kitty.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/create_kitty/pages/error_page.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/edit_kitty/end_date.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/edit_kitty/step1.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/edit_kitty/whatsapp_link.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/widgets/success.dart';
import 'package:onekitty/screens/dashboard/pages/profile/edit_profile.dart';
import 'package:onekitty/screens/dashboard/pages/profile/merchant/referrals.dart';
import 'package:onekitty/screens/dashboard/pages/profile/merchant/transactions.dart';
import 'package:onekitty/screens/dashboard/pages/profile/profile_page.dart';

import '../../dashboard/pages/events/events_page.dart';
import '../botton_navigation_section/bottom_nav_section.dart';
import '../../dashboard/pages/home/<USER>';

class NavRoutes {
  static const String eventsPage = '/events';
  static const String createEvent = '/createEvent';

  static const String homeScreen = '/home_screen';

  static const String crtContributionKittyPage = '/crt_contribution_kitty_page';

  static const String bottomNavSection = '/bottom_nav_screen';

  static const String urlScreen = '/enter_url_screen';
  static const String contibuteScreen = '/contribute_screen';
  static const String contrSuccessScreen = '/success_Screen';
  static const String contrErrScreen = '/error_screen';
  static const String myKittiescontribtionScreen = '/my_Kitties_screen';
  static const String createkittyScreen = '/create_kitty_screen';
  static const String viewingsinglekittyScreen = '/view_single_kitty';
  static const String seeAlltranscScreen = '/see_all_transc';
  static const String editEndDatekittyScreen = '/edit_kitty_end_date';
  //static const String confirmPayscreen = '/confirmpayment';
  static const String editKittyscreen = '/editKitty';
  static const String emptyblksmsScreen = '/emptyblksms';
  static const String crtsmsScreen = '/crtsms';
  static const String usertransactions = '/usertransactions';
  //static const String cardpayment = '/cardpayment';

  static const String withdrawPage = '/withdraw/withdraw.dart';
  static const String whatsappErrorPage = '/error_page';
  static const String chatRoom = '/chatbot/chatbot.dart';
  static const String addWhatsappGroup = '/edit_kitty/whatsapp_link.dart';
  static const String tawk = '/tawk/tawk.dart';

  //bulks sms
  static const String selectcontacts = '/selectcontacts';
  static const String withdrawSuccessPage = '/withdraw/withdaraw_sucess';
  static const String withdrawErrorPage = '/withdraw/withdraw_error';
  static const String SmSsent = '/smssuccess';
  static const String mainbulksms = '/bulksmspage';
  static const String singlebulksms = '/singlesmspage';
  static const String topup = '/topup';
  static const String topupOtp = '/topupOTP';
  static const String createSuccess = '/create/success_page';
  static const String chamaTransactionsPage = '/chama/transactions';

  //chama Routes
  static const String chamaStepper = '/crtChamaSteps';
  static const String updateChama = '/updateChama';
  static const String resourcesView = '/reources';
  static const String uploadResource = '/uploadRsrc';
  static const String members = '/members';
  static const String singleChama = '/singlechama';
  static const String addGrouplink = '/grouplink';
  static const String penalties = '/penalties';
  static const String meetings = '/meetings';
  static const String signatories = '/signatories';
  static const String settings = '/settings';
  static const String signatoryTransactiions = '/signatoryTransactiions';
  static const String mainTransactions = '/mainTransactions';
  static const String penalizeMultiple = '/penalizeMultiple';
  static const String viewingSingleChama = '/viewingSingleChama';
  static const String chamaSettings = '/chamaSettings';
  static const String operation = '/operations';
  static const String invite = '/inviteMembers';
  static const String beneficiary = '/benf';
  static const String updateRs = '/updateRs';
  static const String addSignatory = '/addSignatoty';
  static const String profile = '/profile';
  static const String updateProfile = '/updateProfile';
  static const String chamaContribute = '/chamaContribute';

//Refferals
  static const String referrals = '/referrals';
  static const String mrntTransac = '/merchantsTransactions';

  static Map<String, WidgetBuilder> routes = {
    createEvent: (context) => CreateEventPage(),
    eventsPage: (context) => EventsPage(),
    homeScreen: (context) => HomeScreen(),
    bottomNavSection: (context) => BottomNavSection(),
    urlScreen: (context) => EnteringUrlForAKittyScreen(),
    contibuteScreen: (context) => const ContributingToAKitty(),
    contrSuccessScreen: (context) => SuccessfulContibutionPage(),
    contrErrScreen: (context) => ContibutionErrorScreen(),
    myKittiescontribtionScreen: (context) => MyKittiesScreen(),
    viewingsinglekittyScreen: (context) => ViewingSingleKittyScreen(),
    seeAlltranscScreen: (context) => AllTransactionsScreen(),
    createkittyScreen: (context) => StepOne(),
    editEndDatekittyScreen: (context) => EndDate(),
    //confirmPayscreen: (context) => ProcessPaymentOtp(),
    editKittyscreen: (context) => EditKittyDetails(),
    emptyblksmsScreen: (context) => EmptyBulkSmsScreen(),
    // crtsmsScreen: (context) => CrtSmsScreen(),
    usertransactions: (context) => UserTransactionsScreen(),
    whatsappErrorPage: (context) => ErrorStepperPage(),
    chatRoom: (context) => ChatbotEntryPage(),
    addWhatsappGroup: (context) => WhatsAppEditLink(),
    tawk: (context) => TawkWidget(),
    //cardpayment: (context) => CardPayment(),
    // selectcontacts: (context) => SelectContactsScreen(),
    withdrawSuccessPage: (context) => WithdrawSuccess(),
    withdrawErrorPage: (context) => WithdrawError(),
    SmSsent: (context) => SmsSentScreen(),
    mainbulksms: (context) => AllSmsScreen(),
    singlebulksms: (context) => SingleSmsScreen(),
    topup: (context) => TopUp(),
    topupOtp: (context) => TopUpOtp(),
    createSuccess: (context) => SucessPage(),
    // chamaTransactionsPage: (context) => ChamaTransactionsPage(),
    chamaStepper: (context) => ChamaStepper(),
    updateChama: (context) => UpdateChama(),
    resourcesView: (context) => ResourcesWidget(),
    uploadResource: (context) => UploadResource(),
    singleChama: (context) => ViewingSingleChama(),
    addGrouplink: (context) => AddGroup(),
    penalties: (context) => PenaltiesPage(),
    meetings: (context) => MeetingsPage(),
    signatories: (context) => Signatories(),
    settings: (context) => ChamaSettings(),
    signatoryTransactiions: (context) => SignatoryTransactions(),
    mainTransactions: (context) => SignatoryInitiateTransactions(),
    penalizeMultiple: (context) => ChamaMembersPage(),
    viewingSingleChama: (context) => ViewingSingleChama(),
    chamaSettings: (context) => ChamaSettings(),
    members: (context) => Operations(),
    operation: (context) => MembersOrder(),
    invite: (context) => Invite(),
    beneficiary: (context) => Beneficiary(),
    updateRs: (context) => UpdateResource(),
    addSignatory: (context) => AddSignatories(),
    profile: (context) => ProfilePage(),
    updateProfile: (context) => EditProfilePage(),
    chamaContribute: (context) => ChamaContributePage(),
    referrals: (context) => Referrals(),
    mrntTransac: (context) => Transactions(),
  };
}
