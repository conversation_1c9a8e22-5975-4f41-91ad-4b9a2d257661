import 'package:contacts_service/contacts_service.dart';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl_phone_number_input/intl_phone_number_input.dart';
import 'package:onekitty/controllers/events/controllers.dart';
import 'package:onekitty/controllers/events/invite_users_controller.dart';
import 'package:onekitty/utils/my_text_field.dart';
import 'package:onekitty/utils/my_button.dart';
import 'package:shimmer/shimmer.dart';

class InviteUsersPage extends StatefulWidget {
  final int ticketId;
  final int eventId;
  const InviteUsersPage({
    super.key,
    required this.ticketId,
    required this.eventId,
  });

  @override
  State<InviteUsersPage> createState() => _InvitePageState();
}

class _InvitePageState extends State<InviteUsersPage> {
  final TextEditingController _phoneNumber = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final GlobalControllers _controller = Get.put(GlobalControllers());
  final controller = Get.put(InviteUsersController());
  @override
  void initState() {
    super.initState();
    controller.fetchInvites(widget.eventId);
    _controller.fetchContacts();
  }

  @override
  void dispose() {
    _phoneNumber.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(),
        body: Padding(
          padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 20.h),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(height: 20.h),
                Form(
                  key: _formKey,
                  child: InternationalPhoneNumberInput(
                    onInputChanged: (PhoneNumber number) {
                      print("${number.phoneNumber}");
                      _phoneNumber.text =
                          number.phoneNumber.toString().replaceAll("+", '');
                    },
                    onInputValidated: (bool value) {},
                    selectorConfig: const SelectorConfig(
                      selectorType: PhoneInputSelectorType.BOTTOM_SHEET,
                      useBottomSheetSafeArea: true,
                    ),
                    ignoreBlank: false,
                    selectorTextStyle: const TextStyle(color: Colors.black),
                    initialValue: PhoneNumber(isoCode: 'KE', dialCode: '+254'),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Phone Number is required';
                      }
                      return null;
                    },
                    formatInput: true,
                    keyboardType: const TextInputType.numberWithOptions(
                        signed: true, decimal: true),
                    inputBorder: const OutlineInputBorder(),
                    inputDecoration: InputDecoration(
                      hintText: "eg. 0712345678",
                      suffixIcon: IconButton(
                          onPressed: () async {
                            Map<String, dynamic>? results = await showDialog(
                                context: context,
                                builder: (context) {
                                  return Dialog(
                                      child: DisplayContacts(
                                    controller: _controller,
                                  ));
                                });
                            if (results != null) {
                              showDialog(
                                  context: context,
                                  builder: (BuildContext context) =>
                                      InviteDetails(
                                        phone: (results['phone'].length >= 9)
                                            ? "254${results['phone'].substring(results['phone'].length - 9)}"
                                            : results['phone'],
                                        name: results['names'],
                                        eventId: widget.eventId,
                                        ticketId: widget.ticketId,
                                      ));
                            }
                          },
                          icon: const Icon(Icons.contacts_rounded)),
                      //filled: true,
                      // fillColor: Colors.white70,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(
                          width: 0.5,
                          color: Colors.grey,
                        ),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: const BorderSide(
                          width: 0.5,
                          color: Colors.grey,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          width: 1,
                          color: Theme.of(context).primaryColor,
                        ),
                      ),
                    ),
                  ),
                ),
                SizedBox(height: 20.h),
                ElevatedButton(
                  onPressed: () {
                    if (_formKey.currentState!.validate()) {
                      showDialog(
                          context: context,
                          builder: (BuildContext context) => InviteDetails(
                                phone: _phoneNumber.text,
                                // name: results['names'],
                                eventId: widget.eventId,
                                ticketId: widget.ticketId,
                              )).whenComplete(() {
                        _phoneNumber.clear();
                      });
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: primaryColor,
                    padding: EdgeInsets.symmetric(vertical: 10.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(24.r),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.add, color: Colors.white),
                      SizedBox(width: 8.w),
                      Text(
                        'Invite User',
                        style: TextStyle(fontSize: 16.sp, color: Colors.white),
                      ),
                    ],
                  ),
                ),
                Obx(
                  () => controller.newInvites.isEmpty
                      ? const SizedBox()
                      : Column(
                          children: [
                            Align(
                              alignment: Alignment.centerLeft,
                              child: Text(
                                'new Event invitees',
                                style: TextStyle(
                                    fontSize: 16.sp,
                                    fontWeight: FontWeight.w600),
                              ),
                            ),
                            SizedBox(height: 8.h),
                            ListView.builder(
                                shrinkWrap: true,
                                itemCount: controller.newInvites.length,
                                itemBuilder: (context, index) {
                                  final user = controller.newInvites[index];
                                  return ListTile(
                                    leading: Text('${index + 1}'),
                                    title: Text(
                                        "${user.firstName} ${user.secondName}"),
                                    subtitle: Text(user.phoneNumber),
                                    trailing: IconButton(
                                        onPressed: () => controller.newInvites
                                            .removeAt(index),
                                        icon: const Icon(Icons.close)),
                                  );
                                }),
                            Obx(
                              () => MyButton(
                                showLoading: controller.sendInvites.value,
                                onClick: () {
                                  controller.inviteMember(widget.eventId,
                                      widget.ticketId, controller.newInvites);
                                },
                                label: 'Send Invites',
                              ),
                            )
                          ],
                        ),
                ),
                SizedBox(height: 20.h),
                Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    'Event invitees',
                    style:
                        TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
                  ),
                ),
                SizedBox(height: 8.h),
                Obx(() => controller.isLoadingInvitees.value
                    ? ListView.builder(
                        itemCount: 3,
                        shrinkWrap: true,
                        itemBuilder: (context, index) {
                          return Shimmer.fromColors(
                            baseColor: Colors.grey[300]!,
                            highlightColor: Colors.grey[100]!,
                            child: Container(
                              margin: const EdgeInsets.all(4),
                              width: double.infinity,
                              height: 100.h,
                              color: Colors.white,
                            ),
                          );
                        })
                    : controller.invitedUsers.isEmpty
                        ? const Center(child: Text('No invitees found'))
                        : ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: controller.invitedUsers.length,
                            itemBuilder: (context, index) {
                              if (controller.isLoadingInvitees.value) {
                                return ListView.builder(
                                    itemCount: 3,
                                    shrinkWrap: true,
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    itemBuilder: (context, index) {
                                      return Shimmer.fromColors(
                                        baseColor: Colors.grey[300]!,
                                        highlightColor: Colors.grey[100]!,
                                        child: Container(
                                          margin: const EdgeInsets.all(4),
                                          width: double.infinity,
                                          height: 100.h,
                                          color: Colors.white,
                                        ),
                                      );
                                    });
                              }
                              return Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  InkWell(
                                    onTap: () {},
                                    child: Row(children: [
                                      Text('${index + 1}'),
                                      SizedBox(width: 8.w),
                                      const CircleAvatar(
                                        child: Icon(Icons.person),
                                      ),
                                      SizedBox(width: 8.w),
                                      Expanded(
                                        child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Row(
                                                children: [
                                                  Text(
                                                      "${controller.invitedUsers[index].firstName} ${controller.invitedUsers[index].secondName}",
                                                      style: const TextStyle(
                                                          fontSize: 16,
                                                          fontWeight:
                                                              FontWeight.w600)),
                                                  const Spacer(),
                                                  GestureDetector(
                                                    onTap: () {
                                                      final inviteCode =
                                                          controller
                                                              .invitedUsers[
                                                                  index]
                                                              .inviteCode;
                                                      Clipboard.setData(
                                                          ClipboardData(
                                                              text:
                                                                  inviteCode));
                                                      Get.snackbar(
                                                        'Copied',
                                                        'Invite code copied to clipboard',
                                                      );
                                                    },
                                                    child: Chip(
                                                      avatar: const Icon(
                                                          Icons.copy),
                                                      label: Text(controller
                                                          .invitedUsers[index]
                                                          .inviteCode),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                              SizedBox(height: 2.h),
                                              Text(
                                                  controller.invitedUsers[index]
                                                      .phoneNumber,
                                                  style: const TextStyle(
                                                      fontSize: 16,
                                                      fontWeight:
                                                          FontWeight.w600)),
                                            ]),
                                      ),
                                    ]),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 8.0),
                                    child: Divider(color: Colors.grey[300]),
                                  ),
                                ],
                              );
                            })),
              ],
            ),
          ),
        ));
  }
}

class DisplayContacts extends StatelessWidget {
  final GlobalControllers controller;
  const DisplayContacts({super.key, required this.controller});
  @override
  Widget build(BuildContext context) {
    final searchController = TextEditingController();
    final filteredContacts =
        <Contact>[].obs; // Observable list for filtered contacts

    // Initialize filteredContacts with all contacts
    filteredContacts.assignAll(controller.contacts);

    return Container(
        padding: const EdgeInsets.all(8),
        height: MediaQuery.sizeOf(context).height * 3 / 4,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24), border: Border.all()),
        child: Obx(() {
          return Column(
            children: [
              const Text('Add Contact',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 24,
                  )),
              const SizedBox(height: 8),
              CupertinoSearchTextField(
                controller: searchController,
                onChanged: (value) {
                  // Filter contacts based on search input
                  if (value.isEmpty) {
                    filteredContacts.assignAll(controller.contacts);
                  } else {
                    filteredContacts
                        .assignAll(controller.contacts.where((contact) {
                      return contact.displayName != null &&
                          contact.displayName!
                              .toLowerCase()
                              .contains(value.toLowerCase());
                    }).toList());
                  }
                },
              ),
              Expanded(
                child: ListView.builder(
                    itemCount: filteredContacts.length,
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      final contact = filteredContacts[index];
                      return ListTile(
                        onTap: () {
                          final results = {
                            'names': contact.displayName,
                            'phone':
                                '${contact.phones != null && contact.phones!.isNotEmpty ? contact.phones![0].value : ''}'
                                    .removeAllWhitespace,
                            'role': 'viewer',
                            'date_joined': DateTime.now(),
                            'avatar': contact.avatar != null &&
                                    contact.avatar!.isNotEmpty
                                ? contact.avatar
                                : null,
                          };
                          Navigator.of(context).pop(results);
                        },
                        title: Text(contact.displayName ?? ""),
                        leading:
                            contact.avatar != null && contact.avatar!.isNotEmpty
                                ? ClipRRect(
                                    borderRadius: BorderRadius.circular(360),
                                    child: Image.memory(
                                      contact.avatar!,
                                      height: 36,
                                      width: 36,
                                    ),
                                  )
                                : const CircleAvatar(
                                    radius: 18, child: Icon(Icons.person)),
                        subtitle: Text(
                            '${contact.phones != null && contact.phones!.isNotEmpty ? contact.phones![0].value : ''}'),
                      );
                    }),
              )
            ],
          );
        }));
  }
}

class InviteDetails extends StatelessWidget {
  final int eventId, ticketId;
  final String? phone, name;
  const InviteDetails(
      {super.key,
      required this.eventId,
      required this.ticketId,
      this.phone,
      this.name});

  @override
  Widget build(BuildContext context) {
    List<String> nameParts = name?.split(' ') ?? [];
    String firstname = nameParts.isNotEmpty ? nameParts.first : '';
    String secondname =
        nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';

    TextEditingController firstName = TextEditingController(text: firstname),
        email = TextEditingController(),
        secondName = TextEditingController(text: secondname),
        _phoneNumber = TextEditingController(text: phone ?? '');
    final GlobalKey<FormState> _formKey1 = GlobalKey<FormState>();
    final controller = Get.put(InviteUsersController());
    return AlertDialog(
      title: Text('User details ${phone != null ? "for $phone " : ''}'),
      content: Form(
        key: _formKey1,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  children: [
                    MyTextFieldwValidator(
                      controller: firstName,
                      title: 'First Name',
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'First Name is required';
                        }
                        return null;
                      },
                    ),
                    MyTextFieldwValidator(
                      controller: secondName,
                      title: 'Second Name',
                    ),
                    if (phone == null)
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: InternationalPhoneNumberInput(
                          onInputChanged: (PhoneNumber number) {
                            print("${number.phoneNumber}");
                            _phoneNumber.text = number.phoneNumber
                                .toString()
                                .replaceAll("+", '');
                          },
                          onInputValidated: (bool value) {},
                          selectorConfig: const SelectorConfig(
                            selectorType: PhoneInputSelectorType.BOTTOM_SHEET,
                            useBottomSheetSafeArea: true,
                          ),
                          ignoreBlank: false,
                          selectorTextStyle:
                              const TextStyle(color: Colors.black),
                          initialValue:
                              PhoneNumber(isoCode: 'KE', dialCode: '+254'),
                          formatInput: true,
                          keyboardType: const TextInputType.numberWithOptions(
                              signed: true, decimal: true),
                          inputBorder: const OutlineInputBorder(),
                          inputDecoration: InputDecoration(
                            hintText: "eg. 0712345678",
                            //filled: true,
                            // fillColor: Colors.white70,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: const BorderSide(
                                width: 0.5,
                                color: Colors.grey,
                              ),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: const BorderSide(
                                width: 0.5,
                                color: Colors.grey,
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(
                                width: 1,
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                          ),
                        ),
                      ),
                    MyTextFieldwValidator(
                      controller: email,
                      keyboardType: TextInputType.emailAddress,
                      title: 'Email',
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Email is required';
                        } else if (!RegExp(r'^[^@]+@[^@]+\.[^@]+')
                            .hasMatch(value)) {
                          return 'Please enter a valid email address';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Get.back();
          },
          child: const Text('Cancel'),
        ),
        MyButton(
          width: 155.w,
          label: 'Invite',
          onClick: () async {
            if (_formKey1.currentState!.validate()) {
              controller.newInvites.add(InviteUsersModel(
                  phoneNumber: phone ?? _phoneNumber.text,
                  firstName: firstName.text,
                  secondName: secondName.text,
                  email: email.text));
              Get.back();
            }
          },
        ),
      ],
    );
  }
}
