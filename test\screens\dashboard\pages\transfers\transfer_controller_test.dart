import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/configs/payment_channels.dart';
import 'package:onekitty/screens/dashboard/pages/transfers/controllers/transfer_controller.dart';
import 'package:onekitty/screens/dashboard/pages/transfers/models/transfer_type.dart';
void main() {
  group('TransferController Basic Tests', () {
    late TransferController controller;

    setUp(() {
      // Initialize GetX
      Get.testMode = true;

      // Register required services with simple implementations
      Get.put<GetStorage>(GetStorage());
      Get.put<Logger>(Logger());
      Get.put<PaymentChannel>(PaymentChannel());

      // Create controller
      controller = TransferController();
    });

    tearDown(() {
      Get.reset();
    });

    group('Data Clearing', () {
      test('should clear all form data', () {
        // Arrange
        const config = TransferPageConfig(
          transferType: TransferType.event,
          entityId: 1,
        );
        controller.initialize(config);

        // Set data in all fields
        controller.phoneController.text = '**********';
        controller.paybillController.text = '123456';
        controller.accountNumberController.text = 'ACC123';
        controller.tillNumberController.text = '789012';
        controller.bankAccountController.text = 'BANK123';

        // Act
        controller.clearAllFormData();

        // Assert
        expect(controller.phoneController.text, isEmpty);
        expect(controller.paybillController.text, isEmpty);
        expect(controller.accountNumberController.text, isEmpty);
        expect(controller.tillNumberController.text, isEmpty);
        expect(controller.bankAccountController.text, isEmpty);
        expect(controller.selectedBank.value, isNull);
      });



      test('should reset for new transfer', () {
        // Arrange
        const config = TransferPageConfig(
          transferType: TransferType.event,
          entityId: 1,
        );
        controller.initialize(config);

        // Set data
        controller.amountController.text = '1000';
        controller.reasonController.text = 'Test reason';
        controller.phoneController.text = '**********';
        controller.currentPage.value = 2;

        // Act
        controller.resetForNewTransfer();

        // Assert
        expect(controller.amountController.text, isEmpty);
        expect(controller.reasonController.text, isEmpty);
        expect(controller.phoneController.text, isEmpty);
        expect(controller.currentPage.value, equals(0));
        expect(controller.isTransferring.value, isFalse);
        expect(controller.isConfirming.value, isFalse);
      });
    });
  });
}


