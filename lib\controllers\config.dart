import 'package:get/get.dart';
import 'package:get_storage_pro/get_storage_pro.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/models/config_model.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/http_service.dart';

class ConfigController extends GetxController implements GetxService {
  final HttpService apiProvider = Get.put(HttpService());
  final box = Get.put(GetStorage());
  final logger = Get.put(Logger());
  RxString apiMessage = ''.obs;
  RxBool isConfigLoading = false.obs;
  RxBool status = false.obs;
  RxString tawk = ''.obs;
  RxString gemini = ''.obs;
  RxString gpt = ''.obs;
  RxString phoneNumbers = ''.obs;
  RxString whatsappNumbers = ''.obs;
  RxString storeUrl = ''.obs;
  Rx<ConfigModel> configKeys = ConfigModel().obs;
  RxMap<String, dynamic> apiKeys = <String, dynamic>{}.obs;

  // getConfig() async {
  //   isConfigLoading(true);
  //   update();
  //   try {
  //     var res =
  //         await apiProvider.request(url: ApiUrls.config, method: Method.GET);
  //     apiMessage(res.data["message"]);
  //     status(res.data["status"]);
  //     isConfigLoading(false);
  //     if (res.data["status"]) {
  //       if (res.data["data"] != null && res.data["data"]["api_keys"] != null) {
  //         final keys = res.data["data"]["api_keys"];
  //         configKeys(ConfigModel.fromJson(keys));

  //         update();
  //       }
  //       tawk(res.data["data"]["api_keys"]["tawk_key"]);
  //       gemini(res.data["data"]["api_keys"]["gemini_key"]);
  //       gpt(res.data["data"]["api_keys"]["gpt_key"]);
  //       update();
  //       return true;
  //     } else {
  //       return false;
  //     }
  //   } catch (e) {
  //     logger.e(e);
  //     isConfigLoading(false);
  //     return false;
  //   }
  // }

  getConfig2() async {
    isConfigLoading(true);
    update();
    try {
      var res =
          await apiProvider.request(url: ApiUrls.config, method: Method.GET);
      if (res.data['status']) {
        final data = res.data['data'];
        final apiKeysData = data['api_keys'];
        if (apiKeysData != null) {
          final keys = ConfigModel.fromJson(apiKeysData);
          configKeys(keys);
          tawk(keys.tawkKey);
          gemini(keys.geminiKey);
          gpt(keys.gptKey);
          phoneNumbers(keys.phoneNumbers);
          whatsappNumbers(keys.whatsappNumbers);
          storeUrl(keys.storeUrl);
        }
        apiMessage(data["message"]);
      } else {
        apiMessage(res.data[
            'message']); // Assuming error message is available in the response
      }
      status(res.data['status']);
      isConfigLoading(false);
      return res.data['status'];
    } catch (e) {
      logger.e('Error fetching configuration: $e');
      apiMessage("Error,Please try again");
      isConfigLoading(false);
      return false;
    }
  }
}

class ConfigDataController extends GetxController {
  RxString tawkKey = ''.obs;
  RxString geminiKey = ''.obs;
  RxString gptKey = ''.obs;

  updateKeys({String? tawk, String? gemini, String? gpt}) {
    tawkKey.value = tawk ?? '';
    geminiKey.value = gemini ?? '';
    gptKey.value = gpt ?? '';
  }
}
