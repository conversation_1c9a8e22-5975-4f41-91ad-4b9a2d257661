import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:onekitty/controllers/admin/chama/chama_admin_controller.dart';
import '../../models/chama/chama_transactions.dart';
import 'widgets/table_footer.dart';

class TransactionTable extends StatefulWidget {
  final int chamaId;

  const TransactionTable({super.key, required this.chamaId});

  @override
  _TransactionTableState createState() => _TransactionTableState();
}

class _TransactionTableState extends State<TransactionTable> {
  final controller = Get.find<ChamaAdminController>();

  late bool _sortAscending;
  late int _sortColumnIndex;

  @override
  void initState() {
    super.initState();
    controller.clearInvoice();
    controller.getChamaTrnsactions(chamaId: widget.chamaId, page: 0);
    _sortAscending = true;
    _sortColumnIndex = 0;
  }

  @override
  Widget build(BuildContext context) {
    void _sort<T>(Comparable<T> Function(Transaction transaction) getField,
        int columnIndex, bool ascending) {
      setState(() {
        _sortColumnIndex = columnIndex;
        _sortAscending = ascending;
        controller.transactions.sort((a, b) {
          final aValue = getField(a);
          final bValue = getField(b);
          return ascending
              ? Comparable.compare(aValue, bValue)
              : Comparable.compare(bValue, aValue);
        });
      });
    }

    void onRefresh() {
      controller.getChamaTrnsactions(chamaId: widget.chamaId, page: 0);
    }

    void _onRowsPerPageChanged(int value) {
      controller.invoiceSize.value = value;
      controller.getChamaTrnsactions(chamaId: widget.chamaId, page: 0);
    }

    void _onRefresh() {
      controller.invoices.clear();
      controller.getChamaTrnsactions(chamaId: widget.chamaId, page: 0);
    }

    void _onPreviousPage() {
      if (!controller.invoiceIsFirst.value) {
        controller.getChamaTrnsactions(
            chamaId: widget.chamaId,
            page: controller.invoiceCurrentPage.value - 1);
      }
    }

    void _onNextPage() {
      if (!controller.invoiceIsLast.value) {
        controller.getInvoices(
            chamaId: widget.chamaId,
            page: controller.invoiceCurrentPage.value + 1);
      }
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Transactions'),
      ),
      body: RefreshIndicator(
        onRefresh: () async => onRefresh,
        child: Obx(
          () => controller.isLoading.value
              ? const Center(
                  child: CircularProgressIndicator(),
                )
              : Obx(
                  () => Column(
                    children: [
                      Expanded(
                        child: DataTable2(
                          columnSpacing: 12,
                          horizontalMargin: 12,
                          minWidth: 800,
                          sortColumnIndex: _sortColumnIndex,
                          sortAscending: _sortAscending,
                          columns: [
                            DataColumn2(
                              label: const Text('ID'),
                              size: ColumnSize.S,
                              onSort: (columnIndex, ascending) {
                                _sort<num?>((transaction) => transaction.id ?? 0,
                                    columnIndex, ascending);
                              },
                            ),
                            DataColumn2(
                              label: const Text('Created At'),
                              onSort: (columnIndex, ascending) {
                                _sort<DateTime?>(
                                    (transaction) =>
                                        transaction.createdAt ?? DateTime.now(),
                                    columnIndex,
                                    ascending);
                              },
                            ),
                            DataColumn2(
                              label: const Text('Amount'),
                              size: ColumnSize.M,
                              onSort: (columnIndex, ascending) {
                                _sort<num>((transaction) => transaction.amount ?? 0,
                                    columnIndex, ascending);
                              },
                            ),
                            DataColumn2(
                              label: const Text('Status'),
                              size: ColumnSize.M,
                              onSort: (columnIndex, ascending) {
                                _sort<String?>(
                                    (transaction) => transaction.status ?? '',
                                    columnIndex,
                                    ascending);
                              },
                            ),
                            DataColumn2(
                              label: const Text('Phone Number'),
                              size: ColumnSize.L,
                              onSort: (columnIndex, ascending) {
                                _sort<String?>(
                                    (transaction) => transaction.phoneNumber ??'',
                                    columnIndex,
                                    ascending);
                              },
                            ),
                            DataColumn2(
                              label: const Text('Transaction Type'),
                              size: ColumnSize.L,
                              onSort: (columnIndex, ascending) {
                                _sort<String?>(
                                    (transaction) =>
                                        transaction.transactionType ??'',
                                    columnIndex,
                                    ascending);
                              },
                            ),
                          ],
                          rows: controller.transactions
                              .map(
                                (transaction) => DataRow2(
                                  onTap: () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) =>
                                            TransactionDetailPage(
                                                transaction: transaction),
                                      ),
                                    );
                                  },
                                  cells: [
                                    DataCell(Text(transaction.id.toString())),
                                    DataCell(Text(
                                        _formatDate(transaction.createdAt))),
                                    DataCell(
                                        Text(transaction.amount.toString())),
                                    DataCell(Text(transaction.status ??'')),
                                    DataCell(Text(transaction.phoneNumber ??'')),
                                    DataCell(Text(transaction.transactionType ??'')),
                                  ],
                                ),
                              )
                              .toList(),
                        ),
                      ),
                      TablePaginationFooter(
                        currentPage: controller.invoiceCurrentPage.value,
                        totalPages: controller.invoiceTotalPages.value,
                        rowsPerPage: controller.invoiceSize.value,
                        availableRowsPerPage: const [15, 30, 50, 100],
                        onRowsPerPageChanged: _onRowsPerPageChanged,
                        onRefresh: _onRefresh,
                        onPreviousPage: _onPreviousPage,
                        onNextPage: _onNextPage,
                      ),
                    ],
                  ),
                ),
        ),
      ),
    );
  }

  String _formatDate(DateTime? date) {
    return date != null ? DateFormat('yyyy-MM-dd').format(date) : 'N/A';
  }
}

class TransactionDetailPage extends StatelessWidget {
  final Transaction transaction;

  const TransactionDetailPage({super.key, required this.transaction});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Transaction Details'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('Transaction ID', transaction.id.toString()),
            _buildDetailRow('Created At', _formatDate(transaction.createdAt)),
            _buildDetailRow('Updated At', _formatDate(transaction.updatedAt)),
            _buildDetailRow('Account Number', transaction.accountNumber ??''),
            _buildDetailRow('First Name', transaction.firstName ??''),
            _buildDetailRow('Second Name', transaction.secondName ??''),
            _buildDetailRow('Transaction Code', transaction.transactionCode ??''),
            _buildDetailRow(
                'Other Transaction Code', transaction.transactionCodeOther ??''),
            _buildDetailRow('Internal ID', transaction.internalId ??''),
            _buildDetailRow('Phone Number', transaction.phoneNumber ??''),
            _buildDetailRow('Kitty ID', transaction.kittyId.toString()),
            _buildDetailRow('Payment Ref', transaction.paymentRef ??''),
            _buildDetailRow('Member ID', transaction.memberId.toString()),
            _buildDetailRow('Penalty ID', transaction.penaltyId.toString()),
            _buildDetailRow('User ID', transaction.userId.toString()),
            _buildDetailRow('Currency Code', transaction.currencyCode ??''),
            _buildDetailRow('Channel Name', transaction.channelName ??''),
            _buildDetailRow('Channel Code', transaction.channelCode ??''),
            _buildDetailRow('Amount', transaction.amount.toString()),
            _buildDetailRow('Type In/Out', transaction.typeInOut ??''),
            _buildDetailRow('Status', transaction.status ??''),
            _buildDetailRow('Transaction Type', transaction.transactionType ??''),
            _buildDetailRow(
                'Transaction Category', transaction.transactionCategory ??''),
            _buildDetailRow('Metadata ID', transaction.metadataId.toString()),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 3,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16.0,
              ),
            ),
          ),
          Expanded(
            flex: 5,
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 16.0,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime? date) {
    return date != null
        ? DateFormat('yyyy-MM-dd HH:mm:ss').format(date)
        : 'N/A';
  }
}
