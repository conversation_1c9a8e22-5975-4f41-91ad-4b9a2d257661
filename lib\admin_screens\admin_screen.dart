import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/admin/dashboard_controller.dart';
import 'drawer.dart';

class AdminScreen extends StatefulWidget {
  const AdminScreen({super.key});

  @override
  State<AdminScreen> createState() => _AdminScreenState();
}

class _AdminScreenState extends State<AdminScreen> {
  final controller = Get.put(DashboardController());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: const AdminDrawer(),
      appBar: AppBar(
        centerTitle: true,
        leading: MediaQuery.of(context).size.width > 600
            ? null
            : Builder(builder: (context) {
                return IconButton(
                  icon: const Icon(Icons.menu),
                  onPressed: () {
                    Scaffold.of(context).openDrawer();
                  },
                );
              }),
        title: const Text("Admin Screen"),
      ),
      body: Row(
        children: [
          if (MediaQuery.of(context).size.width > 600)
             SizedBox(
              width: MediaQuery.of(context).size.width > 600 &&
              MediaQuery.of(context).size.width < 720 ? 60 : 300,
              child: const AdminDrawer(),
            ),
          Expanded(
            child: Obx(() => controller.AdminPages(controller.pageNo.value)),
          ),
        ],
      ),
    );
  }
}
