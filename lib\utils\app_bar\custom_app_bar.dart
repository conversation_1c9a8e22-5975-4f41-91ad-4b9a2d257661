import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/user_ktty_controller.dart';
import 'package:onekitty/screens/dashboard/pages/profile/profile_page.dart';
import 'package:onekitty/utils/size_config.dart';

import '../utils_exports.dart';

// ignore: must_be_immutable
class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  const CustomAppBar({
    super.key,
    this.height,
    this.styleType,
    this.leadingWidth,
    this.leading,
    this.title,
    this.centerTitle,
    this.actions,
  });

  final double? height;

  final Style? styleType;

  final double? leadingWidth;

  final Widget? leading;

  final Widget? title;

  final bool? centerTitle;

  final List<Widget>? actions;

  @override
  Widget build(BuildContext context) {
    return AppBar(
      elevation: 0,
      toolbarHeight: height ?? 98.h,
      automaticallyImplyLeading: false,
      backgroundColor: Colors.transparent,
      flexibleSpace: _getStyle(),
      leadingWidth: leadingWidth ?? 0,
      leading: leading,
      title: title,
      titleSpacing: 0,
      centerTitle: centerTitle ?? false,
      actions: actions,
    );
  }

  @override
  Size get preferredSize => Size(
        SizeConfig.screenWidth,
        height ?? 110.h,
      );
  _getStyle() {
    switch (styleType) {
      case Style.bgFill:
        return Container(
          height: 98.h,
          width: double.maxFinite,
          decoration: BoxDecoration(
            color: appTheme.indigo500,
          ),
        );
      case Style.bgwhite:
        Container(
          height: 98.h,
          width: double.maxFinite,
          decoration: BoxDecoration(
            color: appTheme.whiteA700,
          ),
        );
      default:
        return null;
    }
  }
}

enum Style {
  bgFill,
  bgwhite,
}

PreferredSizeWidget buildAppBar(BuildContext context, [List<Widget>? actions]) {
  return CustomAppBar(
    height: 30.h,
    leadingWidth: 30.w,
    leading: Padding(
      padding: const EdgeInsets.only(right: 16.0, bottom: 6),
      child: IconButton(
          onPressed: () {
            Navigator.of(context).pop();
          },
          icon: const Icon(
            Icons.arrow_back_rounded,
            color: Colors.black,
          )),
    ),
    title: AppbarTitle(
        text: "Back",
        //textSize: 18,
        textColor: appTheme.black900,
        margin: EdgeInsets.only(left: 1.w)),
    actions: actions,
  );
}

Color getRoleColors(String role) {
  switch (role.toUpperCase()) {
    case "CHAIRPERSON":
      return const Color.fromARGB(255, 36, 11, 119);
    case "TREASURER":
      return const Color(0xFF56AF57);
    case "SECRETARY":
      return const Color.fromARGB(255, 206, 104, 192);
    case "MEMBER":
      return Colors.black;
    default:
      return Colors.black;
  }
}

PreferredSizeWidget buildAppBarWithImage(BuildContext context) {
  String greeting = getGreeting();
  final UserKittyController userController = Get.put(UserKittyController());
  return CustomAppBar(
    leadingWidth: 40.w,
    height: 60.h,
    leading: AppbarLeadingImage(
        imagePath: userController.getLocalUser()?.profileUrl != null ||
                userController.getLocalUser()?.profileUrl?.toString() != ""
            ? userController.getLocalUser()?.profileUrl
            : null,
        margin: EdgeInsets.only(left: 5.w, top: 1.h, bottom: 1.h),
        onTap: () {
          Navigator.pushAndRemoveUntil(
              context,
              MaterialPageRoute(builder: (context) => const ProfilePage()),
              (route) => route.isActive);
        }),
    title: Padding(
      padding: EdgeInsets.only(top: 0.h),
      child: AppbarTitle(
          textSize: 17,
          text: "$greeting, ${userController.getLocalUser()!.firstName}",
          margin: EdgeInsets.only(left: 5.w)),
    ),
  );
}

class RowAppBar extends StatelessWidget {
  const RowAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        Navigator.pop(context);
      },
      child: Row(
        children: [
          IconButton(
              onPressed: () {
                Navigator.pop(context);
              },
              icon: const Icon(Icons.arrow_back)),
          const Text("Back")
        ],
      ),
    );
  }
}
