// To parse this JSON data, do
//
//     final chamaContributeRequest = chamaContributeRequestFromJson(jsonString);

import 'dart:convert';

ChamaContributeRequest chamaContributeRequestFromJson(String str) => ChamaContributeRequest.fromJson(json.decode(str));

String chamaContributeRequestToJson(ChamaContributeRequest data) => json.encode(data.toJson());

class ChamaContributeRequest {
    int? kittyId;
    int? penaltyId;
    int? userId;
    int? amount;
    String? phoneNumber;
    String? accountNumber;
    String? latitude;
    String? longitude;
    int? channelCode;
    String? email;
    String? paymentType;

    ChamaContributeRequest({
        this.kittyId,
        this.penaltyId,
        this.userId,
        this.amount,
        this.phoneNumber,
        this.accountNumber,
        this.latitude,
        this.longitude,
        this.channelCode,
        this.email,
        this.paymentType,
    });

    factory ChamaContributeRequest.from<PERSON>son(Map<String, dynamic> json) => ChamaContributeRequest(
        kittyId: json["kitty_id"],
        penaltyId: json["penalty_id"],
        userId: json["user_id"],
        amount: json["amount"],
        phoneNumber: json["phone_number"],
        accountNumber: json["account_number"],
        latitude: json["latitude"],
        longitude: json["longitude"],
        channelCode: json["channel_code"],
        email: json["email"],
        paymentType: json["payment_type"],
    );

    Map<String, dynamic> toJson() => {
        "kitty_id": kittyId,
        "penalty_id": penaltyId,
        "user_id": userId,
        "amount": amount,
        "phone_number": phoneNumber,
        "account_number": accountNumber,
        "latitude": latitude,
        "longitude": longitude,
        "channel_code": channelCode,
        "email": email,
        "payment_type": paymentType,
    };
}
