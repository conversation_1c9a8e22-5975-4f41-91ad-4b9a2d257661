import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'dart:convert';

import 'package:flutter_quill/quill_delta.dart';
import 'package:flutter_quill_delta_from_html/flutter_quill_delta_from_html.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:onekitty/utils/iswysiwyg.dart';

class EditableQuillWidget extends StatefulWidget {
  final String text;
  final double? height;
  final Function(String) onTextChanged; // Callback for when text changes

  const EditableQuillWidget({
    super.key,
    required this.text,
    required this.onTextChanged,
    this.height,
  });

  @override
  State<EditableQuillWidget> createState() => _EditableQuillWidgetState();
}

class _EditableQuillWidgetState extends State<EditableQuillWidget> {
  late QuillController _controller;

  @override
  void initState() {
    super.initState();
    _initializeController();

    // Listen to changes in the document
    _controller.document.changes.listen((event) {
      // Convert the document to JSON and pass it to the parent
      final json = jsonEncode(_controller.document.toDelta().toJson());
      widget.onTextChanged(json);
    });
  }

  void _initializeController() {
    try {
      List<dynamic> operations;
      String cleanedText = widget.text.trim();

      // Handle cases like '[{insert: DevFest}]'
      if (cleanedText.startsWith('[{') && cleanedText.endsWith('}]')) {
        try {
          operations = jsonDecode(cleanedText);
        } catch (e) {
          String content = cleanedText
              .replaceAll('[{insert:', '')
              .replaceAll('}]', '')
              .trim();

          operations = [
            {"insert": "$content\n"}
          ];
        }
      } else {
        try {
          dynamic decodedContent = jsonDecode(cleanedText);

          if (decodedContent is Map && decodedContent.containsKey('ops')) {
            operations = decodedContent['ops'] as List<dynamic>;
          } else if (decodedContent is List) {
            operations = decodedContent;
          } else {
            operations = [
              {"insert": "$cleanedText\n"}
            ];
          }
        } catch (e) {
          operations = [
            {"insert": "$cleanedText\n"}
          ];
        }
      }

      // Ensure the last insert ends with a newline
      if (operations.isNotEmpty) {
        dynamic lastOp = operations.last;
        if (lastOp is Map && lastOp.containsKey('insert')) {
          String lastText = lastOp['insert'] as String;
          if (!lastText.endsWith('\n')) {
            if (lastOp.containsKey('attributes')) {
              operations.add({"insert": "\n"});
            } else {
              lastOp['insert'] = '$lastText\n';
            }
          }
        } else {
          operations.add({"insert": "\n"});
        }
      }

      Delta delta = Delta.fromJson(operations);
      _controller = QuillController(
        document: Document.fromDelta(delta),
        selection: const TextSelection.collapsed(offset: 0),
        configurations: const QuillControllerConfigurations(),
      );
    } catch (e) {
      // Fallback to plain text
      _controller = QuillController(
        document: Document.fromJson([
          {"insert": "${widget.text}\n"}
        ]),
        selection: const TextSelection.collapsed(offset: 0),
        configurations: const QuillControllerConfigurations(),
      );
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 8),
             Padding(
              padding: const EdgeInsets.all(4.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 8),
                  QuillToolbar.simple(
                    configurations: QuillSimpleToolbarConfigurations(
                      multiRowsDisplay: false,
                      sharedConfigurations: const QuillSharedConfigurations(
                        locale: Locale('en'),
                      ),
                      controller: _controller,
                    ),
                  ),
                  const SizedBox(height: 15),
                  QuillEditor.basic(
                    configurations: QuillEditorConfigurations(
                      placeholder: "e.g Time to award our best artist",
                      controller: _controller,
                      // readOnly: false,
                      autoFocus: false,
                      enableInteractiveSelection:
                          true, // Enable interactive selection to allow text editing
              
                      sharedConfigurations: const QuillSharedConfigurations(
                        locale: Locale('en'),
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                ],
              ),
            ),
           
     
      const SizedBox(height: 8),
      ],
    );
  }
}

class QuillReadMoreController extends GetxController {
  final RxBool isExpanded = false.obs;
  final RxBool shouldShowReadMore = false.obs;

  void toggleExpanded() {
    isExpanded.toggle();
  }

  void updateShouldShowReadMore(bool value) {
    shouldShowReadMore.value = value;
  }
}

class QuillEditorWidget extends StatelessWidget {
  final String? text;
  final int maxLines;
  final bool readMore;
  const QuillEditorWidget({
    super.key,
    this.readMore = true,
    this.text,
    this.maxLines = 1,
  });

  @override
  Widget build(BuildContext context) {
    final QuillReadMoreController controller = Get.put(
      QuillReadMoreController(),
    );

    if (text == null || text!.isEmpty) {
      return const SizedBox.shrink();
    }

    try {
      late Document document;
      if(isWysiwygFormat(text!)){
           document = Document.fromJson(
           HtmlToDelta().convert(text!).toJson()
           );
      var selection = const TextSelection.collapsed(offset: 0);
      final eventDescription =
          QuillController(document: document, selection: selection);

      }else{
      List<dynamic> operations = _parseContent(text!);
      Delta delta = Delta.fromJson(operations);
      document = Document.fromDelta(delta);
      }
    
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 4.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Material(
              color: Colors.transparent,
              child: LayoutBuilder(
                builder: (context, constraints) {
                  return readMore == false
                      ? _buildQuillEditor(
                          document: document,
                          controller: controller,
                        )
                      : Stack(
                          alignment: Alignment.bottomRight,
                          children: [
                            Obx(() => _buildQuillEditor(
                                  document: document,
                                  controller: controller,
                                  maxHeight:
                                      controller.isExpanded.value ? null : 38.h,
                                )),
                            if (_needsReadMore(document))
                              Obx(() => Positioned(
                                    bottom: -14.h,
                                    child: Padding(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 4.0.w),
                                      child: TextButton(
                                        onPressed: controller.toggleExpanded,
                                        child: Text(
                                          controller.isExpanded.value
                                              ? 'Read Less'
                                              : 'Read More',
                                          style: TextStyle(
                                            backgroundColor: Theme.of(context)
                                                .colorScheme
                                                .surface,
                                            color:
                                                Theme.of(context).primaryColor,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ),
                                    ),
                                  )),
                          ],
                        );
                },
              ),
            ),
          ],
        ),
      );
    } catch (e) {
      return _buildFallbackWidget(text ?? '', controller);
    }
  }

  Widget _buildQuillEditor({
    required Document document,
    required QuillReadMoreController controller,
    double? maxHeight,
  }) {
    return Container(
      constraints: BoxConstraints(
        maxHeight: controller.isExpanded.value
            ? double.infinity
            : maxHeight ?? double.infinity,
      ),
      child: QuillEditor(
        focusNode: FocusNode(canRequestFocus: false),
        scrollController: ScrollController(),
        configurations: QuillEditorConfigurations(
          controller: QuillController(
            readOnly: true,
            document: document,
            selection: const TextSelection.collapsed(offset: 0),
            configurations: const QuillControllerConfigurations(),
          ),
          autoFocus: false,
          scrollable: false,
          showCursor: false,
          enableInteractiveSelection: false,
          enableSelectionToolbar: false,
          maxHeight: maxHeight,
          // Add embedBuilders for image support
          embedBuilders: [
            QuillEditorImageEmbedBuilder(),
          ],
        ),
      ),
    );
  }

  List<dynamic> _parseContent(String inputText) {
    if (inputText.isEmpty) {
      return [
        {"insert": "\n"}
      ];
    }

    String cleanedText = inputText.trim();
    List<dynamic> operations;

    try {
      if (cleanedText.startsWith('[{') && cleanedText.endsWith('}]')) {
        try {
          operations = jsonDecode(cleanedText);
        } catch (_) {
          String content = cleanedText
              .replaceAll('[{insert:', '')
              .replaceAll('}]', '')
              .trim();
          operations = [
            {"insert": "${content.isNotEmpty ? content : ''}\n"}
          ];
        }
      } else {
        try {
          dynamic decodedContent = jsonDecode(cleanedText);
          if (decodedContent is Map && decodedContent.containsKey('ops')) {
            operations = decodedContent['ops'] as List<dynamic>;
          } else if (decodedContent is List) {
            operations = decodedContent;
          } else {
            operations = [
              {"insert": "$cleanedText\n"}
            ];
          }
        } catch (_) {
          operations = [
            {"insert": "$cleanedText\n"}
          ];
        }
      }

      if (operations.isEmpty) {
        return [
          {"insert": "\n"}
        ];
      }

      dynamic lastOp = operations.last;
      if (lastOp is Map && lastOp.containsKey('insert')) {
        String lastText = (lastOp['insert'] as String?) ?? '';
        if (!lastText.endsWith('\n')) {
          if (lastOp.containsKey('attributes')) {
            operations.add({"insert": "\n"});
          } else {
            lastOp['insert'] = '$lastText\n';
          }
        }
      } else {
        operations.add({"insert": "\n"});
      }

      return operations;
    } catch (_) {
      return [
        {"insert": "${cleanedText.isNotEmpty ? cleanedText : ''}\n"}
      ];
    }
  }

  bool _needsReadMore(Document document) {
    try {
      final plainText = document.toPlainText();
      return plainText.split('\n').length > maxLines + 1;
    } catch (_) {
      return false;
    }
  }

  Widget _buildFallbackWidget(
      String fallbackText, QuillReadMoreController controller) {
    try {
      Delta delta = Delta.fromJson([
        {"insert": "$fallbackText\n"}
      ]);
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Material(
          color: Colors.transparent,
          child: Obx(() => _buildQuillEditor(
                document: Document.fromDelta(delta),
                controller: controller,
                maxHeight: controller.isExpanded.value ? null : 25,
              )),
        ),
      );
    } catch (_) {
      return const SizedBox.shrink();
    }
  }
}

class QuillEditorImageEmbedBuilder extends EmbedBuilder {
  @override
  String get key => 'image';

  @override
  Widget build(
    BuildContext context,
    QuillController controller,
    Embed node,
    bool readOnly,
    bool inline,
    TextStyle textStyle,
  ) {
    final imageUrl = node.value.data;
    if (imageUrl == null || imageUrl is! String) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: ConstrainedBox(
        constraints: const BoxConstraints(
          maxHeight: 300,
        ),
        child: Image.network(
          imageUrl,
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) {
            return const Icon(Icons.error);
          },
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return Center(
              child: CircularProgressIndicator(
                value: loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                        loadingProgress.expectedTotalBytes!
                    : null,
              ),
            );
          },
        ),
      ),
    );
  }
}
