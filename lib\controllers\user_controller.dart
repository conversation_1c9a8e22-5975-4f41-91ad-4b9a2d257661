import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:onekitty/models/auth/profile_request.dart';
import 'package:onekitty/models/auth/user_model.dart';
import 'package:onekitty/models/kitty_model.dart';
import 'package:onekitty/services/api_urls.dart';
import 'package:onekitty/services/auth_manager.dart';
import 'package:onekitty/services/http_service.dart';
import 'package:get/get.dart';
import 'package:get_storage_pro/get_storage_pro.dart';
import 'package:logger/logger.dart';
import 'package:onekitty/utils/cache_keys.dart';

class UserController extends GetxController implements GetxService {
  final HttpService apiProvider = Get.put(
    HttpService(),
  );
  final box = Get.find<GetStorage>();
  final logger = Get.find<Logger>();
  RxString apiMessage = ''.obs;
  RxString checkoutId = ''.obs;
  final authManager = AuthenticationManager();

  Rx<UserModelLatest> user = UserModelLatest().obs;
  RxString kittStatus = ''.obs;
  RxBool isloading = false.obs;
  RxBool loadingStats = false.obs;
  RxBool kittiesLoading = false.obs;
  RxBool loadingTransactions = false.obs;

  RxBool status = false.obs;

  RxList<Kitty> kitties = <Kitty>[].obs;
  // RxList<TransactionModel> transactions = <TransactionModel>[].obs;
  // Rx<TransactionTicketModel> searchedTransaction = TransactionTicketModel().obs;
  RxInt userContributions = 0.obs;
  RxInt userTotalMoney = 0.obs;
  RxInt userKitties = 0.obs;
  RxString names = "".obs;
  RxString tracode = "".obs;
  RxInt amount = 0.obs;
  RxBool isLoading = false.obs;
  RxBool isUpdateProfile = false.obs;

  RxBool isPinloading = false.obs;

  TextEditingController? photoUrl;

  @override
  void onInit() {
    getLocalUser();

    super.onInit();
  }

  UserModelLatest? getLocalUser() {
    final usr = box.read(CacheKeys.user);
    if (usr != null) {
      user(UserModelLatest.fromJson(usr));
      return user.value;
    } else {
      return null;
    }
  }

  getStats() async {
    loadingStats(true);
    update();

    try {
      getLocalUser();
      var resp = await apiProvider.request(
          url: "${ApiUrls.getUserStats}?phone_number=${user.value.phoneNumber}",
          method: Method.GET);
      if (resp.data["status"]) {
        userContributions(resp.data["data"]["user_contributions"]);
        userKitties(resp.data["data"]["user_kitties"]);
        userTotalMoney(resp.data["data"]["total_expenditure"]);
      }

      loadingStats(false);
      update();
    } catch (e) {
      logger.e(e);
      loadingStats(false);
      apiMessage('An error occured');
      update();
    }
  }

  Future<bool> updateProfile({required ProfileRequest request}) async {
    isUpdateProfile(true);
    update();
    try {
      var res = await apiProvider.request(
          url: ApiUrls.updateProfile,
          method: Method.POST,
          params: request.toJson());
      apiMessage(res.data["message"]);
      if (res.data["status"]) {
        apiMessage(res.data["message"]);
      }
      isUpdateProfile(false);
      return res.data["status"];
    } catch (e) {
      logger.e(e);
      isUpdateProfile(false);
      apiMessage("An error occured");
      return false;
    }
  }

  String maskString(String input) {
    if (input.length > 9) {
      final int startLength = (input.length - 3) ~/ 2;
      final int endLength = input.length - startLength - 3;
      final String start = input.substring(0, startLength);
      final String end = input.substring(input.length - endLength);
      final String masked = '*' * 3;
      return '$start$masked$end';
    } else {
      return input;
    }
  }

  Future<String> uploadMediaprofile(dynamic filebytes, String file) async {
    isLoading(true);
    try {
      final Reference storageReference =
          FirebaseStorage.instance.ref().child('user-profile').child(file);

      UploadTask uploadTask = storageReference.putData(filebytes);
      String profileURL = await (await uploadTask).ref.getDownloadURL();
      isLoading(false);
      return profileURL;
    } catch (e) {
      print("Error ${e.toString()}");
      return e.toString();
    }
  }
}
