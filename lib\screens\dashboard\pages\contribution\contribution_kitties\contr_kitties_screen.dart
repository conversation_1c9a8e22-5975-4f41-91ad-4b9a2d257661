// ignore_for_file: prefer_const_constructors
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/contribute_controller.dart';
import 'package:onekitty/models/auth/user_model.dart';
import 'package:onekitty/screens/dashboard/pages/contribution/create_or_contri_kitty_screen.dart';
import 'package:onekitty/screens/bottom_navbar_screens/nav_routes/nav_routes.dart';
import 'package:onekitty/utils/size_config.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import '../../../../../controllers/user_ktty_controller.dart';
import '../../../../../utils/utils_exports.dart';
import 'widgets/single_kitty_card.dart';

// ignore_for_file: must_be_immutable
class MyKittiesScreen extends StatefulWidget {
  const MyKittiesScreen({super.key});

  @override
  State<MyKittiesScreen> createState() => _MyKittiesScreenState();
}

class _MyKittiesScreenState extends State<MyKittiesScreen> {
  TextEditingController searchController = TextEditingController();

  UserModelLatest user = UserModelLatest();

  UserKittyController userController = Get.put(UserKittyController());

  String greeting = getGreeting();

  DateTime tagetDate = DateTime.now().add(const Duration(days: 1));
  ContributeController singleKitty = Get.put(ContributeController());
  List filteredKitties = [];
  final RefreshController _refreshController =
      RefreshController(initialRefresh: true);
  void _onRefresh() async {
    userController.getLocalUser();
    await userController.getUserkitties();
    userController.reset();

    _refreshController.refreshCompleted();
  }

  @override
  void initState() {
    super.initState();
    //_onRefresh();
    filteredKitties = userController.kitties;
    //setValues();
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    //final screenSize = MediaQuery.of(context).size;
    return Scaffold(
      appBar: buildAppBarWithImage(context),
      body: SmartRefresher(
        onRefresh: _onRefresh,
        controller: _refreshController,
        child: Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            Padding(
              padding: EdgeInsets.only(left: 20.w, right: 20.w),
              child: userController.kitties.isEmpty
                  ? SizedBox()
                  : _buildRow(context),
            ),
            SizedBox(height: 24.h),
            userController.kitties.isEmpty
                ? SizedBox()
                : Padding(
                    padding: EdgeInsets.only(left: 20.w, right: 20.w),
                    child: TextFormField(
                      autofocus: false,
                      controller: searchController,
                      decoration: InputDecoration(
                        prefixIcon: Icon(Icons.search),
                        hintText: "Search for a Kitty",
                      ),
                      onChanged: (value) {
                        filteredKitties = userController.kitties
                            .where((kitty) => kitty.kitty!.title!
                                .toLowerCase()
                                .contains(value.toLowerCase()))
                            .toList();
                        setState(() {});
                      },
                    ),
                  ),
            Expanded(
              child: GetX(
                init: UserKittyController(),
                initState: (state) {
                  Future.delayed(Duration.zero, () async {
                    userController.kittiesLoading(true);
                    try {
                      await state.controller?.getUserkitties();
                      userController.reset();
                      print("Kitties loaded successfully");
                    } catch (e) {
                      print("Error loading kitties: $e");
                      rethrow;
                    }
                    userController.kittiesLoading(false);
                  });
                },
                builder: (UserKittyController usercontroller) {
                  if (usercontroller.kittiesLoading.isTrue) {
                    return SizedBox(
                      height: SizeConfig.screenHeight * .33,
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SpinKitDualRing(
                              color: ColorUtil.blueColor,
                              lineWidth: 4.sp,
                              size: 40.0.sp,
                            ),
                            const Text(
                              "loading..",
                              style: TextStyle(
                                color: Colors.white,
                              ),
                            )
                          ],
                        ),
                      ),
                    );
                  }
                  if (userController.kitties.isEmpty) {
                    return const CrtContributionKittyPage();
                  } else if (filteredKitties.isEmpty) {
                    return Padding(
                      padding: EdgeInsets.only(top: 15.h),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: const [
                          Text("Kitty not found"),
                        ],
                      ),
                    );
                  } else if (filteredKitties.isNotEmpty) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 20.0, vertical: 20),
                      child: Column(
                        children: [
                          Expanded(
                            child: ListView.separated(
                              controller: userController.controller,
                              physics: BouncingScrollPhysics(),
                              separatorBuilder: (context, index) {
                                return SizedBox(
                                  height: 24.h,
                                );
                              },
                              itemCount: filteredKitties.length,
                              itemBuilder: (context, index) {
                                final kitty = filteredKitties[index];

                                return ContributionKittyWidget(
                                  kitty: kitty,
                                );
                              },
                            ),
                          ),
                          if (usercontroller.loadingMore.isTrue)
                            Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Column(
                                children: [
                                  SpinKitDualRing(
                                    color: ColorUtil.blueColor,
                                    lineWidth: 4.sp,
                                    size: 40.0.sp,
                                  ),
                                  const Text(
                                    "loading..",
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontStyle: FontStyle.italic,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    );
                  }

                  return const CrtContributionKittyPage();
                },
              ),
            ),
            GetBuilder(
              builder: (UserKittyController userKittyController) {
                if (userKittyController.kittiesLoading.isTrue) {
                  return Center(
                    child: Text(
                      "Loading More Kitties",
                      style: TextStyle(fontStyle: FontStyle.italic),
                    ),
                  );
                } else if (userKittyController.scrollEnd.isTrue) {
                  Center(
                    child: Text(
                      "No more kitties",
                    ),
                  );
                }
                return SizedBox.shrink();
              },
            )
          ],
        ),
      ),
    );
  }

  /// Section Widget
  Widget _buildCreateAKittyButton(BuildContext context) {
    return CustomElevatedButton(
      onPressed: () {
        Navigator.pushNamed(context, NavRoutes.createkittyScreen);
      },
      width: 150.w,
      height: 40.h,
      text: "Create a Kitty",
      buttonTextStyle: TextStyle(
          fontSize: 12.h, fontWeight: FontWeight.bold, color: Colors.white),
      leftIcon: Container(
        margin: EdgeInsets.only(right: 8.w),
        child: CustomImageView(
          imagePath: AssetUrl.imgPlus,
          height: 18.h,
          width: 18.w,
        ),
      ),
    );
  }

  /// Section Widget
  Widget _buildRow(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(right: 2.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Padding(
            padding: EdgeInsets.only(
              top: 5.h,
              bottom: 1.h,
            ),
            child: Text(
              "My Kitties",
              style: theme.textTheme.titleLarge,
              // ignore: deprecated_member_use
              textScaleFactor: 0.8,
            ),
          ),
          _buildCreateAKittyButton(context),
        ],
      ),
    );
  }
}
