import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/config.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/helpers/extensions/text_styles.dart';
// import 'package:onekitty/screens/dashboard/pages/home/<USER>/tabs/chatgpt.dart';
import 'package:onekitty/screens/dashboard/pages/home/<USER>/tabs/gemini.dart';

class ChatRoom extends StatefulWidget {
  const ChatRoom({super.key});

  @override
  State<ChatRoom> createState() => _ChatRoomState();
}

class _ChatRoomState extends State<ChatRoom> with TickerProviderStateMixin {
  late TabController tabController;
  ConfigController configController = Get.put(ConfigController());
  @override
  void initState() {
    tabController = TabController(initialIndex: 0, length: 2, vsync: this);
    //configController.getConfig();
    // final String apiKey = configController.gpt.value.toString();
    // openAI = OpenAI.instance.build(
    //     token: apiKey,
    //     baseOption:
    //         HttpSetup(receiveTimeout: const Duration(milliseconds: 6000)));
    // print("THE APIKEY IS: $apiKey");
    super.initState();
  }

  TextEditingController chatController = TextEditingController();
  // final List<ChatMessage> _messages = [];

  // late OpenAI? openAI;

  // @override
  // void dispose() {
  //   openAI?.close();
  //   openAI?.genImgClose();
  //   //_subscription?.cancel();
  //   super.dispose();
  // }

  // void _sendMessage() async {
  //   ChatMessage message = ChatMessage(text: chatController.text, sender: "Me");

  //   setState(() {
  //     _messages.insert(0, message);
  //   });

  //   chatController.clear();

  //   final request = CompleteText(
  //       prompt: message.text, model: kChatGptTurbo0301Model, maxTokens: 4000);

  //   final response = await openAI!.onCompletion(request: request);

  //   void insertNewData(String response) {
  //     ChatMessage botMessage =
  //         ChatMessage(text: response.toString(), sender: "Bot");

  //     setState(() {
  //       _messages.insert(0, botMessage);
  //     });
  //   }

  //   insertNewData(response!.choices[0].text);
  // }

  @override
  Widget build(BuildContext context) {
    //final screenSize = MediaQuery.of(context).size;
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          // title: Text(
          //   "Build with Gemini",
          //   style: context.titleLarge?.copyWith(fontWeight: FontWeight.bold, color: AppColors.slate),
          // )
          //     .animate(
          //       onComplete: (controller) => controller.repeat(),
          //     )
          //     .shimmer(
          //       duration: const Duration(milliseconds: 2000),
          //       delay: const Duration(milliseconds: 1000),
          //     ),
          bottom: TabBar(
              controller: tabController,
              physics: const ClampingScrollPhysics(),
              padding: const EdgeInsets.only(left: 5, right: 5),
              unselectedLabelColor:
                  Theme.of(context).cardColor.withOpacity(0.5),
              labelColor: AppColors.slate,
              indicatorSize: TabBarIndicatorSize.tab,
              dividerColor: Colors.transparent,
              labelStyle: context.titleText,
              unselectedLabelStyle: context.dividerTextSmall,
              indicatorColor: AppColors.slate,
              // indicator: BoxDecoration(
              //         color: AppColors.slate,
              //         border: Border(top: BorderSide.none, right: BorderSide.none, left: BorderSide.none)
              //       ),
              tabs: const [
                Tab(
                    child: Text(
                  "Gemini",
                )),
                Tab(
                    child: Text(
                  "OpenAI",
                )),
              ]),
        ),
        body: Container(
          margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          // decoration: BoxDecoration(
          //   image: DecorationImage(image: AssetImage( AssetUrl.logo4), fit: BoxFit.cover)
          // ),
          child: TabBarView(controller: tabController, children: [
            const ChatScreen(),
            // const ChatGptPage(),
          ]),
        ),
      ),
    );
  }

  // Widget geminiTab() {
  //   return ListView.builder(
  //     reverse: true,
  //     itemCount: _messages.length,
  //     itemBuilder: (context, index) {
  //       return _messages[index];
  //     },
  //   );
  // }
}

class ChatMessage extends StatelessWidget {
  const ChatMessage({super.key, required this.text, required this.sender});

  final String text;
  final String sender;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          margin: const EdgeInsets.only(right: 16),
          child: CircleAvatar(child: Text(sender[0])),
        ),
        Expanded(
            child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              sender,
              style: Theme.of(context).textTheme.titleLarge!.copyWith(
                  color: Colors.blue.shade900,
                  fontSize: 16,
                  fontWeight: FontWeight.bold),
            ),
            Container(
              margin: const EdgeInsets.only(top: 5),
              child: Padding(
                padding: const EdgeInsets.only(bottom: 20),
                child: Text(text),
              ),
            )
          ],
        ))
      ],
    );
  }
}

class GeminiTab extends StatefulWidget {
  const GeminiTab({super.key});

  @override
  State<GeminiTab> createState() => _GeminiTabState();
}

class _GeminiTabState extends State<GeminiTab> {
  final List<ChatMessage> _messages = [];

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      reverse: true,
      itemCount: _messages.length,
      itemBuilder: (context, index) {
        return _messages[index];
      },
    );
  }
}
