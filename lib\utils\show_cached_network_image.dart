import 'package:fast_cached_network_image/fast_cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:onekitty/utils/asset_urls.dart';
import 'package:shimmer/shimmer.dart';

// Cache for image dimensions to avoid recalculating layout
final Map<String, Size> _imageSizeCache = <String, Size>{};

class ShowCachedNetworkImage extends StatefulWidget {
  const ShowCachedNetworkImage({
    super.key,
    required this.imageUrl,
    this.height,
    this.width,
    this.borderRadius,
    this.errorWidget,
    this.fit,
    this.enableReload = false,
    this.useShimmerLoading = true,
    this.useAdvancedLayout = false,
  });

  final String imageUrl;
  final double? height;
  final double? width;
  final BorderRadius? borderRadius;
  final Widget? errorWidget;
  final BoxFit? fit;
  final bool enableReload;
  final bool useShimmerLoading;
  final bool useAdvancedLayout;

  @override
  _ShowCachedNetworkImageState createState() => _ShowCachedNetworkImageState();
}

class _ShowCachedNetworkImageState extends State<ShowCachedNetworkImage>
    with AutomaticKeepAliveClientMixin {

  // Image reload functionality
  late String currentImageUrl;
  String? _lastWidgetImageUrl;

  // Advanced layout computation
  Size? _cachedImageSize;
  double? _cachedWidth;
  double? _cachedHeight;
  BoxFit? _cachedFit;
  bool _layoutComputed = false;

  @override
  bool get wantKeepAlive => widget.useAdvancedLayout;

  @override
  void initState() {
    super.initState();
    currentImageUrl = widget.imageUrl;
    _lastWidgetImageUrl = widget.imageUrl;

    if (widget.useAdvancedLayout) {
      _loadCachedImageSize();
    }
  }

  @override
  void didUpdateWidget(ShowCachedNetworkImage oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Check if the imageUrl prop has changed
    if (oldWidget.imageUrl != widget.imageUrl) {
      setState(() {
        // Use the new URL directly without cache busting to preserve caching
        currentImageUrl = widget.imageUrl;
        _lastWidgetImageUrl = widget.imageUrl;

        // Reset layout computation for advanced layout
        if (widget.useAdvancedLayout) {
          _layoutComputed = false;
          _loadCachedImageSize();
        }
      });
    }
  }

  void reloadImage() {
    if (!widget.enableReload) return;

    setState(() {
      // Only add timestamp when explicitly reloading (user action)
      currentImageUrl =
          '${widget.imageUrl}?reload=${DateTime.now().millisecondsSinceEpoch}';
    });
  }

  void _loadCachedImageSize() {
    // Check if we have cached size for this image
    if (_imageSizeCache.containsKey(widget.imageUrl)) {
      _cachedImageSize = _imageSizeCache[widget.imageUrl];
      _layoutComputed = false; // Will recompute layout with cached size
    }
  }

  void _computeLayout(BoxConstraints constraints) {
    if (_layoutComputed || !widget.useAdvancedLayout) return;

    double height = widget.height ?? 120;
    double width = widget.width ?? 200;
    BoxFit fit = widget.fit ?? BoxFit.contain;

    if (_cachedImageSize != null) {
      final double aspectRatio = _cachedImageSize!.width / _cachedImageSize!.height;
      if (aspectRatio > 1) {
        // Landscape
        width = widget.width ?? constraints.maxWidth;
        height = widget.height ?? (width / aspectRatio);
        fit = BoxFit.fitWidth;
      } else {
        // Portrait
        height = widget.height ?? (constraints.maxHeight.isFinite
            ? constraints.maxHeight
            : 300); // Default height if constraint is infinite
        width = widget.width ?? (height * aspectRatio);
        fit = BoxFit.fitHeight;
      }
    } else {
      // If image info is not available, use provided or default sizes
      width = widget.width ?? (constraints.maxWidth.isFinite ? constraints.maxWidth : 200);
      height = widget.height ?? (constraints.maxHeight.isFinite
              ? constraints.maxHeight
              : 120); // Default height if constraint is infinite
    }

    // Ensure height and width are finite and within constraints
    if (constraints.maxHeight.isFinite) {
      height = height.clamp(0.0, constraints.maxHeight);
    }
    if (constraints.maxWidth.isFinite) {
      width = width.clamp(0.0, constraints.maxWidth);
    }

    _cachedWidth = width;
    _cachedHeight = height;
    _cachedFit = fit;
    _layoutComputed = true;
  }

  @override
  Widget build(BuildContext context) {
    if (widget.useAdvancedLayout) {
      super.build(context); // Required for AutomaticKeepAliveClientMixin
    }

    // URL validation
    if (currentImageUrl.isEmpty || !Uri.parse(currentImageUrl).isAbsolute) {
      return widget.errorWidget ?? _buildInvalidUrlWidget();
    }

    if (widget.useAdvancedLayout) {
      return LayoutBuilder(
        builder: (context, constraints) {
          _computeLayout(constraints);
          return _buildImageWidget();
        },
      );
    } else {
      return _buildImageWidget();
    }
  }

  Widget _buildImageWidget() {
    return ClipRRect(
      borderRadius: widget.borderRadius ?? BorderRadius.circular(4.5),
      child: SizedBox(
        width: widget.useAdvancedLayout ? _cachedWidth : widget.width,
        height: widget.useAdvancedLayout ? _cachedHeight : widget.height,
        child: FastCachedImage(
          url: currentImageUrl,
          fit: widget.useAdvancedLayout
              ? (_cachedFit ?? widget.fit ?? BoxFit.cover)
              : (widget.fit ?? BoxFit.cover),
          fadeInDuration: const Duration(milliseconds: 250),
          errorBuilder: (context, url, error) {
            return widget.errorWidget ?? _buildErrorWidget();
          },
          loadingBuilder: (context, url) {
            return widget.useShimmerLoading
                ? _buildShimmerLoadingWidget()
                : _buildCircularLoadingWidget();
          },
        ),
      ),
    );
  }

  Widget _buildInvalidUrlWidget() {
    return Container(
      width: widget.width ?? 200,
      height: widget.height ?? 120,
      color: Colors.grey,
      child: const Center(
        child: Text(
          'Invalid image URL',
          style: TextStyle(color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Stack(
      alignment: Alignment.center,
      children: [
        Image.network(
          height: widget.height ?? 200.h,
          width: widget.width ?? 390.w,
          AssetUrl.onekittyBannnerUrl,
          fit: BoxFit.cover,
        ),
        if (widget.enableReload)
          Positioned(
            top: 8,
            right: 8,
            child: GestureDetector(
              onTap: reloadImage,
              child: const Tooltip(
                triggerMode: TooltipTriggerMode.tap,
                message: 'Tap to reload image',
                child: Icon(
                  Icons.refresh,
                  color: Colors.white,
                  size: 20,
                ),
              ),
            ),
          ),
        const Positioned(
          top: 8,
          left: 8,
          child: Tooltip(
            triggerMode: TooltipTriggerMode.tap,
            message: 'Error loading image',
            child: Icon(
              Icons.error,
              color: Colors.red,
              size: 16,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildShimmerLoadingWidget() {
    final borderRadius = widget.borderRadius ?? BorderRadius.circular(4.5);

    return Container(
      width: widget.width ?? 200,
      height: widget.height ?? 120,
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: borderRadius,
      ),
      child: Shimmer.fromColors(
        baseColor: Colors.grey[300]!,
        highlightColor: Colors.grey[100]!,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: borderRadius,
          ),
        ),
      ),
    );
  }

  Widget _buildCircularLoadingWidget() {
    return Container(
      width: widget.width ?? 200,
      height: widget.height ?? 120,
      color: Colors.grey.shade100,
      child: Center(
        child: SizedBox(
          width: 24,
          height: 24,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              Colors.grey.shade400,
            ),
          ),
        ),
      ),
    );
  }
}
